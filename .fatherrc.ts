import { defineConfig } from 'father';

export default defineConfig({
  esm: {
    input: 'src',
    output: 'es',
    platform: 'browser',
    transformer: 'babel',
  },
  cjs: {
    input: 'src',
    output: 'lib',
    platform: 'browser',
    transformer: 'babel',
  },
  prebundle: {
    deps: {
      'antd': true
    }
  },
  extraBabelPlugins: [
    ['babel-plugin-import', { 
      libraryName: 'antd', 
      libraryDirectory: 'es',
      style: true 
    }]
  ]
}); 