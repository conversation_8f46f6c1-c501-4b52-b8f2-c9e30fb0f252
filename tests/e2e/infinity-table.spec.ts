// 导入Playwright测试框架
import { test } from '@playwright/test';

// InfinityTable组件的端到端测试套件
test.describe('InfinityTable', () => {
  // 每个测试用例执行前的通用配置和钩子函数
  test.beforeEach(async ({ page }) => {
    // 设置测试超时时间为60秒
    test.setTimeout(60000);

    // 监听并打印页面console日志
    page.on('console', msg => console.log(msg.text()));

    // 监听并打印页面JavaScript错误
    page.on('pageerror', err => console.error(err));
  });

  // 每个测试用例执行后的清理工作
  test.afterEach(async ({ page }, testInfo) => {
    // 如果测试未通过
    if (testInfo.status !== 'passed') {
      // 打印当前页面DOM结构,用于调试
      console.log('Current DOM:', await page.content());
    }
  });
}); 