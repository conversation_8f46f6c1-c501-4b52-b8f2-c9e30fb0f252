import { test, expect } from '@playwright/test';
import { enterEditMode, saveByBlur } from '../utils/table-helpers';

test.describe('薪资范围-数字范围编辑器', () => {
  test.beforeEach(async ({ page }) => {
    // 使用专门的测试页面
    await page.goto('/tests/input-range');
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('.infinity-table-root');
  });

  test('应该能正常编辑薪资范围', async ({ page }) => {
    // 进入编辑模式 - 第一行的薪资范围列(索引0)
    const { popup } = await enterEditMode(page, 0, 0);
    
    // 获取原始值用于比较
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').first()
      .locator('.infinity-table-display-cell')
      .first();
    const originalValue = await cell.textContent();

    // 找到最小值和最大值输入框
    const minInput = popup.locator('.infinity-table-number-range-input').first().locator('input');
    const maxInput = popup.locator('.infinity-table-number-range-input').last().locator('input');
    await expect(minInput).toBeVisible();
    await expect(maxInput).toBeVisible();

    // 输入新的范围值
    await minInput.clear();
    await minInput.fill('6000');
    await maxInput.clear();
    await maxInput.fill('12000');

    // 点击保存按钮
    await popup.locator('.micro-btn-primary').click();
    
    // 验证显示的新值
    await expect(cell).toContainText('6000');
    await expect(cell).toContainText('12000');
    await expect(cell).toContainText('元'); // 验证单位显示
  });

  test('应该验证输入范围限制', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    // 获取原始值
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').first()
      .locator('.infinity-table-display-cell')
      .first();

    const minInput = popup.locator('.infinity-table-number-range-input').first().locator('input');
    const maxInput = popup.locator('.infinity-table-number-range-input').last().locator('input');

    // 测试负数 - 应该变成0
    await minInput.clear();
    await minInput.fill('-1000');
    await popup.locator('.micro-btn-primary').click();
    await expect(cell).toContainText('0');

    // 测试超过最大值 - 应该变成100000
    await enterEditMode(page, 0, 0);
    await maxInput.clear();
    await maxInput.fill('200000');
    await popup.locator('.micro-btn-primary').click();
    await expect(cell).toContainText('100000');
  });

  test('应该验证最大值大于最小值', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    // 获取原始值
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').first()
      .locator('.infinity-table-display-cell')
      .first();
    const originalValue = await cell.textContent();

    // 输入最大值小于最小值的情况
    const minInput = popup.locator('.infinity-table-number-range-input').first().locator('input');
    const maxInput = popup.locator('.infinity-table-number-range-input').last().locator('input');
    
    await minInput.clear();
    await minInput.fill('10000');
    await maxInput.clear();
    await maxInput.fill('5000');

    // 点击保存按钮
    await popup.locator('.micro-btn-primary').click();
    
    // 验证值未改变
    await expect(cell).toContainText(originalValue || '');
  });

  test('应该正确显示placeholder', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    const minInput = popup.locator('.infinity-table-number-range-input').first().locator('input');
    const maxInput = popup.locator('.infinity-table-number-range-input').last().locator('input');

    // 验证placeholder
    await expect(minInput).toHaveAttribute('placeholder', '最小值');
    await expect(maxInput).toHaveAttribute('placeholder', '最大值');
  });

  test('应该支持键盘操作', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    const minInput = popup.locator('.infinity-table-number-range-input').first().locator('input');
    
    // 输入一个基础值
    await minInput.clear();
    await minInput.fill('5000');
    
    // 使用上下键调整值
    await minInput.press('ArrowUp');
    await expect(minInput).toHaveValue('5001');
    
    await minInput.press('ArrowDown');
    await minInput.press('ArrowDown');
    await expect(minInput).toHaveValue('4999');
  });

  test('不应该允许非数字输入', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    // 获取原始值
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').first()
      .locator('.infinity-table-display-cell')
      .first();
    const originalValue = await cell.textContent();

    // 尝试输入非数字
    const minInput = popup.locator('.infinity-table-number-range-input').first().locator('input');
    await minInput.type('abc');
    
    // 点击保存按钮
    await popup.locator('.micro-btn-primary').click();
    
    // 验证值未改变
    await expect(cell).toContainText(originalValue || '');
  });

  test('应该支持取消编辑', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    // 获取原始值
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').first()
      .locator('.infinity-table-display-cell')
      .first();
    const originalValue = await cell.textContent();

    // 修改值
    const minInput = popup.locator('.infinity-table-number-range-input').first().locator('input');
    const maxInput = popup.locator('.infinity-table-number-range-input').last().locator('input');
    
    await minInput.clear();
    await minInput.fill('8000');
    await maxInput.clear();
    await maxInput.fill('15000');

    // 点击取消按钮
    await popup.locator('.micro-btn-default').click();
    
    // 验证值未改变
    await expect(cell).toContainText(originalValue || '');
  });

  test('最大值小于最小值时应该显示提示且保持编辑状态', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    // 获取原始值
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').first()
      .locator('.infinity-table-display-cell')
      .first();
    const originalValue = await cell.textContent();

    // 输入最大值小于最小值的情况
    const minInput = popup.locator('.infinity-table-number-range-input').first().locator('input');
    const maxInput = popup.locator('.infinity-table-number-range-input').last().locator('input');
    
    await minInput.clear();
    await minInput.fill('10000');
    await maxInput.clear();
    await maxInput.fill('5000');

    // 点击保存按钮
    await popup.locator('.micro-btn-primary').click();
    
    // 验证提示信息出现
    await expect(
      page.locator('.ant-message-custom-content.ant-message-error')
    ).toContainText('最小值不能大于最大值');

    // 验证编辑弹窗依然可见
    await expect(popup).toBeVisible();
    
    // 验证输入框的值保持不变
    await expect(minInput).toHaveValue('10000');
    await expect(maxInput).toHaveValue('5000');

    // 验证单元格的值未改变
    await expect(cell).toContainText(originalValue || '');

    // 点击取消按钮关闭弹窗
    await popup.locator('.micro-btn-default').click();
    
    // 验证弹窗已关闭
    await expect(popup).not.toBeVisible();
    
    // 验证单元格值恢复到原始值
    await expect(cell).toContainText(originalValue || '');
  });
});
