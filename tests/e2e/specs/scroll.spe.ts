import { test, expect } from '@playwright/test';
import { enterEditMode } from '../utils/table-helpers';

// 辅助函数：获取元素位置信息
async function getPositions(page: any) {
  const positions = await page.evaluate(() => {
    const popup = document.querySelector('.infinity-table-editor-popup');
    const table = document.querySelector('.infinity-table-wrapper');
    
    if (!popup || !table) return null;

    const popupRect = popup.getBoundingClientRect();
    const tableRect = table.getBoundingClientRect();

    return {
      popup: {
        left: popupRect.left,
        right: popupRect.right,
        top: popupRect.top,
        bottom: popupRect.bottom,
        width: popupRect.width,
        height: popupRect.height
      },
      table: {
        left: tableRect.left,
        right: tableRect.right,
        top: tableRect.top,
        bottom: tableRect.bottom,
        width: tableRect.width,
        height: tableRect.height
      }
    };
  });

  expect(positions).not.toBeNull();
  return positions!;
}

test.describe('滚动编辑', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/tests/scroll');
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('.infinity-table-root');
  });

  test.describe('overlay模式编辑器', () => {
    test('应该正确处理左边贴边', async ({ page }) => {
      // 滚动到中间位置
      const tableBody = page.locator('.micro-table-body');
      await tableBody.evaluate(element => {
        element.scrollLeft = element.scrollWidth / 2;
      });
      await page.waitForTimeout(300);

      // 编辑左侧固定列
      const { popup } = await enterEditMode(page, 1, 0);
      
      // 等待浮层完全可见和渲染
      await expect(popup).toBeVisible();
      await expect(popup.locator('.search-dropdown-content')).toBeVisible();
      await page.waitForTimeout(100);

      const positions = await getPositions(page);
      
      // 验证左对齐
      const isSnappedToLeft = Math.abs(positions.popup.left - positions.table.left) < 2;
      expect(isSnappedToLeft).toBe(true,
        `浮层应该贴左边界: 浮层左边缘=${positions.popup.left}, 表格左边缘=${positions.table.left}`
      );
    });

    test('应该正确处理右边贴边', async ({ page }) => {
      // 滚动到最右侧
      const tableBody = page.locator('.micro-table-body');
      await tableBody.evaluate(element => {
        element.scrollLeft = element.scrollWidth;
      });
      await page.waitForTimeout(300);

      // 编辑最右侧列
      const { popup } = await enterEditMode(page, 1, 29);
      await expect(popup).toBeVisible();

      // 验证右对齐
      const positions = await getPositions(page);
      expect(positions.popup.right).toBeLessThanOrEqual(positions.table.right);

      const isSnappedToRight = Math.abs(positions.popup.right - positions.table.right) < 2;
      expect(isSnappedToRight).toBe(true,
        `浮层应该贴右边界: 浮层右边缘=${positions.popup.right}, 表格右边缘=${positions.table.right}`
      );
    });

    test('应该正确处理顶部贴边', async ({ page }) => {
      // 滚动到中间位置
      const tableBody = page.locator('.micro-table-body');
      await tableBody.evaluate(element => {
        element.scrollTop = element.scrollHeight / 2;
      });
      await page.waitForTimeout(300);

      // 编辑可见区域第一行
      const { popup } = await enterEditMode(page, 15, 1);
      await expect(popup).toBeVisible();

      // 验证顶部对齐
      const positions = await getPositions(page);
      expect(positions.popup.top).toBeGreaterThanOrEqual(positions.table.top);
    });

    test('应该正确处理底部贴边', async ({ page }) => {
      // 滚动到底部
      const tableBody = page.locator('.micro-table-body');
      await tableBody.evaluate(element => {
        element.scrollTop = element.scrollHeight;
      });
      await page.waitForTimeout(300);

      // 编辑最后一行
      const { popup } = await enterEditMode(page, 29, 1);
      await expect(popup).toBeVisible();

      // 验证底部对齐
      const positions = await getPositions(page);
      expect(positions.popup.bottom).toBeLessThanOrEqual(positions.table.bottom);

      const isSnappedToBottom = Math.abs(positions.popup.bottom - positions.table.bottom) < 2;
      expect(isSnappedToBottom).toBe(true,
        `浮层应该贴底部: 浮层底边=${positions.popup.bottom}, 表格底边=${positions.table.bottom}`
      );
    });
  });

  test.describe('below模式编辑器', () => {
    test('应该正确处理左边贴边', async ({ page }) => {
      // 滚动到中间位置
      const tableBody = page.locator('.micro-table-body');
      await tableBody.evaluate(element => {
        element.scrollLeft = element.scrollWidth / 2;
      });
      await page.waitForTimeout(300);

      // 编辑日期列
      const { popup } = await enterEditMode(page, 1, 1);
      await expect(popup).toBeVisible();

      // 验证左对齐
      const positions = await getPositions(page);
      expect(positions.popup.left).toBeGreaterThanOrEqual(positions.table.left);
    });

    test('应该正确处理右边贴边', async ({ page }) => {
      // 滚动到最右侧
      const tableBody = page.locator('.micro-table-body');
      await tableBody.evaluate(element => {
        element.scrollLeft = element.scrollWidth;
      });
      await page.waitForTimeout(300);

      // 编辑最右侧非固定列
      const { popup } = await enterEditMode(page, 1, 28);
      await expect(popup).toBeVisible();

      // 验证右对齐
      const positions = await getPositions(page);
      expect(positions.popup.right).toBeLessThanOrEqual(positions.table.right);
    });

    test('应该正确处理底部贴边', async ({ page }) => {
      // 滚动到底部
      const tableBody = page.locator('.micro-table-body');
      await tableBody.evaluate(element => {
        element.scrollTop = element.scrollHeight;
      });
      await page.waitForTimeout(300);

      // 编辑最后一行
      const { popup } = await enterEditMode(page, 29, 1);
      await expect(popup).toBeVisible();

      // 验证底部对齐
      const positions = await getPositions(page);
      expect(positions.popup.bottom).toBeLessThanOrEqual(positions.table.bottom);
    });
  });

  test('应该在滚动时保持编辑浮层位置正确', async ({ page }) => {
    // 开始编辑中间位置的单元格
    const { popup } = await enterEditMode(page, 15, 15);
    await expect(popup).toBeVisible();
    await page.waitForTimeout(100);
    
    // 记录初始位置
    const initialPositions = await getPositions(page);
    
    // 水平滚动
    const tableBody = page.locator('.micro-table-body');
    await tableBody.evaluate(element => {
      element.scrollLeft += 100;
    });
    await page.waitForTimeout(300);
    
    // 验证水平位置更新
    const afterHScrollPositions = await getPositions(page);
    expect(Math.abs(afterHScrollPositions.popup.left - (initialPositions.popup.left - 100))).toBeLessThan(2);
    
    // 垂直滚动
    await tableBody.evaluate(element => {
      element.scrollTop += 100;
    });
    await page.waitForTimeout(300);
    
    // 验证垂直位置更新
    const afterVScrollPositions = await getPositions(page);
    expect(Math.abs(afterVScrollPositions.popup.top - (afterHScrollPositions.popup.top - 100))).toBeLessThan(2);
  });
}); 