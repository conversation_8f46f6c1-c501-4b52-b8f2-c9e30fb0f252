import { test, expect } from '@playwright/test';
import { enterEditMode, saveByBlur } from '../utils/table-helpers';

test.describe('供应商-搜索选择编辑器', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/tests/search');
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('.infinity-table-root');
  });

  test('应该能正常编辑供应商', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').first()
      .locator('.infinity-table-display-cell')
      .first();
    const originalValue = await cell.textContent();

    const searchInput = popup.locator('.micro-input');
    await expect(searchInput).toHaveAttribute('placeholder', '搜索供应商');

    await searchInput.fill('杭州');

    const firstOption = popup.locator('.search-dropdown-content .option-item').nth(1);
    await firstOption.click();

    await saveByBlur(page);

    await expect(page.locator('.ant-message-notice')).toContainText('保存成功');

    const newValue = await cell.textContent();
    expect(newValue).not.toBe(originalValue);
  });

  test('应该正确过滤搜索结果', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    const searchInput = popup.locator('.micro-input');
    
    await searchInput.fill('南京');

    const options = popup.locator('.search-dropdown-content .option-item');
    await expect(options).toHaveCount(1);
    await expect(options.first()).toContainText('南京蜂产品基地');

    await searchInput.fill('不存在的供应商');

    await expect(popup.locator('.search-dropdown-content .empty-text'))
      .toContainText('无匹配数据');
  });

  test('应该支持取消编辑供应商', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').first()
      .locator('.infinity-table-display-cell')
      .first();
    const originalValue = await cell.textContent();

    await saveByBlur(page);
    
    await expect(cell).toContainText(originalValue || '');
  });

  test('应该支持键盘操作', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    const searchInput = popup.locator('.micro-input');
    
    await searchInput.press('ArrowDown');
    await searchInput.press('ArrowDown');
    await searchInput.press('ArrowDown');

    
    await expect(
      popup.locator('.search-dropdown-content .option-item').nth(2)
    ).toHaveClass(/active/);
    
    await searchInput.press('Enter');
    
    await saveByBlur(page);
    
    await expect(page.locator('.ant-message')).toContainText('保存成功');
  });

  test('应该正确显示选中状态', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    await expect(
      popup.locator('.search-dropdown-content .option-item.selected')
    ).toBeVisible();
  });
});
