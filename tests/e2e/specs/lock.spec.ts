import { test, expect } from '@playwright/test';
import { enterEditMode,saveByBlur } from '../utils/table-helpers';

test.describe('编辑锁定和权限控制', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/tests/lock');
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('.infinity-table-root');
  });

  test('应该禁止编辑无权限的字段', async ({ page }) => {
    // 尝试编辑年龄字段（无权限）
    const ageCell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').nth(1);
    
    await ageCell.dblclick();
    
    // 验证编辑弹窗没有出现
    await expect(page.locator('.infinity-table-editor-popup')).not.toBeVisible();

    // 验证tooltip提示
    await expect(page.locator('.infinity-table-tooltip')).toContainText('编辑');
  });

  test('应该禁止编辑被锁定的行', async ({ page }) => {
    // 尝试编辑第一行（被锁定）的姓名字段
    const nameCell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').first();
    
    await nameCell.dblclick();
    
    // 验证编辑弹窗没有出现
    await expect(page.locator('.infinity-table-editor-popup')).not.toBeVisible();

    // 验证tooltip提示
    await expect(page.locator('.infinity-table-tooltip')).toContainText('正在编辑');
  });

  test('应该能编辑未锁定行的有权限字段', async ({ page }) => {
    // 编辑第二行（未锁定）的姓名字段
    const { popup } = await enterEditMode(page, 1, 2);
    
    // 验证编辑弹窗出现
    await expect(popup).toBeVisible();
    
    // 验证输入框可以输入
    const input = popup.locator('.micro-input');
    await input.fill('新名字');
    
    // 验证可以保存
    await input.press('Enter');
    await saveByBlur(page);
    await expect(page.locator('.ant-message-notice')).toContainText('保存成功');
  });

  test('应该正确显示所有字段的编辑状态', async ({ page }) => {
    const firstRow = page.locator('.micro-table-row').first();

    // 验证姓名字段（有权限但行被锁定）
    await firstRow.locator('.infinity-table-cell').first().hover();
    await expect(page.locator('.infinity-table-tooltip')).toContainText('正在编辑');

    // 验证年龄字段（无权限）
    await firstRow.locator('.infinity-table-cell').nth(1).hover();
    await expect(page.locator('.infinity-table-tooltip')).toContainText('编辑');

    // 验证地址字段（有权限但行被锁定）
    await firstRow.locator('.infinity-table-cell').nth(2).hover();
    await expect(page.locator('.infinity-table-tooltip')).toContainText('正在编辑');
  });

  test('应该正确处理多人同时编辑的提示', async ({ page }) => {
    // 验证第一行被锁定的提示
    await page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').first().hover();
    await expect(page.locator('.infinity-table-tooltip')).toContainText('正在编辑');

    // 验证第三行被锁定的提示
    await page.locator('.micro-table-row').nth(2)
      .locator('.infinity-table-cell').first().hover();
    await expect(page.locator('.infinity-table-tooltip')).toContainText('正在编辑');
  });

  test('应该禁止编辑被禁用的列', async ({ page }) => {
    // 尝试编辑姓名列（整列禁用）
    const nameCell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').first();
    
    // 验证禁用和锁定样式
    // const displayCell = nameCell.locator('.infinity-table-display-cell');
    // await expect(displayCell).toHaveClass(/disabled-cell-content/);
    // await expect(displayCell).toHaveClass(/locked-cell-content/);

    // // 验证文本内容
    // const textSpan = innerDisplayCell.locator('.infinity-table-text-display');
    // await expect(textSpan).toHaveClass(/ellipsis/);
    // await expect(textSpan).toHaveAttribute('title', '张三');
    // await expect(textSpan).toHaveText('张三');

    await nameCell.dblclick();
    
    // 验证编辑弹窗没有出现
    await expect(page.locator('.infinity-table-editor-popup')).not.toBeVisible();
  });

  test('应该正确区分不同的禁用状态', async ({ page }) => {
    const firstRow = page.locator('.micro-table-row').first();

    // 验证第二行姓名列（整列禁用 - 无提示）
    const secondRow = page.locator('.micro-table-row').nth(1);
    const nameCell = secondRow.locator('.infinity-table-cell').first();
    await expect(nameCell)
      .toHaveClass(/disabled-cell-content/);
    await nameCell.hover();
    await expect(page.locator('.infinity-table-tooltip')).not.toBeVisible();

    // 验证年龄列（无权限 - 有提示）
    const ageCell = firstRow.locator('.infinity-table-cell').nth(1);
    await ageCell.hover();
    await expect(page.locator('.infinity-table-tooltip'))
      .toContainText('编辑');

    // 验证被锁定状态（有提示）
    const addressCell = firstRow.locator('.infinity-table-cell').nth(2);
    await addressCell.hover();
    await expect(page.locator('.infinity-table-tooltip'))
      .toContainText('正在编辑');
  });

  test('应该按正确的优先级显示禁用状态', async ({ page }) => {
    const firstRow = page.locator('.micro-table-row').first();

    // 测试优先级1: 无权限 > 正在编辑
    // 年龄字段: 既无权限又在被编辑
    const ageCell = firstRow.locator('.infinity-table-cell').nth(1);
    await ageCell.hover();
    await expect(page.locator('.infinity-table-tooltip'))
      .toContainText('编辑');  // 应该显示无权限提示

    // 测试优先级2: 正在编辑 > 禁用无提示
    // 姓名字段: 既被禁用又在被编辑
    const nameCell = firstRow.locator('.infinity-table-cell').first();
    await nameCell.hover();
    await expect(page.locator('.infinity-table-tooltip'))
      .toContainText('正在编辑');  // 应该显示编辑锁定提示

    // 测试优先级3: 仅禁用
    // 第二行姓名字段: 仅被禁用
    const secondRowNameCell = page.locator('.micro-table-row').nth(1)
      .locator('.infinity-table-cell').first();
    await expect(secondRowNameCell)
      .toHaveClass(/disabled-cell-content/);
    await secondRowNameCell.hover();
    await expect(page.locator('.infinity-table-tooltip')).not.toBeVisible();  // 不应该有提示
  });

  test('应该正确处理多重禁用状态的编辑尝试', async ({ page }) => {
    const firstRow = page.locator('.micro-table-row').first();

    // 尝试编辑多重禁用的字段（无权限+被编辑）
    const ageCell = firstRow.locator('.infinity-table-cell').nth(1);
    await ageCell.dblclick();
    
    // 验证编辑弹窗没有出现
    await expect(page.locator('.infinity-table-editor-popup')).not.toBeVisible();
    
    // 验证显示优先级最高的提示
    await expect(page.locator('.infinity-table-tooltip'))
      .toContainText('编辑');

    // 尝试编辑仅禁用的字段
    const secondRowNameCell = page.locator('.micro-table-row').nth(1)
      .locator('.infinity-table-cell').first();
    await secondRowNameCell.dblclick();
    
    // 验证编辑弹窗没有出现
    await expect(page.locator('.infinity-table-editor-popup')).not.toBeVisible();
    
    // 验证没有提示
    await expect(page.locator('.infinity-table-tooltip')).not.toBeVisible();
  });
}); 