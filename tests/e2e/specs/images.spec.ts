import { test, expect } from '@playwright/test';
import { enterEditMode, saveByBlur } from '../utils/table-helpers';

test.describe('图片上传编辑器', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/tests/images');
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('.infinity-table-root');
  });

  test('应该能正常显示已上传的图片', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    // 验证图片预览
    const previewImage = popup.locator('.micro-upload-list-item-image');
    await expect(previewImage).toBeVisible();
    await expect(previewImage).toHaveAttribute('src', /111ailwindui.png/);

    // 验证图片操作按钮
    await expect(popup.locator('.anticon-eye')).toBeVisible();
    await expect(popup.locator('.anticon-delete')).toBeVisible();
  });

  test('应该能正常上传新图片', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    // 获取原始图片数量
    const originalCount = await popup.locator('.micro-upload-list-item-image').count();

    // 模拟文件上传
    const fileInput = popup.locator('input[type="file"]');
    await fileInput.setInputFiles({
      name: 'test.png',
      mimeType: 'image/png',
      buffer: Buffer.from('fake image content'),
    });
    // await page.waitForTimeout(300);
        await page.waitForTimeout(1500);
    await saveByBlur(page);


    // 验证保存成功提示
    await expect(page.locator('.ant-message-notice')).toContainText('保存成功');

    // 验证单元格中的图片数量
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').first()
      .locator('.infinity-table-display-cell')
      .first();
    
    const images = cell.locator('img');
    await expect(images).toHaveCount(originalCount + 1);
  });

  test('应该能删除已上传的图片', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    // 点击删除按钮
    await popup.locator('.anticon-delete').first().click();

    // 验证图片已删除
    await expect(popup.locator('.micro-upload-list-item-image')).toHaveCount(0);

    await saveByBlur(page);

    // 验证保存成功
    await expect(page.locator('.ant-message-notice')).toContainText('保存成功');
  });

  test('应该显示上传按钮和限制信息', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    // 验证上传按钮
    const uploadButton = popup.locator('.upload-button');
    await expect(uploadButton).toBeVisible();
    await expect(uploadButton.locator('.upload-text')).toContainText('上传');

    // 验证文件输入框属性
    const fileInput = popup.locator('input[type="file"]');
    await expect(fileInput).toHaveAttribute('accept', 'image/*');
    await expect(fileInput).toHaveAttribute('multiple', '');
  });

//   test('应该支持图片预览', async ({ page }) => {
//     const { popup } = await enterEditMode(page, 0, 0);
    
//     // 点击预览按钮
//     await popup.locator('.anticon-eye').first().click();

//     // 验证预���图片显示
//     await expect(page.locator('.micro-image-preview-img')).toBeVisible();
//     await expect(page.locator('.micro-image-preview-img')).toHaveAttribute('src', /111ailwindui.png/);

//     // 关闭预览
//     await page.keyboard.press('Escape');
//   });
}); 