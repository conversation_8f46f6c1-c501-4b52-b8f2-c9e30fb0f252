import { test, expect } from '@playwright/test';
import { enterEditMode, saveByBlur } from '../utils/table-helpers';
import { generateTestData } from '../utils/test-data';

test.describe('年龄-数字编辑器', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/guide/demo');
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('.infinity-table-root');
  });

  test('应该能正常编辑年龄单元格', async ({ page }) => {
    // 进入编辑模式 - 第一行的年龄列(索引2)
    const { popup } = await enterEditMode(page, 0, 2);
    const editor = popup.locator('.micro-input-number-input');
    
    // 获取原始值用于比较
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').nth(2)
      .locator('.infinity-table-display-cell');
    const originalValue = await cell.textContent();

    // 生成一个随机年龄(20-50岁)
    const newValue = generateTestData.number(20, 50);
    await editor.clear();
    await editor.fill(newValue.toString());

    // 保存并等待保存完成
    await saveByBlur(page);
    // 等待保存API调用完成(1秒)
    await page.waitForTimeout(1100);

    // 验证 - 检查年龄单元格的值
    await expect(cell).toContainText(newValue.toString());
  });

  test('应该验证非法输入并保持原值', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 2);
    const editor = popup.locator('.micro-input-number-input');
    
    // 获取原始值
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').nth(2)
      .locator('.infinity-table-display-cell');
    const originalValue = await cell.textContent();
    
    // 测试非数字输入
    await editor.fill('abc');
    await saveByBlur(page);
    await page.waitForTimeout(1100); // 等待保存尝试完成
    await expect(cell).toContainText(originalValue || ''); // 单元格值应该保持不变

    // 测试负数
    await enterEditMode(page, 0, 2); // 重新进入编辑模式
    await editor.clear();
    await editor.fill('-1');
    await saveByBlur(page);
    await page.waitForTimeout(1100);
    await expect(cell).toContainText(originalValue || '');

    // 测试超大数字
    await enterEditMode(page, 0, 2);
    await editor.clear();
    await editor.fill('999999');
    await saveByBlur(page);
    await page.waitForTimeout(1100);
    await expect(cell).toContainText(originalValue || '');
  });

  test('应该支持键盘上下键调整数值', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 2);
    const editor = popup.locator('.micro-input-number-input');
    
    // 输入初始值
    await editor.clear();
    await editor.fill('25');
    
    // 按上键增加
    await editor.press('ArrowUp');
    await expect(editor).toHaveValue('26');
    
    // 按下键减少
    await editor.press('ArrowDown');
    await editor.press('ArrowDown');
    await expect(editor).toHaveValue('24');
  });

  test('应该能清空数字单元格', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 2);
    const editor = popup.locator('.micro-input-number-input');
    
    // 清空内容
    await editor.clear();
    await saveByBlur(page);

    // 验证单元格为空
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').nth(2)
      .locator('.infinity-table-display-cell');
    await expect(cell).toBeEmpty();
  });

  // test('应该支持小数点输入', async ({ page }) => {
  //   const { popup } = await enterEditMode(page, 0, 2);
  //   const editor = popup.locator('.micro-input-number-input');
    
  //   // 输入小数
  //   await editor.clear();
  //   await editor.fill('25.5');
  //   await saveByBlur(page);

  //   // 验证显示
  //   const cell = page.locator('.micro-table-row').first()
  //     .locator('.infinity-table-cell').nth(2)
  //     .locator('.infinity-table-display-cell');
  //   await expect(cell).toContainText('25.5');
  // });

  test('应该限制小数位数', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 2);
    const editor = popup.locator('.micro-input-number-input');
    
    // 记录原始值
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').nth(2)
      .locator('.infinity-table-display-cell');
    const originalValue = await cell.textContent();
    
    // 输入小数
    await editor.clear();
    await editor.fill('25.5555');
    await saveByBlur(page);

    // 验证值未改变,仍为原始值
    await expect(cell).toContainText(originalValue || '');
  });
}); 