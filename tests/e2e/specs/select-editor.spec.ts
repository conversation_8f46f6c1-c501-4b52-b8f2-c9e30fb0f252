import { test, expect } from '@playwright/test';
import { enterEditMode, saveByBlur, waitForSave } from '../utils/table-helpers';

test.describe('城市-选择编辑器', () => {
  test.beforeEach(async ({ page }) => {
    // 使用专门的测试页面
    await page.goto('/tests/select');
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('.infinity-table-root');
  });

  test('应该能正常选择城市', async ({ page }) => {
    // 进入编辑模式 - 第一行的城市列(索引0)
    const { popup } = await enterEditMode(page, 0, 0);
    
    // 获取原始值用于比较
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').first()
      .locator('.infinity-table-display-cell')
      .first(); // 添加first()以确保只获取第一个元素
    const originalValue = await cell.textContent();

    // 打开下拉框
    const optionsList = popup.locator('.options-list');
    await expect(optionsList).toBeVisible();

    // 选择"上海"选项
    const option = optionsList.locator('.option-item').filter({ hasText: '上海' });
    await option.click();

    // 保存并等待保存完成
    await saveByBlur(page);
    
    // 验证显示的是新选择的城市
    await expect(cell).toContainText('上海');

    // 验证成功提示
    // await expect(page.locator('.micro-message-notice-success')).toBeVisible();
  });

  test('应该显示所有预设的城市选项', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    // 验证所有预设的城市选项都存在
    const optionsList = popup.locator('.options-list');
    const cityOptions = [
      '杭州', '宁波', '上海', '北京', '广州'
    ];

    for (const city of cityOptions) {
      await expect(
        optionsList.locator('.option-item').filter({ hasText: city })
      ).toBeVisible();
    }
  });


  test('应该正确显示当前选中项', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    // 获取当前单元格的值
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').first()
      .locator('.infinity-table-display-cell')
      .first(); // 添加first()以确保只获取第一个元素
    const originalValue = await cell.textContent();
    
    // 验证选项列表中对应的选项有selected类
    const selectedOption = popup.locator('.options-list .option-item.selected');
    await expect(selectedOption).toBeVisible();
    await expect(selectedOption).toContainText(originalValue || '');
  });

  test('不应该允许手动输入', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0);
    
    // 获取原始值
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').first()
      .locator('.infinity-table-display-cell')
      .first(); // 添加first()以确保只获取第一个元素
    const originalValue = await cell.textContent();

    // 尝试手动输入(应该无效)
    await page.keyboard.type('invalid city');
    
    // 保存
    await saveByBlur(page);
    
    // 验证值未改变
    await expect(cell).toContainText(originalValue || '');
  });
});