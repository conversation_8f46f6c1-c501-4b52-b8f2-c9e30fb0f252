import { test, expect } from '@playwright/test';
import { enterEditMode, saveByBlur } from '../utils/table-helpers';
import dayjs from 'dayjs';

test.describe('日期编辑器测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/tests/date');
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('.infinity-table-root');
  });

  test('应该能正常编辑日期', async ({ page }) => {
    // 进入编辑模式
    const { popup } = await enterEditMode(page, 0, 1);
    
    // 获取原始值用于比较
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').nth(1)
      .locator('.infinity-table-display-cell')
      .first();
    const originalValue = await cell.textContent();

    // 选择一个日期
    const dateCell = popup.locator('.micro-picker-cell-in-view').first();
    const selectedDate = await dateCell.getAttribute('title');
    await dateCell.click();

    // 保存更改
    await saveByBlur(page);
    
    // 验证显示的新值
    const dateDisplay = await page.getByTestId('date-display').first();
    await expect(dateDisplay).toContainText(selectedDate || '');
    
    // 验证年龄显示
    const age = dayjs().diff(dayjs(selectedDate), 'years');
    await expect(dateDisplay).toContainText(`(${age}岁)`);
  });

  test('应该支持取消编辑', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 1);
    
    // 获取原始值
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').nth(1)
      .locator('.infinity-table-display-cell')
      .first();
    const originalValue = await cell.textContent();

    // 点击外部取消编辑
    await page.click('body', { position: { x: 0, y: 0 } });
    
    // 验证值未改变
    await expect(cell).toContainText(originalValue || '');
  });

});
