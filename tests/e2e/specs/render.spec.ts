import { test, expect } from '@playwright/test';
import { enterEditMode, saveByBlur } from '../utils/table-helpers';

test.describe('自定义渲染测试', () => {
  test.beforeEach(async ({ page }) => {
    // 访问测试页面
    await page.goto('/tests/render');
  });

  test('应该正确渲染初始数据', async ({ page }) => {
    // 检查表格是否存在
    const table = await page.getByTestId('custom-render-table');
    await expect(table).toBeVisible();

    // 检查第一行数据
    await expect(page.getByText('测试项目 A')).toBeVisible();
    await expect(page.getByText('进行中')).toBeVisible();
    await expect(page.getByTestId('progress-bar').first()).toBeVisible();
    await expect(page.getByTestId('score-display').first()).toBeVisible();
  });

  test('应该支持文本编辑', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 0); // 第一行的名称列

    const input = await popup.getByTestId('name-input');
    await expect(input).toBeVisible();
    await input.clear();
    await input.fill('新项目名称');
    
    await saveByBlur(page);

    await expect(page.getByText('新项目名称')).toBeVisible();
  });

  test('应该支持状态选择', async ({ page }) => {
    // 使用 enterEditMode 进入编辑模式
    const { popup } = await enterEditMode(page, 0, 1); // 第一行的状态列

    // 等待选择器出现并点击
    const select = await popup.getByTestId('status-select');
    await expect(select).toBeVisible();
    await select.click();

    // 等待下拉菜单出现并选择选项
    const dropdown = await page.locator('.micro-select-dropdown-placement-bottomLeft');
    // await expect(dropdown).toBeVisible();
    
    const option = await dropdown.locator('.micro-select-item-option').filter({ hasText: '待处理' });
    await option.click();

    // 使用 saveByBlur 保存更改
    await saveByBlur(page);

    // 验证更新后的状态
    const statusDisplay = await page.getByTestId('status-display').first();
    const tag = await statusDisplay.locator('.micro-tag-orange');
    await expect(tag).toBeVisible();
    await expect(tag).toHaveText('待处理');
  });

  test('应该支持进度编辑', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 2); // 第一行的进度列

    const input = await popup.getByTestId('progress-input');
    await expect(input).toBeVisible();
    await input.clear();
    await input.fill('50');
    
    await saveByBlur(page);

    const progressBar = await page.getByTestId('progress-bar').first();
    await expect(progressBar.locator('.micro-progress-bg')).toHaveAttribute('style', 'width: 50%; height: 6px;');
    await expect(progressBar.locator('.micro-progress-text')).toHaveText('50%');
  });

  test('应该支持评分编辑', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 3); // 第一行的评分列

    const input = await popup.getByTestId('score-input');
    await expect(input).toBeVisible();
    await input.clear();
    await input.fill('4.8');
    
    await saveByBlur(page);

    const scoreDisplay = await page.getByTestId('score-display').first();
    await expect(scoreDisplay).toContainText('4.8');
    await expect(scoreDisplay).toHaveCSS('color', 'rgb(82, 196, 26)');
  });

  test('应该正确处理无效输入', async ({ page }) => {
    // 测试评分超出范围
    await page.getByTestId('score-display').first().dblclick();
    const scoreInput = await page.getByTestId('score-input');
    await scoreInput.clear();
    await scoreInput.fill('6');  // 超出最大值5
    await scoreInput.press('Enter');
    // 验证值被限制在有效范围内
        await saveByBlur(page);

    const scoreDisplay = await page.getByTestId('score-display').first();
    await expect(scoreDisplay).not.toContainText('6');
    // 测试进度超出范围
    await page.getByTestId('progress-bar').first().dblclick();
    const progressInput = await page.getByTestId('progress-input');
    await progressInput.clear();
    await progressInput.fill('101');  // 超出最大值100
    await progressInput.press('Enter');
    // 验证值被限制在有效范围内
        await saveByBlur(page);

    const progressBar = await page.getByTestId('progress-bar').first();
    await expect(progressBar).not.toHaveAttribute('percent', '101');
  });

});
