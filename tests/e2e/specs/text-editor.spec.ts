import { test, expect } from '@playwright/test';
import { enterEditMode, saveByBlur } from '../utils/table-helpers';
import { generateTestData } from '../utils/test-data';

test.describe('文本编辑器', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/guide/demo');
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('.infinity-table-root');
  });

  test('应该能正常编辑文本单元格', async ({ page }) => {
    // 进入编辑模式
    const { popup } = await enterEditMode(page, 0, 1); // 第一行花名列

    // 找到编辑器并输入
    const editor = popup.locator('.infinity-table-text-editor');
    await expect(editor).toBeVisible();
    
    const newValue = generateTestData.text('花名');
    await editor.clear();
    await editor.fill(newValue);

    // 保存
    await saveByBlur(page);

    // 验证
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').nth(1)
      .locator('.infinity-table-display-cell');
    await expect(cell).toContainText(newValue);
  });

  test('应该能清空文本单元格', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 1);
    const editor = popup.locator('.infinity-table-text-editor');
    
    // 清空内容
    await editor.clear();
    await saveByBlur(page);

    // 验证单元格为空
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').nth(1)
      .locator('.infinity-table-display-cell');
    await expect(cell).toBeEmpty();
  });

  // test('按ESC键应该取消编辑', async ({ page }) => {
  //   const { popup } = await enterEditMode(page, 0, 1);
  //   const editor = popup.locator('.infinity-table-text-editor');
    
  //   // 记录原始值
  //   const cell = page.locator('.micro-table-row').first()
  //     .locator('.infinity-table-cell').nth(1)
  //     .locator('.infinity-table-display-cell');
  //   const originalValue = await cell.textContent();

  //   // 输入新值
  //   const newValue = generateTestData.text('新花名');
  //   await editor.clear();
  //   await editor.fill(newValue);

  //   // 按ESC取消
  //   await editor.press('Escape');

  //   // 验证值未改变
  //   await expect(cell).toContainText(originalValue || '');
  // });

  test('输入超长文本应该被截断', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 1);
    const editor = popup.locator('.infinity-table-text-editor');
    
    // 输入超长文本
    const longText = 'a'.repeat(1000);
    await editor.clear();
    await editor.fill(longText);
    await saveByBlur(page);

    // 验证显示的文本被截断
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').nth(1)
      .locator('.infinity-table-display-cell');
    const displayedText = await cell.textContent();
    expect(displayedText?.length).toBeLessThan(longText.length);
  });

  test('应该支持特殊字符输入', async ({ page }) => {
    const { popup } = await enterEditMode(page, 0, 1);
    const editor = popup.locator('.infinity-table-text-editor');
    
    // 输入特殊字符
    const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?/~`';
    await editor.clear();
    await editor.fill(specialChars);
    await saveByBlur(page);

    // 验证特殊字符正确显示
    const cell = page.locator('.micro-table-row').first()
      .locator('.infinity-table-cell').nth(1)
      .locator('.infinity-table-display-cell');
    await expect(cell).toContainText(specialChars);
  });
}); 