import { Page, expect } from '@playwright/test';

export async function enterEditMode(page: Page, rowIndex: number, columnIndex: number) {
  const cell = page.locator('.micro-table-row')
    .nth(rowIndex)
    .locator('.infinity-table-cell')
    .nth(columnIndex)
    .locator('.infinity-table-display-cell');
    
  await expect(cell).toBeVisible();
  await cell.dblclick();
  
  const popup = page.locator('.infinity-table-editor-popup');
  await expect(popup).toBeVisible();
  
  return { cell, popup };
}

export async function saveByBlur(page: Page) {
  await page.click('.infinity-table-root', { position: { x: 0, y: 0 } });
  const popup = page.locator('.infinity-table-editor-popup');
  await expect(popup).toBeHidden();
}

export async function waitForSave(page: Page) {
  // 等待保存API调用完成(比1000ms多等100ms以确保稳定)
  await page.waitForTimeout(1100);
}
  