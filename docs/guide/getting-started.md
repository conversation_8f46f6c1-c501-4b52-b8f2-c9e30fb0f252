---
title: 配置文档
order: 1
nav:
  title: 指南
  order: 1
---

# 配置文档

## 组件接入

使用 npm 或 yarn 安装:

```bash
# 使用 npm
npm install @xc/infinity-table

# 使用 yarn
yarn add @xc/infinity-table
```
<br/>

> **💡 提示:** 文档站点可能存在缓存，可以使用 Command+Shift+R (Windows/Linux 使用 Ctrl+Shift+R) 进行强制刷新。


<br/><br/><br/>

## 内置组件


### 文本组件

文本组件是最基础的编辑组件。

<code src="../components/infinity-table/demo/basic/text.tsx"></code>

### 日期组件

日期组件是最基础的编辑组件。
<code src="../components/infinity-table/demo/basic/date.tsx"></code>

### 数字组件

数字组件是最基础的编辑组件。
<code src="../components/infinity-table/demo/basic/number.tsx"></code>

### 数字区间组件

数字区间组件是最基础的编辑组件。
<code src="../components/infinity-table/demo/basic/number-range.tsx"></code>

### 图片组件

- 支持图片上传基本功能, 可基于提供的images类型底层能力和扩展API自定义实现
- **自定义弹出式编辑组件，目前使用业务侧封装的上传组件接入, 请访问业务侧例子**
- 支持自定义删除联动功能

<code src="../components/infinity-table/demo/basic/images.tsx"></code>

### 单选组件

支持自定义overlayProps, 用于扩展删除数据联动功能
<code src="../components/infinity-table/demo/basic/select.tsx"></code>

### 模糊查询单选

<code src="../components/infinity-table/demo/basic/search.tsx"></code>

### 多选组件

- 支持多选，建议数量不是太多的场景使用。
- 支持自定义overlayProps, 用于扩展删除数据联动功能

<code src="../components/infinity-table/demo/basic/multi-select.tsx"></code>


## 编辑校验

### 基础校验
<code src="../components/infinity-table/demo/validator/text1.tsx"></code>

### 自定义校验
<code src="../components/infinity-table/demo/validator/text2.tsx"></code>


## 高级使用

### 自定义渲染

<code src="../components/infinity-table/demo/pro/custom-render.tsx"></code>

### 自定义编辑+antd原生渲染

<code src="../components/infinity-table/demo/pro/custom-editor.tsx"></code>

### 自定义编辑+自定义渲染(实时同步)

<code src="../components/infinity-table/demo/pro/custom-editor2.tsx"></code>

### 自定义编辑组件:支持Portal
<code src="../components/infinity-table/demo/pro/custom-components.tsx"></code>




## 权限功能

### 没有编辑权限
<code src="../components/infinity-table/demo/control/disabled-columns.tsx"></code>

### 正在编辑
<code src="../components/infinity-table/demo/control/editingLock.tsx"></code>

### 正在编辑和解绑
<code src="../components/infinity-table/demo/control/editingLock2.tsx"></code>

### 正在编辑和没有权限一起
<code src="../components/infinity-table/demo/control/lock-permission.tsx"></code>

### 单独列的禁用

- 权限、他人编辑场景之外:支持columns指定单独列的禁用

<code src="../components/infinity-table/demo/control/columns-disabled.tsx"></code>

### 前置判断是否能编辑
<code src="../components/infinity-table/demo/control/check.tsx"></code>

## 全局配置

### 列管理器:自定义显示列

- 自动读取columns的配置title生成选项。支持string和ReactNode
- 支持自定义列管理器

<code src="../components/infinity-table/demo/global/column-manager.tsx"></code>

### Antd自定义前缀

<code src="../components/infinity-table/demo/global/custom-prefix-antd.tsx"></code>

### debug调试模式

<code src="../components/infinity-table/demo/global/debug.tsx"></code>

### 全局编辑完成回调

<code src="../components/infinity-table/demo/global/onEditComplete.tsx"></code>



## API

### InfinityTable

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| columns | 表格列配置 | `ColumnType<T>[]` | - |
| dataSource | 数据数组 | `T[]` | - |
| rowKey | 行数据的唯一标识字段 | `string` | `'id'` |
| width | 表格宽度 | `string \| number` | `'100%'` |
| height | 表格高度 | `number` | - |
| bordered | 是否显示边框 | `boolean` | `false` |
| debug | 调试配置 | `{ enabled: boolean }` | `{ enabled: false }` |
| editingLock | 编辑锁定配置 | `EditingLockConfig` | - |
| disableControl | 禁用控制配置 | `DisableControlConfig` | - |
| columnManager | 列管理配置 | `ColumnManagerConfig` | - |
| onEditStateChange | 编辑状态变更回调 | `(info: EditStateChangeInfo) => void` | - |

### ColumnType

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| title | 列标题 | `string` | - |
| dataIndex | 列数据字段名 | `string` | - |
| key | 列唯一标识 | `string` | - |
| width | 列宽度 | `number` | - |
| fixed | 列固定方向 | `'left' \| 'right'` | - |
| editor | 编辑器配置 | `EditorConfig` | - |
| show | 是否显示该列 | `boolean` | `true` |

### EditorConfig

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| type | 编辑器类型 | `'text' \| 'number' \| 'date' \| 'select' \| 'multi-select' \| 'number-range' \| 'images' \| 'search-select'` | - |
| props | 编辑器属性 | `object` | - |
| overlayProps | 浮层属性 | `{ showClear?: boolean; onClear?: Function }` | - |
| validators | 验证规则 | `ValidatorConfig` | - |
| onEditComplete | 编辑完成回调 | `(value: CellValue, record: T) => Promise<boolean>` | - |
| beforeEdit | 编辑前检查 | `(record: T) => Promise<boolean>` | - |
| editorRender | 自定义编辑器渲染函数 | `(props: EditorProps) => ReactNode` | - |
| displayRender | 自定义显示渲染函数 | `(props: DisplayProps) => ReactNode` | - |

### EditingLockConfig

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| editingRows | 被锁定的行 | `string[] \| { id: string; message?: string }[]` | - |
| tooltip | 提示配置 | `{ show?: boolean; message?: string }` | - |

### DisableControlConfig 

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| rules | 禁用规则 | `(string \| { field: string; match: (record: T) => boolean })[]` | - |
| tooltip | 提示配置 | `{ show?: boolean; message?: string }` | - |

