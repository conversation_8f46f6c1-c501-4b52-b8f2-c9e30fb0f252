/**
 * height: 800
 */
import React from 'react';
import { message } from 'antd';
import { InfinityTable } from '@xc/infinity-table';
import { ConfigProvider } from 'antd';


interface TableRecord extends Record<string, unknown> {
  id: string;
  salary: {
    min: number;
    max: number;
  };
  remark: string;
}

// 生成测试数据
const generateData = (count: number): TableRecord[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: String(index + 1),
    salary: {
      min: Math.floor(Math.random() * 5000) + 5000,
      max: Math.floor(Math.random() * 10000) + 10000,
    },
    remark: `这是一段测试备注信息-${index + 1}`,
  }));
};

export default () => {
  const [tableData, setTableData] = React.useState(generateData(30));

  // 处理保存
  const handleSave = async (
    value: CellValue,
    record: TableRecord,
    dataIndex: string
  ): Promise<boolean> => {
    try {
      // 更新本地数据
      setTableData(prevData =>
        prevData.map(item =>
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );

      message.success('保存成功');
      return true;
    } catch (err: unknown) {
      console.error('保存失败:', err);
      message.error('保存失败');
      return false;
    }
  };

  const columns = [
    {
      title: '薪资范围',
      dataIndex: 'salary',
      key: 'salary',
      editor: {
        type: 'number-range',
        props: {
          placeholder: ['最小值', '最大值'],
          min: 0,
          max: 100000,
          unit: '元',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'salary'),
      },
      width: 200,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 200,
      editor: {
        type: 'text',
        props: {
          placeholder: '请输入备注信息',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'remark'),
      }
    }
  ];

  return (
    <div>
      <h3>数字范围编辑器示例：</h3>
      <ul>
        <li>双击单元格进入编辑状态</li>
        <li>编辑完成后自动保存</li>
      </ul>
      <ConfigProvider prefixCls="micro">

      <InfinityTable
        columns={columns}
        dataSource={tableData}
        prefixCls="micro"
        height={900}
        debug={{ enabled: true }}
        rowKey="id"
        width="100%"
      />
      </ConfigProvider>
    </div>
  );
};
