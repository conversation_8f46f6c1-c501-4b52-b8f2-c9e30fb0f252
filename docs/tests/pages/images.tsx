/**
 * height: 800
 */
import React from 'react';
import { message } from 'antd';
import { InfinityTable } from '@xc/infinity-table';
import type { CellValue } from '../../../../src/components/InfinityTable/types';
import { ConfigProvider } from 'antd';

interface TableRecord extends Record<string, unknown> {
  id: string;
  photos: string[];
  remark: string;
}

// 模拟API请求
const mockApi = {
  uploadImage: async (file: File) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      success: true,
      url: 'https://s.xinc818.com/files/webcim3zonla0auicrk/111ailwindui.png'
    };
  }
};

// 生成测试数据
const generateData = (count: number): TableRecord[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: String(index + 1),
    photos: [
      'https://s.xinc818.com/files/webcim3zonla0auicrk/111ailwindui.png',
    ],
    remark: `这是一段测试备注信息-${index + 1}`
  }));
};

export default () => {
  const [tableData, setTableData] = React.useState(generateData(30));

  // 处理保存
  const handleSave = async (
    value: CellValue,
    record: TableRecord,
    dataIndex: string
  ): Promise<boolean> => {
    try {
      setTableData(prevData =>
        prevData.map(item =>
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );

      message.success('保存成功');
      return true;
    } catch (err: unknown) {
      console.error('保存失败:', err);
      message.error('保存失败');
      return false;
    }
  };

  const columns = [
    {
      title: '图片',
      dataIndex: 'photos',
      key: 'photos',
      editor: {
        type: 'images',
        props: {
          maxCount: 3,
          accept: 'image/*',
          maxSize: 5 * 1024 * 1024,
          customRequest: async ({ file, onSuccess, onError }) => {
            try {
              const response = await mockApi.uploadImage(file);
              if (response.success) {
                onSuccess?.(response);
              } else {
                onError?.(new Error('上传失败'));
              }
            } catch (error) {
              onError?.(error as Error);
            }
          }
        },
        overlayProps: {
          showClear: true,
          onClear: ({ record, onChange }) => {
            console.log('清除图片:', record);
            onChange([]);
          }
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'photos'),
      },
      width: 200,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 200,
      editor: {
        type: 'text',
        props: {
          placeholder: '请输入备注信息',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'remark'),
      }
    }
  ];

  return (
    <div>
      <h3>图片上传编辑器示例：</h3>
      <ul>
        <li>双击单元格进入编辑状态</li>
        <li>支持图片预览、删除和上传</li>
        <li>限制上传大小和类型</li>
      </ul>
      <ConfigProvider prefixCls="micro">
        <InfinityTable
          columns={columns}
          prefixCls='micro'
          dataSource={tableData}
          height={900}
          debug={{ enabled: true }}
          rowKey="id"
          width="100%"
        />
      </ConfigProvider>
    </div>
  );
}; 