/**
 * height: 800
 */
import React from 'react';
import { message } from 'antd';
import { InfinityTable } from '@xc/infinity-table';
import { ConfigProvider } from 'antd';

// 添加 SearchParams 类型定义
interface SearchParams {
  keyword: string;
  field: string;
  record?: Record<string, unknown>;
}

// 模拟API请求
const mockApi = {
  searchSuppliers: async (params: SearchParams) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const allSuppliers = [
      { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场' },
      { value: 'hangzhou_honey', label: '杭州蜂蜜专业户' },
      { value: 'nanjing_base', label: '南京蜂产品基地' },
      { value: 'anhui_coop', label: '安徽蜂蜜合作社' },
      { value: 'shandong_corp', label: '山东蜂产品公司' }
    ].filter(item => 
      item.label.toLowerCase().includes(params.keyword.toLowerCase())
    );
    
    return allSuppliers;
  }
};

interface TableRecord extends Record<string, unknown> {
  id: string;
  supplier: { value: string; label: string };
  remark: string;
}

// 生成测试数据
const generateData = (count: number): TableRecord[] => {
  const supplierValues = [
    { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场' },
    { value: 'hangzhou_honey', label: '杭州蜂蜜专业户' },
    { value: 'nanjing_base', label: '南京蜂产品基地' },
    { value: 'anhui_coop', label: '安徽蜂蜜合作社' },
    { value: 'shandong_corp', label: '山东蜂产品公司' }
  ];
  
  return Array.from({ length: count }, (_, index) => ({
    id: String(index + 1),
    supplier: supplierValues[index % supplierValues.length],
    remark: `这是一段测试备注信息-${index + 1}`
  }));
};

export default () => {
  const [tableData, setTableData] = React.useState(generateData(10));

  // 处理保存
  const handleSave = async (
    value: CellValue,
    record: TableRecord,
    dataIndex: string
  ): Promise<boolean> => {
    try {
      setTableData(prevData =>
        prevData.map(item =>
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );

      message.success('保存成功');
      return true;
    } catch (err: unknown) {
      console.error('保存失败:', err);
      message.error('保存失败');
      return false;
    }
  };

  const columns = [
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier',
      width: 150,
      editor: {
        type: 'search-select',
        props: {
          placeholder: '搜索供应商',
          onSearch: mockApi.searchSuppliers,
          initialOptions: [
            { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场' },
            { value: 'hangzhou_honey', label: '杭州蜂蜜专业户' },
            { value: 'nanjing_base', label: '南京蜂产品基地' },
            { value: 'anhui_coop', label: '安徽蜂蜜合作社' },
            { value: 'shandong_corp', label: '山东蜂产品公司' }
          ]
        },
        overlayProps: {
            showClear: true,
            onClear: ({ onChange }) => {
              onChange([]);
            }
          },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'supplier')
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
    }
  ];

  return (
    <div>
      <h3>搜索选择和文本编辑器示例：</h3>
      <ul>
        <li>双击单元格进入编辑状态</li>
        <li>编辑完成��自动保存</li>
      </ul>
      <ConfigProvider prefixCls="micro">
        <InfinityTable
          columns={columns}
          prefixCls='micro'
          dataSource={tableData}
          height={800}
          debug={{ enabled: true }}
          rowKey="id"
          scroll={{ y: 800 }}
        />
      </ConfigProvider>
    </div>
  );
};
