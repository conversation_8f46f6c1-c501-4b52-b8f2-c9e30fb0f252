import React from 'react';
import { InfinityTable } from '@xc/infinity-table';
import { Tag, Progress, Select } from 'antd';
import { ConfigProvider } from 'antd';


// 定义状态的复杂数据结构
interface StatusValue {
  code: string;
  label: string;
  extra?: {
    color: string;
    description?: string;
  };
}

interface TableRecord extends Record<string, unknown> {
  id: string;
  name: string;
  status: string;
  statusExtra?: StatusValue;
  progress: number;
  score: number;
}

// 修改测试数据生成
const generateData = (count: number): TableRecord[] => {
  return Array.from({ length: count }, (_, index) => {
    const id = (index + 1).toString();
    const statuses = [
      {
        code: 'active',
        label: '进行中',
        extra: { color: 'green', description: '正常进行中' }
      },
      {
        code: 'pending',
        label: '待处理',
        extra: { color: 'orange', description: '等待处理中' }
      },
      {
        code: 'completed',
        label: '已完成',
        extra: { color: 'blue', description: '已完成项目' }
      }
    ];
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
    
    return {
      id,
      name: `测试项目 ${String.fromCharCode(65 + (index % 26))}${Math.floor(index / 26) > 0 ? Math.floor(index / 26) : ''}`,
      status: randomStatus.code,
      statusExtra: randomStatus,
      progress: Math.floor(Math.random() * 100),
      score: Number((Math.random() * 2 + 3).toFixed(1)), // 生成 3.0-5.0 之间的随机分数
    };
  });
};

// 使用生成的 20 行数据
const data: TableRecord[] = generateData(3);

// 自定义状态选择器组件
const StatusEditor: React.FC<{
  value?: string;
  onChange?: (value: string) => void;
}> = ({ value, onChange }) => {
  const handleChange = (code: string) => {
    onChange?.(code);
  };

  return (
    <Select
      value={value}
      onChange={handleChange}
      style={{ width: '100%' }}
      data-testid="status-select"
    >
      <Select.Option value="active">进行中</Select.Option>
      <Select.Option value="pending">待处理</Select.Option>
      <Select.Option value="completed">已完成</Select.Option>
    </Select>
  );
};

export default () => {
  const [tableData, setTableData] = React.useState(data);

  const handleSave = async (
    value: CellValue,
    record: TableRecord,
    dataIndex: string
  ): Promise<boolean> => {
    try {
      setTableData(prevData => 
        prevData.map(item => 
          item.id === record.id ? {
            ...item,
            [dataIndex]: value
          } : item
        )
      );
      return true;
    } catch (err: unknown) {
      console.error('保存失败:', err);
      return false;
    }
  };

  const columns = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      editor: {
        type: 'text',
        props: {
          maxLength: 20,
          'data-testid': 'name-input',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => 
          handleSave(value, record, 'name'),
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 150,
      editor: {
        type: 'custom',
        editorRender: StatusEditor,
        // popupProps: {
        //   width: 'cell',
        //   height:50,
        // },
        onEditComplete: (value: string, record: TableRecord) => 
          handleSave(value, record, 'status'),
        displayRender: ({ value }) => {
          console.log('value~~~', value);
          const statusMap: Record<string, { color: string; label: string; description?: string }> = {
            active: { 
              color: 'green', 
              label: '进行中',
              description: '正常进行中'
            },
            pending: { 
              color: 'orange', 
              label: '待处理',
              description: '等待处理中'
            },
            completed: { 
              color: 'blue', 
              label: '已完成',
              description: '已完成项目'
            }
          };
          
          const status = statusMap[value as string];
          
          if (!status) {
            return value;
          }
          
          return (
            <div style={{ padding: '0 12px' }} data-testid="status-display">
              <Tag color={status.color}>
                {status.label}
              </Tag>
              {/* {status.description && (
                <span style={{ fontSize: 12, marginLeft: 8 }}>
                  {status.description}
                </span>
              )} */}
            </div>
          );
        },
      }
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      editor: {
        type: 'number',
        props: {
          min: 0,
          max: 100,
          precision: 0,
          'data-testid': 'progress-input',
        },
        displayRender: ({ value }) => {            
          return (
            <Progress 
              style={{ width: '100%', padding: '0 12px' }}
              percent={value as number} 
              size="small" 
              status={value === 100 ? 'success' : 'active'}
              data-testid="progress-bar"
            />
          );
        },
        onEditComplete: (value: CellValue, record: TableRecord) => 
          handleSave(value, record, 'progress'),
      },
    },
    {
      title: '评分',
      dataIndex: 'score',
      key: 'score',
      width: 100,
      editor: {
        type: 'number',
        props: {
          min: 0,
          max: 5,
          precision: 1,
          step: 0.1,
          'data-testid': 'score-input',
        },
        displayRender: ({ value }) => {
          const score = value as number;
          const color = score >= 4.5 ? '#52c41a' : 
                       score >= 4.0 ? '#1890ff' : 
                       score >= 3.5 ? '#faad14' : '#ff4d4f';
                         
          return (
            <div 
              style={{ 
                color,
                padding: '0 12px',
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center',
                gap: 4
              }}
              data-testid="score-display"
            >
              {score}
              <span style={{ fontSize: 12 }}>/5.0</span>
            </div>
          );
        },
        onEditComplete: (value: CellValue, record: TableRecord) => 
          handleSave(value, record, 'score'),
      },
    },
  ];

  return (
    <div>
      <h3>自定义渲染测试用例：</h3>
      <ul>
        <li>测试复杂数据结构的编和展示</li>
        <li>测试自定义编辑器组件</li>
        <li>测试 displayRender 展示效果</li>
        <li>测试编辑功能和数据更新</li>
      </ul>
      <ConfigProvider prefixCls="micro">
        <div style={{ height: '1000px' }}>
          <InfinityTable<TableRecord>
            columns={columns}
            dataSource={tableData}
            rowKey="id"
            prefixCls="micro"
            bordered
            data-testid="custom-render-table"
            // scroll={{ y: 1000 }}
          />
        </div>
      </ConfigProvider>
    </div>
  );
};