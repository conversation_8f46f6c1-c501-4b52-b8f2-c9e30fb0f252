/**
 * height: 800
 */
import React from 'react';
import { message } from 'antd';
import { InfinityTable } from '@xc/infinity-table';
import type { CellValue } from '../../../../src/components/InfinityTable/types';
import { ConfigProvider } from 'antd';
import dayjs from 'dayjs';

interface TableRecord extends Record<string, unknown> {
  id: string;
  name: string;
  birthday: string;
  remark: string;
}

// 生成测试数据
const generateData = (count: number): TableRecord[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: String(index + 1),
    name: `用户${index + 1}`,
    birthday: dayjs()
      .subtract(Math.floor(Math.random() * 30) + 20, 'years')
      .format('YYYY-MM-DD'),
    remark: `这是一段测试备注信息-${index + 1}`
  }));
};

export default () => {
  const [tableData, setTableData] = React.useState(generateData(30));

  // 处理保存
  const handleSave = async (
    value: CellValue,
    record: TableRecord,
    dataIndex: string
  ): Promise<boolean> => {
    try {
      setTableData(prevData =>
        prevData.map(item =>
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );

      message.success('保存成功');
      return true;
    } catch (err: unknown) {
      console.error('保存失败:', err);
      message.error('保存失败');
      return false;
    }
  };

  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      editor: {
        type: 'text',
        props: {
          maxLength: 20,
          'data-testid': 'name-input',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => 
          handleSave(value, record, 'name'),
      }
    },
    {
      title: '生日',
      dataIndex: 'birthday',
      key: 'birthday',
      width: 150,
      editor: {
        type: 'date',
        props: {
          format: 'YYYY-MM-DD',
          'data-testid': 'date-picker',
          disabledDate: (current) => {
            // 禁用未来日期
            return current && current > dayjs().endOf('day');
          }
        },
        displayRender: ({ value }) => {
          const date = dayjs(value as string);
          const age = dayjs().diff(date, 'years');
          
          return (
            <div style={{ padding: '0 12px' }} data-testid="date-display">
              <span>{date.format('YYYY-MM-DD')}</span>
              <span style={{ 
                marginLeft: 8, 
                fontSize: 12, 
                color: '#666' 
              }}>
                ({age}岁)
              </span>
            </div>
          );
        },
        onEditComplete: (value: CellValue, record: TableRecord) => 
          handleSave(value, record, 'birthday'),
      }
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 200,
      editor: {
        type: 'text',
        props: {
          placeholder: '请输入备注信息',
          'data-testid': 'remark-input',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => 
          handleSave(value, record, 'remark'),
      }
    }
  ];

  return (
    <div>
      <h3>日期编辑器测试用例：</h3>
      <ul>
        <li>支持日期选择和输入</li>
        <li>禁用未来日期</li>
        <li>显示年龄计算</li>
        <li>双击单元格进入编辑状态</li>
      </ul>
      <ConfigProvider prefixCls="micro">
        <InfinityTable
          columns={columns}
          dataSource={tableData}
          prefixCls="micro"
          height={900}
          debug={{ enabled: true }}
          rowKey="id"
          width="100%"
          data-testid="date-table"
        />
      </ConfigProvider>
    </div>
  );
};
