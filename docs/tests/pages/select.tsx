/**
 * height: 800
 */
import React from 'react';
import { message, Switch, Space, Button } from 'antd';
import { InfinityTable } from '@xc/infinity-table';
import dayjs from 'dayjs';
import { ConfigProvider } from 'antd';

// 城市选项
const cityOptions = [
  { value: 'hz', label: '杭州'},
  { value: 'nb', label: '宁波'},
  { value: 'sh', label: '上海'},
  { value: 'bj', label: '北京'},
  { value: 'gz', label: '广州'},
];

interface TableRecord extends Record<string, unknown> {
  id: string;
  city: string;
}

// 生成测试数据
const generateData = (count: number): TableRecord[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: String(index + 1),
    city: cityOptions[Math.floor(Math.random() * cityOptions.length)].value,
  }));
};

export default () => {
  const [tableData, setTableData] = React.useState(generateData(30));
  const [forceSuccess, setForceSuccess] = React.useState(true);

  // 处理保存
  const handleSave = async (
    value: CellValue,
    record: TableRecord,
    dataIndex: string
  ): Promise<boolean> => {
    try {
      setTableData(prevData =>
        prevData.map(item =>
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );

      message.success('保存成功');
      return true;
    } catch (err: unknown) {
      console.error('保存失败:', err);
      message.error('保存失败');
      return false;
    }
  };

  const columns = [
    {
      title: '所在城市',
      dataIndex: 'city',
      key: 'city',
      editor: {
        type: 'select',
        props: {
          options: cityOptions,
          placeholder: '请选择城市',
        },
        overlayProps: {
          showClear: true,
          onClear: ({ onChange }) => {
            onChange([]);
          }
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'city'),
      },
      width: 80,
    },
    {
      title: '其他',
      dataIndex: 'options',
      key: 'options',
    }
  ];

  return (
    <div>
      <h3>城市选择示例：</h3>
      <Space direction="vertical" size="middle" style={{ marginBottom: 16, width: '100%' }}>
        <ul>
          <li>双击单元格进入编辑状态</li>
          <li>编辑完成后自动保存</li>
        </ul>
      </Space>
      <ConfigProvider prefixCls="micro">

      <InfinityTable
        columns={columns}
        dataSource={tableData}
        height={900}
        prefixCls="micro"
        debug={{ enabled: true }}
        rowKey="id"
          width="100%"
        />
      </ConfigProvider>
    </div>
  );
};
