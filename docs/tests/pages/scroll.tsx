/**
 * height: 800
 */
import React from 'react';
import { message } from 'antd';
import { InfinityTable } from '@xc/infinity-table';
import { ConfigProvider } from 'antd';
import { Input, Button } from 'antd';

interface TableRecord extends Record<string, unknown> {
  id: string;
  supplier: { value: string; label: string };
  supplier2: { value: string; label: string };
  [key: string]: string | number | { value: string; label: string };  // 动态列
}

// 模拟API请求
const mockApi = {
  searchSuppliers: async (params: { keyword: string }) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const allSuppliers = [
      { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场' },
      { value: 'hangzhou_honey', label: '杭州蜂蜜专业户' },
      { value: 'nanjing_base', label: '南京蜂产品基地' },
      { value: 'anhui_coop', label: '安徽蜂蜜合作社' },
      { value: 'shandong_corp', label: '山东蜂产品公司' }
    ].filter(item => 
      item.label.toLowerCase().includes(params.keyword.toLowerCase())
    );
    
    return allSuppliers;
  }
};

// 生成列配置
const generateColumns = (columnCount: number) => {
  const supplierColumn = {
    title: '供应商',
    dataIndex: 'supplier',
    key: 'supplier',
    width: 200,
    // fixed: 'left' as const,
    editor: {
      type: 'search-select',
      props: {
        placeholder: '搜索供应商',
        onSearch: mockApi.searchSuppliers,
        initialOptions: [
          { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场' },
          { value: 'hangzhou_honey', label: '杭州蜂蜜专业户' },
          { value: 'nanjing_base', label: '南京蜂产品基地' },
          { value: 'anhui_coop', label: '安徽蜂蜜合作社' },
          { value: 'shandong_corp', label: '山东蜂产品公司' }
        ]
      },
      onEditComplete: (value: any, record: TableRecord) => handleSave(value, record, 'supplier')
    }
  };

  // 添加一个自定义编辑器列
  const customEditorColumn = {
    title: '自定义编辑器',
    dataIndex: 'customEditor',
    key: 'customEditor',
    width: 200,
    editor: {
      type: 'custom',
      editorRender: ({ value, onChange, onSave, onCancel }) => {
        return (
          <div style={{ padding: '8px 8px',backgroundColor: '#fff' }}>
            <Input.TextArea
              value={value}
              onChange={e => onChange(e.target.value)}
              onPressEnter={() => onSave()}
              onBlur={() => onSave()}
              autoFocus
            />
            <div style={{ marginTop: 8 }}>
              <Button size="small" onClick={() => onSave()}>确定</Button>
              <Button size="small" onClick={() => onCancel()} style={{ marginLeft: 8 }}>取消</Button>
            </div>
          </div>
        );
      },
      popupProps: {
        width: 300,
        height: 120,
      },
      overlayProps: {
        showClear: true,
        onClear: ({ onChange }) => {
          onChange(''); // 对于文本框，清空为空字符串
        }
      },
      onEditComplete: (value: any, record: TableRecord) => 
        handleSave(value, record, 'customEditor')
    }
  };

  // 生成前面的普通列
  const normalColumns = Array.from({ length: columnCount - 4 }, (_, columnIndex) => ({
    title: `列${columnIndex + 1}`,
    dataIndex: `col${columnIndex + 1}`,
    key: `col${columnIndex + 1}`,
    width: 150,
    editor: {
      type: 'text',
      props: {
        placeholder: `请输入列${columnIndex + 1}`,
      },
    //   ...(columnIndex === 0 ? {
    //     popupProps: {
    //       width: 300,  // 指定宽度
    //       height: 120, // 指定高度
    //     }
    //   } : {}),
      onEditComplete: (value: any, record: TableRecord) => 
        handleSave(value, record, `col${columnIndex + 1}`),
    }
  }));

  // 供应商2列（倒数第三列）
  const supplier2Column = {
    title: '供应商2',
    dataIndex: 'supplier2',
    key: 'supplier2',
    width: 200,
    editor: {
      type: 'search-select',
      props: {
        placeholder: '搜索供应商2',
        onSearch: mockApi.searchSuppliers,
        initialOptions: [
          { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场' },
          { value: 'hangzhou_honey', label: '杭州蜂蜜专业户' },
          { value: 'nanjing_base', label: '南京蜂产品基地' },
          { value: 'anhui_coop', label: '安徽蜂蜜合作社' },
          { value: 'shandong_corp', label: '山东蜂产品公司' }
        ]
      },
      onEditComplete: (value: any, record: TableRecord) => handleSave(value, record, 'supplier2')
    }
  };

  // 供应商2后面的自定义编辑器列
  const customEditorColumn2 = {
    title: '自定义编辑器2',
    dataIndex: 'customEditor2',
    key: 'customEditor2',
    width: 200,
    editor: {
      type: 'custom',
      editorRender: ({ value, onChange, onSave, onCancel }) => {
        return (
          <div style={{ padding: '8px 8px', backgroundColor: '#fff', width:'300px'}}>
            <Input.TextArea
              value={value}
              onChange={e => onChange(e.target.value)}
              onPressEnter={() => onSave()}
              onBlur={() => onSave()}
              autoFocus
            />
            <div style={{ marginTop: 8 }}>
              <Button size="small" onClick={() => onSave()}>确定</Button>
              <Button size="small" onClick={() => onCancel()} style={{ marginLeft: 8 }}>取消</Button>
            </div>
          </div>
        );
      },
    //   popupProps: {
    //     width: 300,
    //     height: 120,
    //   },
      placement: 'below', // 明确指定 placement
      onEditComplete: (value: any, record: TableRecord) => 
        handleSave(value, record, 'customEditor2')
    }
  };

  // 最后两列（文本类型）
  const lastColumns = Array.from({ length: 2 }, (_, columnIndex) => {
    const isLast = columnIndex === 1;
    return {
      title: `列${columnCount - 2 + columnIndex}`,
      dataIndex: `col${columnCount - 2 + columnIndex}`,
      key: `col${columnCount - 2 + columnIndex}`,
      width: 150,
      fixed: isLast ? 'right' as const : undefined,
      editor: {
        type: 'text',
        props: {
          placeholder: `请输入列${columnCount - 2 + columnIndex}`,
        },
        onEditComplete: (value: any, record: TableRecord) => 
          handleSave(value, record, `col${columnCount - 2 + columnIndex}`),
      }
    };
  });

  return [
    supplierColumn, 
    customEditorColumn,  // 第一个自定义编辑器列
    ...normalColumns, 
    supplier2Column,
    customEditorColumn2, // 第二个自定义编辑器列 
    ...lastColumns
  ];
};

// 生成测试数据
const generateData = (rowCount: number, columnCount: number): TableRecord[] => {
  const supplierValues = [
    { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场' },
    { value: 'hangzhou_honey', label: '杭州蜂蜜专业户' },
    { value: 'nanjing_base', label: '南京蜂��品基地' },
    { value: 'anhui_coop', label: '安徽蜂蜜合作社' },
    { value: 'shandong_corp', label: '山东蜂产品公司' }
  ];

  return Array.from({ length: rowCount }, (_, rowIndex) => {
    const record: TableRecord = { 
      id: String(rowIndex + 1),
      supplier: supplierValues[rowIndex % supplierValues.length],
      supplier2: supplierValues[(rowIndex + 2) % supplierValues.length],
      customEditor: `自定义编辑器数据 ${rowIndex + 1}`,
      customEditor2: `自定义编辑器2数据 ${rowIndex + 1}`, // 添加第二个自定义编辑器的数据
    };
    
    for (let colIndex = 0; colIndex < columnCount - 2; colIndex++) {
      record[`col${colIndex + 1}`] = `数据${rowIndex + 1}-${colIndex + 1}`;
    }
    
    return record;
  });
};

// 处理保存
const handleSave = async (
  value: any,
  record: TableRecord,
  dataIndex: string
): Promise<boolean> => {
  try {
    message.success('保存成功');
    return true;
  } catch (err: unknown) {
    console.error('保存失败:', err);
    message.error('保存失败');
    return false;
  }
};

const ROW_COUNT = 3;
const COLUMN_COUNT = 30;

export default () => {
  const [tableData, setTableData] = React.useState(generateData(ROW_COUNT, COLUMN_COUNT));
  const columns = React.useMemo(() => generateColumns(COLUMN_COUNT), []);

  return (
    <div>
      <h3>滚动编辑示例：</h3>
      <ul>
        <li>大数据量表格（30行×30列）</li>
        <li>左侧固定供应商搜索列</li>
        <li>右侧固定供应商2搜索列</li>
        <li>支持横向和纵向滚动</li>
        <li>首尾列固定</li>
        <li>编辑浮层自动定位</li>
        <li>自定义编辑器列（宽度300px，高度120px）</li>
      </ul>
      <ConfigProvider prefixCls="micro">
        <InfinityTable
          columns={columns}
          prefixCls='micro'
          dataSource={tableData}
          height={600}
          debug={{ enabled: true }}
          rowKey="id"
          width="100%"
          scroll={{ x: COLUMN_COUNT * 150, y: 600 }}
        />
      </ConfigProvider>
    </div>
  );
}; 