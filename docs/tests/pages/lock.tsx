/**
 * height: 800
 */
import React from 'react';
import { message } from 'antd';
import { InfinityTable } from '@xc/infinity-table';
import type { CellValue } from '../../../../src/components/InfinityTable/types';
import { ConfigProvider } from 'antd';

interface TableRecord extends Record<string, unknown> {
  id: string;
  name: string;
  age: number;
  salary: number;
  address: string;
  phone: string;
  editor?: string;  // 添加编辑者信息
}

// 生成测试数据
const data: TableRecord[] = [
  {
    id: '1',
    name: '张三',
    age: 28,
    salary: 12000,
    address: '浙江省杭州市西湖区',
    phone: '13800138000',
    editor: '小王'
  },
  {
    id: '2',
    name: '李四',
    age: 32,
    salary: 15000,
    address: '浙江省宁波市海曙区',
    phone: '13900139000',
  },
  {
    id: '3',
    name: '王五',
    age: 25,
    salary: 9000,
    address: '上海市浦东新区',
    phone: '13700137000',
    editor: '小李'
  }
];

export default () => {
  const [tableData, setTableData] = React.useState(data);
  // 添加编辑锁定状态管理
  const [editingRows, setEditingRows] = React.useState([
    { id: '1', message: '小王正在编辑此行' },
    { id: '3', message: '小李正在编辑此行' }
  ]);

  // 添加处理点击事件的函数
  const handleLockClick = () => {
    // 先清空之前的状态
    setEditingRows([]);
    
    // 然后设置新状态
    setTimeout(() => {
      setEditingRows([
        { id: '1', message: '小王正在编辑此行' },
        { id: '3', message: '小李正在编辑此行' }
      ]);

      // 2秒后清空
      setTimeout(() => {
        setEditingRows([]);
      }, 2000);
    }, 0);
  };

  // 处理保存
  const handleSave = async (
    value: CellValue,
    record: TableRecord,
    dataIndex: string
  ): Promise<boolean> => {
    try {
      setTableData(prevData =>
        prevData.map(item =>
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );

      message.success('保存成功');
      return true;
    } catch (err: unknown) {
      console.error('保存失败:', err);
      message.error('保存失败');
      return false;
    }
  };

  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      disabled: true,  // 直接禁用整列
      editor: { 
        type: 'text',
        props: {
          maxLength: 5,
          placeholder: '请输入姓名',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'name'),
      }
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
      width: 100,
      editor: {
        type: 'number',
        props: {
          min: 0,
          max: 150,
          precision: 0,
          placeholder: '请输入年龄',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'age'),
      }
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      width: 200,
      editor: {
        type: 'text',
        props: {
          maxLength: 50,
          placeholder: '请输入地址',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'address'),
      }
    },
    {
      title: '薪资',
      dataIndex: 'salary',
      key: 'salary',
      width: 150,
      editor: {
        type: 'number',
        props: {
          min: 0,
          precision: 2,
          suffix: '元',
          placeholder: '请输入薪资',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'salary'),
      }
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
      editor: {
        type: 'text',
        props: {
          maxLength: 11,
          placeholder: '请输入手机号',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'phone'),
      }
    }
  ];

  return (
    <div>
      <h3>编辑锁定示例：</h3>
      <ul>
        <li>姓名列整列禁用（不可编辑且无提示）</li>
        <li>部分字段无权限编辑（年龄、薪资、手机号，有提示）</li>
        <li>部分行正在被其他人编辑（有提示）</li>
        <li>双击单元格进入编辑状态</li>
      </ul>
      {/* 添加测试按钮 */}
      <button 
        onClick={handleLockClick}
        style={{ marginBottom: '16px' }}
      >
        测试锁定切换
      </button>
      <ConfigProvider prefixCls="micro">
        <InfinityTable
          columns={columns}
          prefixCls='micro'
          dataSource={tableData}
          height={900}
          debug={{ enabled: true }}
          rowKey="id"
          width="100%"
          // disableControl={{
          //   rules: ['age', 'salary', 'phone'],
          //   tooltip: {
          //     show: true,
          //     message: '您没有编辑此字段的权限',
          //   }
          // }}
          editingLock={{
            editingRows: editingRows, // 使用状态变量
            tooltip: {
              show: true,
              message: '他人正在编辑'
            }
          }}
        />
      </ConfigProvider>
    </div>
  );
}; 