---
title: infinity-table 可编辑表格
nav:
  title: 组件
  path: /components
group:
  title: 数据展示
  order: 2
---

# infinity-table 可编辑表格

可编辑表格组件,支持单元格编辑、自定义编辑器等功能。

## 何时使用

- 需要对表格数据进行编辑时
- 需要自定义编辑器时
- 需要处理大量数据的编辑场景

## 代码演示

### 基础用法


### 自定义编辑器


## API

### InfinityTable

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| columns | 表格列配置 | `ColumnType<T>[]` | - |
| dataSource | 数据数组 | `T[]` | - |
| onChange | 数据变化回调 | `(data: T[]) => void` | - |
| editable | 是否可编辑 | `boolean` | `true` |
| rowKey | 行数据的 key | `string` | `'id'` |

### ColumnType

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| editor | 编辑器配置 | `EditorConfig` | - |
| editable | 是否可编辑 | `boolean \| ((record: T) => boolean)` | `true` |

### EditorConfig

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| type | 编辑器类型 | `'text' \| 'number' \| 'select' \| 'custom'` | - |
| component | 自定义编辑器组件 | `React.ComponentType<EditorProps>` | - |
| props | 传递给编辑器的属性 | `object` | - |

## 主题定制

支持通过 CSS 变量覆盖默认样式: 