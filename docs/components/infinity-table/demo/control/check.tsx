/**
 * height: 600
 */
import React from 'react';
import { message } from 'antd';
import { InfinityTable } from '@xc/infinity-table';

// 模拟API请求
const mockApi = {
  // 检查编辑权限
  checkEditPermission: async (rowKey: string, dataIndex: string): Promise<boolean> => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 100% 被锁定
    return false;
  },

  // 更新数据
  updateTableData: async (rowKey: string, dataIndex: string, value: CellValue): Promise<boolean> => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 模拟随机错误
    if (Math.random() < 0.1) { // 10%的概率失败
      throw new Error('模拟更新失败');
    }

    return true;
  }
};

// 更多的测试数据
const data = [
  {
    id: '1',
    name: '张三',
    nickname: '小张',
    address: '浙江省杭州市',
  },
  {
    id: '2',
    name: '李四',
    nickname: '小李',
    address: '浙江省宁波市',
  },
  {
    id: '3',
    name: '王五',
    nickname: '小王',
    address: '上海市',
  },
  {
    id: '4',
    name: '赵六',
    nickname: '小赵',
    address: '北京市',
  },
  {
    id: '5',
    name: '钱七',
    nickname: '小钱',
    address: '广州市',
  }
];

export default () => {
  const [tableData, setTableData] = React.useState(data);
  const [editingLock, setEditingLock] = React.useState<{
    editingRows: Array<{ id: string; message: string }>;
    tooltip: {
      show?: boolean;
      message?: string;
    };
  }>({
    editingRows: [],
    tooltip: {
      show: true,
      message: '该记录正在被编辑'
    }
  });

  // 处理编辑前的检查
  const handleBeforeEdit = async (
    record: Record<string, any>,
    dataIndex: string
  ): Promise<boolean> => {
    try {
      const canEdit = await mockApi.checkEditPermission(record.id, dataIndex);
      
      if (!canEdit) {
        // 更新锁定状态，添加自定义消息
        setEditingLock(prev => ({
          ...prev,
          editingRows: [...prev.editingRows, { 
            id: record.id, 
            message: `${dataIndex} 字段正在被他人编辑` 
          }]
        }));
        message.warning('该记录正在被他人编辑');
        return false;
      }
      
      return true;
    } catch (error) {
      message.error('检查编辑权限失败');
      return false;
    }
  };

  // 处理编辑完成
  const handleEditComplete = async (
    value: string | number,
    record: Record<string, any>,
    dataIndex: string
  ): Promise<boolean> => {
    try {
      const success = await mockApi.updateTableData(record.id, dataIndex, value);

      if (success) {
        // 更新本地数据
        setTableData(prevData =>
          prevData.map(item =>
            item.id === record.id ? { ...item, [dataIndex]: value } : item
          )
        );

        // 移除锁定状态
        setEditingLock(prev => ({
          ...prev,
          editingRows: prev.editingRows.filter(item => item.id !== record.id)
        }));

        message.success('保存成功');
        return true;
      }
      return false;
    } catch (error) {
      message.error('保存失败');
      return false;
    }
  };

  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      editor: {
        type: 'text',
        props: {
          maxLength: 5,
          placeholder: '请输入姓名',
        },
        beforeEdit: (record: any) => handleBeforeEdit(record, 'name'),
        onEditComplete: (value: CellValue, record: any) => handleEditComplete(value, record, 'name'),
      },
      width: 150,
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
      key: 'nickname',
      width: 150,
      editor: {
        type: 'text',
        props: {
          maxLength: 10,
          placeholder: '请输入你的花名',
        },
        beforeEdit: (record: any) => handleBeforeEdit(record, 'nickname'),
        onEditComplete: (value: CellValue, record: any) => handleEditComplete(value, record, 'nickname'),
      },
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      editor: {
        type: 'text',
        props: {
          placeholder: '请输入地址',
        },
        beforeEdit: (record: any) => handleBeforeEdit(record, 'address'),
        onEditComplete: (value: CellValue, record: any) => handleEditComplete(value, record, 'address'),
      },
      width: 200,
    },
  ];

  return (
    <div>
      <h3>可编辑表格示例：</h3>
      <ul>
        <li>双击单元格进入编辑状态</li>
        <li>编辑前会检查是否被锁定（100%被锁定）</li>
        <li>编辑完成后自动保存（10%概率保存失败）</li>
      </ul>
      <InfinityTable
        columns={columns}
        dataSource={tableData}
        rowKey="id"
        editingLock={editingLock}
        bordered
        width="100%"
      />
    </div>
  );
};