/**
 * height: 600
 */
import React from 'react';
import { InfinityTable } from '@xc/infinity-table';
import { Tag, Space } from 'antd';

interface TableRecord {
  id: string;
  name: string;
  age: number;
  salary: number;
  address: string;
  phone: string;
  editor?: string;  // 添加编辑者信息
}

const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name', 
    editor: { 
      type: 'text',
      props: {
        maxLength: 5,
        placeholder: '请输入姓名',
      },
    },
    width: 120,
  },
  {
    title: '年龄',
    dataIndex: 'age',
    key: 'age', 
    width: 100,
    editor: { 
      type: 'number',
      props: {
        min: 0,
        max: 150,
        precision: 0,
        placeholder: '请输入年龄',
      },
    },
  },
  {
    title: '地址',
    dataIndex: 'address',
    key: 'address', 
    width: 200,
    editor: { 
      type: 'text',
      props: {
        maxLength: 50,
        placeholder: '请输入地址',
      },
    },
  },
  {
    title: '薪资',
    dataIndex: 'salary',
    key: 'salary',
    width: 150,
    editor: {
      type: 'number',
      props: {
        min: 0,
        precision: 2,
        suffix: '元',
        placeholder: '请输入薪资',
      },
    },
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    key: 'phone',
    width: 150,
    editor: {
      type: 'text',
      props: {
        maxLength: 11,
        placeholder: '请输入手机号',
      },
    },
  }
];

const data: TableRecord[] = [
  {
    id: '1',
    name: '张三',
    age: 28,
    salary: 12000,
    address: '浙江省杭州市西湖区',
    phone: '13800138000',
    editor: '小王'  // 正在被小王编辑
  },
  {
    id: '2',
    name: '李四',
    age: 32,
    salary: 15000,
    address: '浙江省宁波市海曙区',
    phone: '13900139000',
  },
  {
    id: '3',
    name: '王五',
    age: 25,
    salary: 9000,
    address: '上海市浦东新区',
    phone: '13700137000',
    editor: '小李'  // 正在被小李编辑
  }
];

export default () => {
  const [tableData, setTableData] = React.useState(data);

  return (
    <InfinityTable
      columns={columns}
      dataSource={tableData}
      rowKey="id"
      bordered
      width="100%"
      // 编辑锁定配置
      editingLock={{
        editingRows: [
          { id: '1', message: '小王正在编辑' },
          { id: '2', message: '' },
          { id: '3', message: '小李正在编辑' }
        ],
        tooltip: {
          show: true,
          message: '他人正在编辑'  // 当editingRows中没有message时的默认提示语
        }
      }}
    />
  );
};