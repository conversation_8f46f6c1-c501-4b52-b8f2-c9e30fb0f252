

## 2、组件的接入方法

### 基础配置使用

使用 Infinity Table 时，可通过相关配置项实现表格的基础功能：

```typescript
import { InfinityTable } from '@xc/infinity-table';
import React, { useState } from 'react';

const App = () => {
  const [data, setData] = useState(initialData);

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      editor: {
        type: 'text',
        props: {
          placeholder: 'Enter Name',
        },
        onEditComplete: (value, record) => {
          console.log(value, record);
        },
      },
    },
  ];

  return (
    <InfinityTable
      dataSource={data}
      columns={columns}
      rowKey="id"
    />
  );
};
```

**配置说明**：
- **`columns`**: 每一列的配置，包括表头文字、数据区块和编辑器配置。
- **`dataSource`**: 表格数据源，通过数组传入。
- **`rowKey`**: 指定表格中每一行的唯一标识。

### 自定义编辑器
支持通过 `editor` 控件自定义单元格的编辑体验：

```typescript
{
  title: 'Email',
  dataIndex: 'email',
  editor: {
    type: 'text',
    props: {
      placeholder: 'Enter Email',
    },
    editorRender: LevelSelect, //自定义编辑组件
    onEditComplete: (value, record) => {
      // 完成编辑后的事件
    },
  },
}
```


---
