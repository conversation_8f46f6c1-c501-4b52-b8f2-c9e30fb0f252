## 3、编辑链路和数据转换设计

### 概述
Infinity Table 支持内联和弹出式编辑模式，用户在单元格进入编辑状态时，数据通过一些共通链路完成转换和保存。以下是编辑链路的高清线路说明：

#### **1. 点击触发编辑**
- 用户点击表格单元格后，调用相关配置中的编辑器，并切换到编辑模式。
- Infinity Table 通过内部的事件链接和封装，根据传入的 `editor` 配置生成对应的编辑器。

#### **2. 编辑涉及的数据通道**
编辑进行时，数据会在以下流程中进行转换：

1. **当前值获取：**通过表格传入的 `dataSource` 和列配置中的 `dataIndex` 获取当前值。
2. **编辑值应用：**编辑器内部在用户操作后，通过 `onEditComplete` 事件返回用户修改的值。
3. **数据同步：**将用户输入的值同步到 `dataSource`，并触发组件重新渲染。

#### **3. 保存和验证**
- **保存逻辑：**
  - 编辑完成后，触发保存事件，通过 `TableContext` 中的 `setDataSource` 函数更新全局数据。
  - 可根据业务需求调用远程 API，将编辑内容同步到后端。
- **验证逻辑：**
  - 编辑值通过 `validator` 或自定义规则进行校验。
  - 若输入值不合规，则提示用户错误信息并阻止保存。

### TableContext 的实现

`TableContext` 是表格的中央存储和状态管理器，用于统一管理全局事件和数据。其主要功能包括：

#### **1. 完整的 Context API**
通过 React 的 Context API 构建，确保全局状态的共享与组件间的高效通信。

```typescript
import { createContext, useContext } from 'react';

const TableContext = createContext({
  dataSource: [],
  setDataSource: () => {},
  editingCell: null,
  setEditingCell: () => {},
});

export const useTableContext = () => useContext(TableContext);
```

#### **2. Context 内部状态管理**
`TableContext` 内部状态主要包括：
- **`dataSource`**：
  - 管理表格的所有数据。
  - 当用户编辑数据时，`dataSource` 会被更新，并触发表格重新渲染。
- **`editingCell`**：
  - 当前正在编辑的单元格信息（包括 `rowKey` 和 `dataIndex`）。
  - 用于切换编辑状态，确保单元格之间的编辑行为互不干扰。
- **操作函数：**
  - **`setDataSource`**：更新表格数据源。
  - **`setEditingCell`**：设置当前编辑单元格。

#### **3. 数据流转核心逻辑**
1. **触发编辑：**
   - 用户双击单元格，`setEditingCell` 被调用，记录当前编辑单元格状态。
   - 渲染对应的编辑器组件。
2. **值的修改与验证：**
   - 编辑器内修改的值通过 `onEditComplete` 回调传递到父组件。
   - 在父组件中对新值进行校验，并调用 `setDataSource` 更新数据。
3. **重新渲染：**
   - `setDataSource` 更新后，触发表格组件的重新渲染，确保视图与数据一致。

### 编辑模式切换与性能优化

#### **1. 编辑模式切换**
- **内联模式：**
  - 编辑器直接渲染在单元格内部。
  - 使用绝对定位实现样式调整，确保与单元格边界对齐。
- **弹出模式：**
  - 编辑器以浮层形式展示，适用于复杂或多字段编辑的场景。
  - 浮层位置根据单元格坐标动态计算，避免遮挡。

#### **2. 性能优化**
- **避免不必要的渲染：**
  - 使用 React.memo 或 shouldComponentUpdate 避免非编辑单元格的重复渲染。
- **虚拟滚动支持：**
  - 结合虚拟滚动技术，仅渲染可视区域的单元格，提升大数据量场景下的性能。
- **批量更新：**
  - 数据更新时，采用批量操作合并多次变更，减少渲染次数。

---

