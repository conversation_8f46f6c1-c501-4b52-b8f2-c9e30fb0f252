## 4、不同编辑浮层的定位规则

### 概述
Infinity Table 支持内嵌式和弹出式两种编辑模式，以适配不同的业务场景。无论是直接内嵌的编辑器还是弹出式的浮层，定位的精确性和灵活性是保证用户体验的关键。

以下将详细描述这两种模式的定位规则，并着重介绍如何实现弹出式编辑器在不同场景下的自适应上下定位。

---

### 内嵌式编辑器定位规则

#### **1. 定位策略**
内嵌式编辑器直接渲染在单元格内，其定位策略包括：

- **对齐规则：**
  - 编辑器边框与单元格边框对齐。
  - 保持编辑器宽度与单元格宽度一致。
- **绝对定位：**
  - 使用 `position: absolute`，相对于单元格的父容器进行定位。
  - 通过 `top`、`left`、`width` 和 `height` 动态设置位置和尺寸。

#### **2. 注意事项**
- 确保单元格内没有内容溢出，避免遮挡编辑器。
- 动态监听单元格尺寸变化（如窗口调整、列宽调整），并实时更新编辑器样式。

---

### 弹出式编辑器定位规则

#### **1. 定位策略**
弹出式编辑器通过浮层展示，适用于内容较复杂或需要更大空间的编辑场景。其定位策略包括：

- **动态计算位置：**
  - 根据单元格位置，优先尝试显示在单元格下方。
  - 若下方空间不足，则自动调整到上方。
- **避免遮挡：**
  - 浮层需要考虑表格边界和窗口边界，避免超出可视区域。

#### **2. 浮层定位算法**

1. **获取单元格位置和尺寸：**
   - 使用 `getBoundingClientRect()` 方法获取单元格的位置信息。

2. **计算可用空间：**
   - 获取窗口高度，计算单元格下方和上方的剩余空间。

3. **选择浮层位置：**
   - 若下方空间足够，则将浮层渲染在单元格下方。
   - 若下方空间不足且上方空间足够，则调整到上方。
   - 若上下空间都不足，则调整浮层尺寸以适配空间。

4. **应用样式：**
   - 动态设置浮层的 `top`、`left` 和 `transform` 属性。


#### **3. 注意事项**
- **边界检测：**
  - 考虑表格和窗口的边界，避免浮层超出。
  - 若浮层仍然可能溢出，可使用 `overflow: auto` 限制内容。
- **实时更新：**
  - 监听窗口滚动和尺寸变化，动态更新浮层位置。
- **交互优化：**
  - 点击浮层外部自动关闭。
  - 使用动画效果提升用户体验。

---

### 自适应上下的实现细节

#### **1. 自动调整方向**
弹出式编辑器通过计算可用空间，自动选择合适的方向。优先显示在单元格下方，当下方空间不足时，切换到上方。

#### **2. 边界处理**
- **左侧边界：**
  - 若单元格靠近窗口左侧，需调整浮层位置以避免溢出。
- **右侧边界：**
  - 若浮层超出窗口右侧，需向左调整位置。
- **上下溢出：**
  - 当上下空间不足时，调整浮层高度以适配。

#### **3. 响应式设计**
- 监听 `resize` 和 `scroll` 事件，确保浮层位置实时更新。

---
