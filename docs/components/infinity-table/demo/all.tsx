/**
 * height: 800
 */
import React from 'react';
import { message, Switch, Space, Button } from 'antd';
import { InfinityTable } from '@xc/infinity-table';
import type { CellValue } from '../../../../src/components/InfinityTable/types';
import dayjs from 'dayjs';
import { ConfigProvider } from 'antd';
import './custom-prefix.less';  // 引入自定义样式

// 添加 SearchParams 类型定义
interface SearchParams {
  keyword: string;
  field: string;
  record?: Record<string, unknown>;
}

// 模拟API请求
const mockApi = {
  updateTableData: async ({
    rowKey,
    dataIndex,
    value,
    forceSuccess = false
  }: {
    rowKey: string;
    dataIndex: string;
    value: CellValue;
    forceSuccess?: boolean;
  }) => {
    await new Promise(resolve => setTimeout(resolve, 2000));
    return { success: forceSuccess };
  },
  searchSuppliers: async (params: SearchParams) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const allSuppliers = [
      { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场' },
      { value: 'hangzhou_honey', label: '杭州蜂蜜专业户' },
      { value: 'nanjing_base', label: '南京蜂产品基地' },
      { value: 'anhui_coop', label: '安徽蜂蜜合作社' },
      { value: 'shandong_corp', label: '山东蜂产品公司' },
      { value: 'henan_farm', label: '河南养蜂基地' },
      { value: 'hangzhou_highland', label: '杭州高原蜂场' },
      { value: 'sichuan_park', label: '四川蜂蜜产业园' },
      { value: 'guangxi_market', label: '广西蜂蜜集散地' },
      { value: 'jiangxi_factory', label: '江西蜂蜜加工厂' },
    ].filter(item => 
      item.label.toLowerCase().includes(params.keyword.toLowerCase())
    );
    
    return allSuppliers;
  },
  uploadImage: async (file: File) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      success: true,
      url: 'https://s.xinc818.com/files/webcim3zonla0auicrk/111ailwindui.png'
    };
  },
};

// 城市选项
const cityOptions = [
  { value: 'hz', label: '杭州'},
  { value: 'nb', label: '宁波'},
  { value: 'sh', label: '上海'},
  { value: 'bj', label: '北京'},
  { value: 'gz', label: '广州'},
];

// 部门选项
const deptOptions = [
  { value: 'rd', label: '研发部'},
  { value: 'pd', label: '产品部'},
  { value: 'ux', label: '设计部'},
  { value: 'hr', label: '人事部'},
  { value: 'op', label: '运营部'},
];

// 技能选项
const skillOptions = [
  { value: 'js', label: 'JavaScript' },
  { value: 'java', label: 'Java' },
  { value: 'python', label: 'Python' },
  { value: 'go', label: 'Golang' },
  { value: 'rust', label: 'Rust' },
  { value: 'ts', label: 'TypeScript' },
];

// 兴趣爱好选项
const hobbyOptions = [
  { value: 'reading', label: '阅读' },
  { value: 'sports', label: '运动' },
  { value: 'music', label: '音乐' },
  { value: 'travel', label: '旅行' },
  { value: 'cooking', label: '烹饪' },
  { value: 'photography', label: '摄影' },
];

interface TableRecord extends Record<string, unknown> {
  id: string;
  name: string;
  nickname: string;
  age: number;
  birthday: string;
  city: string;
  dept: string;
  skills: string[];
  hobbies: string[];
  entryDate: string;
  salary: {
    min: number;
    max: number;
  };
  performance: number;
  photos: string[];
  email: string;
  phone: string;
  remark: string;
  weight: number;
  supplier: { value: string; label: string };
}

// 生成测试数据
const generateData = (count: number): TableRecord[] => {
  const supplierValues = [
    { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场' },
    { value: 'hangzhou_honey', label: '杭州蜂蜜专业户' },
    { value: 'nanjing_base', label: '南京蜂产品基地' },
    { value: 'anhui_coop', label: '安徽蜂蜜合作社' },
    { value: 'shandong_corp', label: '山东蜂产品公司' }
  ];
  
  return Array.from({ length: count }, (_, index) => ({
    id: String(index + 1),
    name: `用户${index + 1}`,
    nickname: `花名${index + 1}`,
    age: Math.floor(Math.random() * 30) + 20,
    birthday: dayjs().subtract(Math.floor(Math.random() * 30) + 20, 'years').format('YYYY-MM-DD'),
    city: cityOptions[Math.floor(Math.random() * cityOptions.length)].value,
    dept: deptOptions[Math.floor(Math.random() * deptOptions.length)].value,
    skills: Array.from({ length: Math.floor(Math.random() * 3) + 1 }, () => 
      skillOptions[Math.floor(Math.random() * skillOptions.length)].value
    ),
    hobbies: Array.from({ length: Math.floor(Math.random() * 3) + 1 }, () => 
      hobbyOptions[Math.floor(Math.random() * hobbyOptions.length)].value
    ),
    entryDate: dayjs().subtract(Math.floor(Math.random() * 1000), 'days').format('YYYY-MM-DD'),
    salary: {
      min: Math.floor(Math.random() * 5000) + 5000,
      max: Math.floor(Math.random() * 10000) + 10000,
    },
    performance: Number((Math.random() * 5).toFixed(1)),
    photos: [
      'https://s.xinc818.com/files/webcim3zonla0auicrk/111ailwindui.png',
      'https://s.xinc818.com/files/webcim3zonla0auicrk/111ailwindui.png',
    ].slice(0, Math.floor(Math.random() * 2) + 1),
    email: `user${index + 1}@example.com`,
    phone: `1${Math.floor(Math.random() * 9000000000 + 1000000000)}`,
    remark: `这是一段测试备注信息-${index + 1}`,
    weight: Number((Math.random() * 100 + 50).toFixed(2)),
    supplier: supplierValues[index % supplierValues.length]
  }));
};

export default () => {
  const [tableData, setTableData] = React.useState(generateData(30));
  const [forceSuccess, setForceSuccess] = React.useState(true);

  // 添加选择相关状态
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<React.Key[]>([]);
  const [loading, setLoading] = React.useState(false);

  // 处理保存
  const handleSave = async (
    value: CellValue,
    record: TableRecord,
    dataIndex: string
  ): Promise<boolean> => {
    try {
      const response = await mockApi.updateTableData({
        rowKey: record.id,
        dataIndex,
        value,
        forceSuccess
      });

      if (!response.success) {
        message.error('保存失败');
        return false;
      }

      // 只有在成功时才更新本地数据
      setTableData(prevData =>
        prevData.map(item =>
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );

      message.success('保存成功');
      return true;
    } catch (err: unknown) {
      console.error('保存失败:', err);
      message.error('保存失败');
      return false;
    }
  };

  // 处理选择变化
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // 批量操作示例
  const handleBatchOperation = () => {
    setLoading(true);
    // 模拟异步操作
    setTimeout(() => {
      message.success(`批量处理了 ${selectedRowKeys.length} 条数据`);
      setLoading(false);
      setSelectedRowKeys([]);
    }, 1000);
  };

  // 配置选择功能
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    selections: [
      {
        key: 'all',
        text: '选择全部',
        onSelect: (changableRowKeys: React.Key[]) => {
          setSelectedRowKeys(changableRowKeys);
        },
      },
      {
        key: 'none',
        text: '清空选择',
        onSelect: () => {
          setSelectedRowKeys([]);
        },
      },
      {
        key: 'adult',
        text: '选择成年人',
        onSelect: (changableRowKeys: React.Key[]) => {
          const newSelectedRowKeys = changableRowKeys.filter((key) => {
            const record = tableData.find(item => item.id === key);
            return record && record.age >= 18;
          });
          setSelectedRowKeys(newSelectedRowKeys);
        },
      },
    ],
  };

  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      disabled: true,
      editor: { 
        type: 'text',
        props: {
          maxLength: 5,
          placeholder: '请输入姓名',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'name'),
      }
    },
    {
      title: '花名',
      dataIndex: 'nickname',
      key: 'nickname',
      editor: {
        type: 'text',
        props: {
          maxLength: 100,
          placeholder: '请输入花名',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'nickname'),
      },
      width: 120,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
      editor: {
        type: 'number',
        props: {
          min: 0,
          max: 1000,
          placeholder: '请输入年龄',
          precision: 0, // 限制只能输入整数
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'age'),
      },
      width: 100,
    },
    {
      title: '生日',
      dataIndex: 'birthday',
      key: 'birthday',
      editor: {
        type: 'date',
        props: {
          format: 'YYYY-MM-DD',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'birthday'),
      },
      width: 100,
    },
    {
      title: '所在城市',
      dataIndex: 'city',
      key: 'city',
      editor: {
        type: 'select',
        props: {
          options: cityOptions,
          placeholder: '请选择城市',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'city'),
      },
      width: 80,
    },
    {
      title: '技能',
      dataIndex: 'skills',
      key: 'skills',
      show:false,
      editor: {
        type: 'multiSelect',
        props: {
          options: skillOptions,
          placeholder: '请选择技能',
          maxTagCount: 2,
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'skills'),
      },
      width: 200,
    },
    {
      title: '兴趣爱好',
      dataIndex: 'hobbies',
      key: 'hobbies',
    //   show:false,
      editor: {
        type: 'multi-select',
        props: {
          options: hobbyOptions,
          placeholder: '请选择兴趣爱好',
          maxTagCount: 2,
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'hobbies'),
      },
      width: 150,
    },
    {
      title: '薪资范围',
      dataIndex: 'salary',
      key: 'salary',
      editor: {
        type: 'number-range',
        props: {
          placeholder: ['最小值', '最大值'],
          min: 0,
          max: 100000,
          unit: '元',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'salary'),
      },
      width: 170,
    },
    {
      title: '绩效评分',
      dataIndex: 'performance',
      key: 'performance',
      editor: {
        type: 'number',
        props: {
          min: 0,
          max: 5,
        //   step: 0.1,
        //   precision:1,
          placeholder: '输入评分',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'performance'),
      },
      width: 120,
    },
    {
      title: '重量',
      dataIndex: 'weight',
      key: 'weight',
      editor: {
        type: 'number',
        props: {
          min: 0,
          max: 1000,
          precision: 2,
          placeholder: '请输入重量',
          suffix: 'kg',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'weight'),
      },
      width: 120,
    },

    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      editor: {
        type: 'text',
        props: {
          placeholder: '请输入邮箱',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'email'),
      },
      width: 200,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      editor: {
        type: 'text',
        props: {
          placeholder: '请输入手机号',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'phone'),
      },
      width: 150,
    },
    {
      title: '图片',
      dataIndex: 'photos',
      key: 'photos',
      editor: {
        type: 'images',
        props: {
          maxCount: 3,
          accept: 'image/*',
          maxSize: 5 * 1024 * 1024,
          customRequest: async ({ file, onSuccess, onError }) => {
            try {
              const response = await mockApi.uploadImage(file);
              if (response.success) {
                onSuccess?.(response);
              } else {
                onError?.(new Error('上传失败'));
              }
            } catch (error) {
              onError?.(error as Error);
            }
          }
        },
        overlayProps: {
          showClear: true,
          onClear: ({ record, onChange }) => {
            console.log('清除图片:', record);
            onChange([]);
          }
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'photos'),
      },
      width: 110,
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier',
      width: 200,
      editor: {
        type: 'search-select',
        props: {
          placeholder: '搜索供应商',
          onSearch: mockApi.searchSuppliers,
          initialOptions: [
            { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场' },
            { value: 'hangzhou_honey', label: '杭州蜂蜜专业户' },
            { value: 'nanjing_base', label: '南京蜂产品基地' },
            { value: 'anhui_coop', label: '安徽蜂蜜合作社' },
            { value: 'shandong_corp', label: '山东蜂产品公司' }
          ]
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'supplier')
      },
      display: {
        type: 'search-select',
        props: {
          initialOptions: [
            { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场' },
            { value: 'hangzhou_honey', label: '杭州蜂蜜专业户' },
            { value: 'nanjing_base', label: '南京蜂产品基地' },
            { value: 'anhui_coop', label: '安徽蜂蜜合作社' },
            { value: 'shandong_corp', label: '山东蜂产品公司' }
          ]
        }
      }
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 200,
      fixed: 'right' as const,
      editor: {
        type: 'text',
        props: {
          placeholder: '请输入备注信息',
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'remark'),
      }
    }
  ];

  return (
    <div>
      <h3>所有内置编辑器示例：</h3>
      <Space direction="vertical" size="middle" style={{ marginBottom: 16, width: '100%' }}>
        <ul>
          <li>包含文本、数字、日期、选择、多选、数字范围、图片上传等编辑器</li>
          <li>双击单元格进入编辑状态</li>
          <li>编辑完成后自动保存</li>
          <li>支持横向滚动和纵向滚动</li>
          <li>首尾列固定，便于操作</li>
        </ul>
        <Space>
          <span>模拟保存：</span>
          <Switch
            checked={forceSuccess}
            onChange={setForceSuccess}
            checkedChildren="成功"
            unCheckedChildren="失败"
          />
          <span style={{ color: '#666' }}>
            ({forceSuccess ? '当前模式：100% 保存成功' : '当前模式：100% 保存失败'})
          </span>
        </Space>
        <Space>
          <Button 
            type="primary"
            onClick={handleBatchOperation}
            disabled={selectedRowKeys.length === 0}
            loading={loading}
          >
            批量操作 ({selectedRowKeys.length} 条)
          </Button>
          <span style={{ color: '#666' }}>
            已选择 {selectedRowKeys.length} 条数据
          </span>
        </Space>
      </Space>
      <ConfigProvider prefixCls="micro">
        <InfinityTable
          rowSelection={rowSelection}
          columns={columns}
          prefixCls='micro'
          dataSource={tableData}
          height={900}
          debug={{ enabled: true }}
          rowKey="id"
          width="100%"
          scroll={{ x: 2500, y: 900 }}
        />
      </ConfigProvider>
    </div>
  );
};
