/**
 * height: 600
 */
import React from 'react';
import { InfinityTable } from '@xc/infinity-table';

const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name', 
    editor: { 
      type: 'text',
      props: {
        maxLength: 5,
        placeholder: '请输入姓名',
      },
    },
    width: 150,
  },
  {
    title: '姓名',
    dataIndex: 'nickname',
    key: 'nickname', 
    width: 150,
    editor: { 
      type: 'text',
      props: {
        maxLength: 10,
        placeholder: '请输入你的花名',
      },
    },
  },
  {
    title: '地址',
    dataIndex: 'address',
    key: 'address', 
    editor: { 
      type: 'text',
    },
  },


];

const data = [
  {
    id: '1',
    name: '张三',
    nickname: '小张',
    address: '浙江省杭州市',
  },
  {
    id: '2',
    name: '李四',
    nickname: '小李',
    address: '浙江省宁波市',
  },
  {
    id: '3',
    name: '王五',
    nickname: '小王',
    address: '上海市',
  }
];

export default () => {
  const [tableData, setTableData] = React.useState(data);


  return (
    <InfinityTable
      columns={columns}
      dataSource={tableData}
      rowKey="id"
      debug={{ enabled: true }}
      bordered
    />
  );
};