import React from 'react';
import { InfinityTable } from '@xc/infinity-table';
import { ConfigProvider, Table } from 'antd';
import '../custom-prefix.less';  // 引入自定义样式

interface DataType extends Record<string, unknown> {
  id: string;
  name: string;
  age: number;
  address: string;
}

const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    editor: {
      type: 'text'
    }
  },
  {
    title: '年龄',
    dataIndex: 'age',
    key: 'age',
    width: 150,
    editor: {
      type: 'number'
    }
  },
  {
    title: '地址',
    dataIndex: 'address',
    key: 'address',
    width: 300,
    editor: {
      type: 'text'
    }
  }
];

const data: DataType[] = [
  { id: '1', name: '张三', age: 25, address: '北京市朝阳区' },
  { id: '2', name: '李四', age: 30, address: '上海市浦东新区' },
  { id: '3', name: '王五', age: 28, address: '广州市天河区' }
];

const CustomPrefixDemo: React.FC = () => {
  return (
    <div>
      <h3>自定义前缀示例 Micro</h3>
      <p>本示例展示了如何自定义 InfinityTable 的前缀，将默认的 ant 前缀修改为 micro</p>
      
      <ConfigProvider prefixCls="micro">
        <InfinityTable<DataType>
          prefixCls="micro"
          columns={columns}
          dataSource={data}
          height={400}
          debug={{ enabled: true }}
          width="100%"
        />
      </ConfigProvider>

      <h3 style={{ marginTop: 32 }}>Antd4 原生表格示例</h3>
      <p>这是一个原生的 Antd4 Table 组件示例</p>
      <Table<DataType>
        columns={columns}
        dataSource={data}
        pagination={false}
        scroll={{ y: 400 }}
      />
    </div>
  );
};

export default CustomPrefixDemo;
