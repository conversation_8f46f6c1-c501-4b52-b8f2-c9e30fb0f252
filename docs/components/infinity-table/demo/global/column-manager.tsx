import React, { useState } from 'react';
import { InfinityTable } from '@xc/infinity-table';

interface DataType extends Record<string, unknown> {
  id: string;
  key: string;
  name: string;
  age: number;
  address: string;
  email: string;
  phone: string;
  department: string;
  position: string;
  salary: number;
  status: string;
  hobby: string;
  education: string;
  joinDate: string;
  birthday: string;
  gender: string;
  level: string;
  score: number;
  rank: number;
  region: string;
  remark: string;
}

// 定义列配置类型
interface ColumnConfig extends ColumnType<DataType> {
  show?: boolean; // 是否显示
  unremovable?: boolean; // 标记该列是否不可删除(必须显示)
}

const ColumnManagerDemo: React.FC = () => {
  // 定义默认列配置 - 使用不同的初始顺序
  const defaultColumns: ColumnConfig[] = [
    {
      key: 'name',
      title: '姓名(固)',
      dataIndex: 'name', 
      width: 100,
      unremovable: true
    },
    {
      key: 'department',
      title: '部门',
      dataIndex: 'department',
      width: 120,
      show: false, // 在默认配置中是显示的
    },
  ];

  // 当前列配置 - 使用不同的顺序和显示状态
  const [columns, setColumns] = useState<ColumnConfig[]>([
    {
      key: 'name',
      title: '姓名(固)',
      dataIndex: 'name', 
      width: 100,
      unremovable: true
    },
    {
      key: 'age',
      title: '年龄',
      dataIndex: 'age',
      width: 80,
    },
    {
      key: 'department',
      title: '部门',
      dataIndex: 'department',
      width: 120,
      show: false, // 当前是隐藏的
    },
    {
      key: 'position',
      title: '职位',
      dataIndex: 'position',
      width: 120,
      show: true,
    },
    {
      key: 'salary',
      title: '薪资',
      dataIndex: 'salary',
      width: 100,
      show: true,
    },
    {
      key: 'status',
      title: '状态',
      dataIndex: 'status',
      width: 100,
      show: true,
    },
    {
      key: 'address',
      title: '地址',
      dataIndex: 'address',
      width: 200,
      show: true,
    },
    {
      key: 'email',
      title: '邮箱',
      dataIndex: 'email',
      width: 180,
      show: true,
    },
    {
      key: 'phone',
      title: '电话',
      dataIndex: 'phone',
      width: 120,
      show: true,
    },
    {
      key: 'hobby',
      title: '兴趣爱好',
      dataIndex: 'hobby',
      width: 120,
      show: true,
    },
    {
      key: 'education',
      title: '学历',
      dataIndex: 'education',
      width: 100,
      show: true,
    },
    {
      key: 'joinDate',
      title: '入职日期',
      dataIndex: 'joinDate',
      width: 120,
      show: true,
    },
    {
      key: 'birthday',
      title: '生日',
      dataIndex: 'birthday',
      width: 120,
      show: true,
    },
    {
      key: 'gender',
      title: '性别',
      dataIndex: 'gender',
      width: 80,
      show: true,
    },
    {
      key: 'level',
      title: '级别',
      dataIndex: 'level',
      width: 100,
      show: true,
    },
    {
      key: 'score',
      title: '评分',
      dataIndex: 'score',
      width: 100,
      show: true,
    },
    {
      key: 'rank',
      title: '排名',
      dataIndex: 'rank',
      width: 100,
      show: true,
    },
    {
      key: 'region',
      title: '地区',
      dataIndex: 'region',
      width: 120,
      show: true,
    },
    {
      key: 'remark',
      title: '备注',
      dataIndex: 'remark',
      width: 150,
      show: true,
    },
    {
      key: 'operation',
      title: '操作',
      dataIndex: 'operation',
      width: 80,
      fixed: 'right',
    },
  ]);

  // 处理列显示变化
  const handleColumnsChange = (newColumnKeys: string[]) => {
    console.log('拿到的新列顺序:', newColumnKeys);
    
    // 创建一个映射来存储新的列顺序
    const orderMap = new Map(newColumnKeys.map((key, index) => [key, index]));
    
    setColumns(prevColumns => {
      // 1. 首先处理显示/隐藏
      const updatedColumns = prevColumns.map(col => ({
        ...col,
        show: col.fixed ? undefined : newColumnKeys.includes(col.dataIndex) ? undefined : false
      }));

      // 2. 然后根据 newColumnKeys 的顺序排序
      return [...updatedColumns].sort((a, b) => {
        // 固定列保持原位置
        if (a.fixed || b.fixed) return 0;
        
        const aOrder = orderMap.get(a.dataIndex) ?? Number.MAX_VALUE;
        const bOrder = orderMap.get(b.dataIndex) ?? Number.MAX_VALUE;
        return aOrder - bOrder;
      });
    });
  };

  // 部门列表
  const departments = ['技术部', '产品部', '销售部', '市场部', '人事部'];
  // 职位列表
  const positions = ['工程师', '经理', '主管', '专员', '总监'];
  // 状态列表
  const statusList = ['在职', '休假', '离职', '试用期'];

  // 模拟数据 - 增加到20条
  const data: DataType[] = Array.from({ length: 20 }, (_, index) => ({
    id: index.toString(),
    key: index.toString(),
    name: `用户${index + 1}`,
    age: 20 + Math.floor(Math.random() * 40),
    department: departments[Math.floor(Math.random() * departments.length)],
    position: positions[Math.floor(Math.random() * positions.length)],
    salary: Math.floor(Math.random() * 20000 + 10000),
    status: statusList[Math.floor(Math.random() * statusList.length)],
    address: `城市${index + 1}路${Math.floor(Math.random() * 100)}号 问王企鹅企微王企鹅王企鹅王企鹅企微王企鹅王企鹅企微额温枪额温枪额温枪额温枪二王企鹅我全额`,
    email: `user${index + 1}@example.com`,
    phone: `1${Math.floor(Math.random() * 1000000000).toString().padStart(9, '0')}`,
    hobby: ['阅读', '运动', '音乐', '旅行', '摄影'][Math.floor(Math.random() * 5)],
    education: ['本科', '硕士', '博士', '大专'][Math.floor(Math.random() * 4)],
    joinDate: `202${Math.floor(Math.random() * 4)}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
    birthday: `199${Math.floor(Math.random() * 9)}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
    gender: Math.random() > 0.5 ? '男' : '女',
    level: ['P1', 'P2', 'P3', 'P4', 'P5'][Math.floor(Math.random() * 5)],
    score: Math.floor(Math.random() * 41) + 60, // 60-100的随机数
    rank: Math.floor(Math.random() * 100) + 1,
    region: ['华东', '华南', '华北', '西南', '东北'][Math.floor(Math.random() * 5)],
    remark: `备注信息${index + 1}`,
  }));

  return (
    <InfinityTable<DataType>
      columns={columns}
      defaultColumns={defaultColumns} // 传入默认列配置
      dataSource={data}
      rowKey="id"
      columnManager={{
        onChange: handleColumnsChange,
        defaultColumns
      }}
      scroll={{ y: 400, x: 'max-content' }}
    />
  );
};

export default ColumnManagerDemo;
