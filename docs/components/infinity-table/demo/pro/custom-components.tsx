/**
 * height: 600
 */
import React from 'react';
import { message, Select, Button } from 'antd';
import { InfinityTable } from '@xc/infinity-table';

// 自定义编辑组件
const CustomAddressEditor: React.FC<EditorProps> = ({
  value,
  onChange,
  onSave,
  onCancel,
  style,
  record
}) => {

  console.log('CustomImageEditor props222:', {
    value,
    onChange,
    onSave,
    onCancel,
    style,
    record,
  });


  const [province, city] = (value as string).split('-');
  console.log('record', record);

  const handleProvinceChange = (newProvince: string) => {
    onChange(`${newProvince}-${city || ''}`);
  };

  const handleCityChange = (newCity: string) => {
    onChange(`${province}-${newCity}`);
  };

  return (
    <div style={{ 
      padding: '8px', 
      display: 'flex', 
      gap: '8px',
      position: 'relative',
      zIndex: 1001,
      ...style 
    }}>
      <Select
        value={province}
        onChange={handleProvinceChange}
        style={{ width: 120 }}
        options={[
          { label: '浙江省', value: '浙江省' },
          { label: '江苏省', value: '江苏省' },
          { label: '上海市', value: '上海市' },
        ]}
        dropdownStyle={{ zIndex: 1002 }}
      />
      <Select
        value={city}
        onChange={handleCityChange}
        style={{ width: 120 }}
        options={[
          { label: '杭州市', value: '杭州市' },
          { label: '宁波市', value: '宁波市' },
          { label: '苏州市', value: '苏州市' },
        ]}
        dropdownStyle={{ zIndex: 1002 }}
      />
      <span>我是文案</span>
      <Button onClick={onSave}>确定</Button>
      <Button onClick={onCancel}>取消</Button>
    </div>
  );
};

// 模拟API请求
const mockApi = {
  updateTableData: async (params: {
    rowKey: string;
    dataIndex: string;
    value: CellValue;
  }): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    // if (Math.random() < 0.1) throw new Error('模拟更新失败');
    return true;
  }
};

const data = [
  {
    id: '1',
    name: '张三',
    nickname: '小张',
    address: '浙江省-杭州市',
  },
  {
    id: '2',
    name: '李四',
    nickname: '小李',
    address: '浙江省-宁波市',
  },
];

export default () => {
  const [tableData, setTableData] = React.useState(data);

  const handleSave = async (
    value: CellValue,
    record: Record<string, any>,
    dataIndex: string
  ): Promise<boolean> => {
    try {
      const success = await mockApi.updateTableData({
        rowKey: record.id,
        dataIndex,
        value
      });

      if (success) {
        setTableData(prevData =>
          prevData.map(item =>
            item.id === record.id ? { ...item, [dataIndex]: value } : item
          )
        );
        message.success('保存成功');
        return true;
      }
      return false;
    } catch (error) {
      message.error('保存失败');
      return false;
    }
  };

  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      editor: {
        type: 'text',
        props: {
          maxLength: 5,
          placeholder: '请输入姓名',
        },
        onEditComplete: (value: CellValue, record: any) => handleSave(value, record, 'name'),
      },
      width: 150,
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      editor: {
        // type: 'custom',
        editorRender: CustomAddressEditor,
        props: {
          placeholder: '请选择地址',
        },
        onEditComplete: (value: CellValue, record: any) => handleSave(value, record, 'address'),
      },
      width: 300,
      render: (value: string) => {
        const [province, city] = value.split('-');
        return `${province} ${city}`;
      }
    },
  ];

  return (
    <div>
      <h3>自定义编辑组件示例：</h3>
      <ul>
        <li>地址列使用自定义的省市选择器</li>
        <li>支持在自定义组件内使用Portal</li>
      </ul>
      <InfinityTable
        columns={columns}
        dataSource={tableData}
        rowKey="id"
        debug={{ enabled: true }}
        width="100%"
        onEditStateChange={async ({ value, record, column, type }) => {
          switch(type) {
            case 'unchanged':
              console.log('值未变更');
              break;
            case 'invalid':
              console.log('验证失败');
              break;
            case 'success':
              console.log('保存成功');
              break;
          }
          message.info(`编辑状态变更: ${type}`);
        }}
      />
    </div>
  );
};