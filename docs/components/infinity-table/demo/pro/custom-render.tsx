import React from 'react';
import { InfinityTable } from '@xc/infinity-table';
import { Tag, Progress, Select } from 'antd';

// 定义状态的复杂数据结构
interface StatusValue {
  code: string;
  label: string;
  extra?: {
    color: string;
    description?: string;
  };
}

interface TableRecord extends Record<string, unknown> {
  id: string;
  name: string;
  status: string;
  statusExtra?: StatusValue;
  progress: number;
  score: number;
}

const data: TableRecord[] = [
  {
    id: '1',
    name: '项目 A',
    status: 'active',  // 使用简单字符串
    statusExtra: {     // 可选的额外信息
      code: 'active',
      label: '进行中',
      extra: {
        color: 'green',
        description: '正常进行中'
      }
    },
    progress: 30,
    score: 4.5,
  },
  {
    id: '2',
    name: '项目 B',
    status: 'pending',
    statusExtra: {
      code: 'pending',
      label: '待处理',
      extra: {
        color: 'orange',
        description: '等待处理中'
      }
    },
    progress: 70,
    score: 3.8,
  },
  {
    id: '3',
    name: '项目 C',
    status: 'completed',
    statusExtra: {
      code: 'completed',
      label: '已完成',
      extra: {
        color: 'blue',
        description: '已完成项目'
      }
    },
    progress: 100,
    score: 5.0,
  },
];

// 自定义状态选择器组件
const StatusEditor: React.FC<{
  value?: string;  // 接收简单字符串
  onChange?: (value: StatusValue) => void;  // 但返回复杂对象
}> = ({ value, onChange }) => {
  const handleChange = (code: string) => {
    // 构建复杂的状态对象
    const statusMap: Record<string, StatusValue> = {
      active: {
        code: 'active',
        label: '进行中',
        extra: { color: 'green' }
      },
      pending: {
        code: 'pending',
        label: '待处理',
        extra: { color: 'orange' }
      },
      completed: {
        code: 'completed',
        label: '已完成',
        extra: { color: 'blue' }
      }
    };
    
    onChange?.(statusMap[code]);
  };

  return (
    <Select
      value={value}  // 使用简单的状态码
      onChange={handleChange}
      style={{ width: '100%' }}
    >
      <Select.Option value="active">进行中</Select.Option>
      <Select.Option value="pending">待处理</Select.Option>
      <Select.Option value="completed">已完成</Select.Option>
    </Select>
  );
};

export default () => {
  const [tableData, setTableData] = React.useState(data);

  const handleSave = (value: CellValue | StatusValue, record: TableRecord, dataIndex: string) => {
    if (dataIndex === 'status' && typeof value === 'object') {
      // 如果是状态字段，需要特殊处理
      const statusValue = value as StatusValue;
      setTableData(prevData => 
        prevData.map(item => 
          item.id === record.id ? {
            ...item,
            status: statusValue.code,  // 保存简单状态码
            statusExtra: statusValue   // 可选：���存完整状态对象
          } : item
        )
      );
    } else {
      // 其他字段正常保存
      setTableData(prevData => 
        prevData.map(item => 
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );
    }
    return Promise.resolve(true);
  };

  const columns = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      editor: {
        type: 'text',
        props: {
          maxLength: 20,
        },
        onEditComplete: (value: CellValue, record: TableRecord) => 
          handleSave(value, record, 'name'),
      },
    },
    // {
    //   title: '状态',
    //   dataIndex: 'status',
    //   key: 'status',
    //   width: 100,
    //   editor: {
    //     type: 'custom',
    //     editorRender: StatusEditor,
    //     onEditComplete: (value: StatusValue, record: TableRecord) => 
    //       handleSave(value, record, 'status'),
    //     displayRender: ({ value, record }) => {
    //       // 使用 statusExtra 中的信息来渲染，如果没有则使用默认映射
    //       const statusMap: Record<string, { color: string; label: string }> = {
    //         active: { color: 'green', label: '进行中' },
    //         pending: { color: 'orange', label: '待处理' },
    //         completed: { color: 'blue', label: '已完成' },
    //       };
          
    //       const currentValue = value as string;
    //       const defaultStatus = statusMap[currentValue];
    //       const status = record?.statusExtra || defaultStatus;
          
    //       if (!status) {
    //         return currentValue; // 降级处理
    //       }
          
    //       return (
    //         <div style={{ padding: '0 12px' }}>
    //           <Tag color={status.extra?.color || defaultStatus.color}>
    //             {status.label || defaultStatus.label}
    //           </Tag>
    //           {status.extra?.description && (
    //             <span style={{ fontSize: 12, marginLeft: 8 }}>
    //               {status.extra.description}
    //             </span>
    //           )}
    //         </div>
    //       );
    //     },
    //   }
    // },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      editor: {
        type: 'number',
        props: {
          min: 0,
          max: 100,
          precision: 0,
        },
        displayRender: ({ value }) => {            
          return (
            <Progress 
              style={{ width: '100%', padding: '0 12px' }}
              percent={value as number} 
              size="small" 
              status={value === 100 ? 'success' : 'active'}
            />
          );
        },
        onEditComplete: (value: CellValue, record: TableRecord) => 
          handleSave(value, record, 'progress'),
      },
    },
    {
      title: '评分',
      dataIndex: 'score',
      key: 'score',
      width: 100,
      editor: {
        type: 'number',
        props: {
          min: 0,
          max: 5,
          precision: 1,
          step: 0.1,
        },
        displayRender: ({ value }) => {
            
          const score = value as number;
          const color = score >= 4.5 ? '#52c41a' : 
                       score >= 4.0 ? '#1890ff' : 
                       score >= 3.5 ? '#faad14' : '#ff4d4f';
                         
          return (
            <div style={{ 
              color,
              padding: '0 12px',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: 4
            }}>
              {score}
              <span style={{ fontSize: 12 }}>/5.0</span>
            </div>
          );
        },
        onEditComplete: (value: CellValue, record: TableRecord) => 
          handleSave(value, record, 'score'),
      },
    },
  ];

  return (
    <div>
      <ul>
        <li>支持复杂数据结构的编辑和展示</li>
        <li>自定义编辑器组件</li>
        <li>配合 displayRender 展示富文本内容</li>
        <li>双击单元格进入编辑状态</li>
      </ul>
      <InfinityTable<TableRecord>
        columns={columns}
        dataSource={tableData}
        rowKey="id"
        bordered
      />
    </div>
  );
};