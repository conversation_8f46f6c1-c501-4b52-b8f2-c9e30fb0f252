/**
 * height: 600
 */
import React from 'react';
import { InfinityTable } from '@xc/infinity-table';
import { Input, Select, Space, Tag } from 'antd';

interface TableRecord {
  id: string;
  name: string;
  level: string;
  remark: string;
}

type CellValue = string | number | null;

  // 自定义等级选择器
  const LevelSelect: React.FC<{
    value?: string;
    onChange?: (value: string) => void;
    onBlur?: () => void;
  }> = ({ value, onChange, onBlur }) => (
    <Select
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      style={{ width: '100%' }}
      options={[
        { value: 'S', label: 'S级' },
        { value: 'A', label: 'A级' },
        { value: 'B', label: 'B级' },
        { value: 'C', label: 'C级' },
      ]}
      dropdownStyle={{ zIndex: 1002 }}
    />
  );

  const data: TableRecord[] = [
    {
      id: '1',
      name: '张三',
      level: 'S',
      remark: '非常优秀的员工'
    },
    {
      id: '2',
      name: '李四',
      level: 'A',
      remark: '工作态度认真'
    },
    {
      id: '3',
      name: '王五',
      level: 'B',
      remark: '需要继续努力'
    },
    {
      id: '4',
      name: '赵六',
      level: 'C',
      remark: '新人'
    }
  ];
export default () => {
  const [tableData, setTableData] = React.useState(data);

  const handleSave = (value: CellValue, record: TableRecord, dataIndex: string) => {
    setTableData(prevData => 
      prevData.map(item => 
        item.id === record.id ? { ...item, [dataIndex]: value } : item
      )
    );
    return Promise.resolve(true);
  };



const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 150,
    editor: {
      type: 'text',
      props: {
        placeholder: '请输入姓名',
        maxLength: 10,
      },
      onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'name')
    }
  },
  {
    title: '等级',
    dataIndex: 'level',
    key: 'level',
    width: 120,
    editor: {
      type: 'custom',
      editorRender: LevelSelect,
      onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'level')
    },
    render: (value: string) => {
      const levelConfig: Record<string, { color: string, label: string, bgColor: string }> = {
        S: { 
          color: '#f50', 
          label: 'S级',
          bgColor: '#fff2e8'
        },
        A: { 
          color: '#52c41a', 
          label: 'A级',
          bgColor: '#f6ffed'
        },
        B: { 
          color: '#1677ff', 
          label: 'B级',
          bgColor: '#e6f4ff'
        },
        C: { 
          color: '#666', 
          label: 'C级',
          bgColor: '#f5f5f5'
        },
      };

      const config = levelConfig[value];
      return (
        <Tag 
          color={config.bgColor}
          style={{
            color: config.color,
            border: `1px solid ${config.color}`,
            fontSize: '14px',
            padding: '2px 12px',
            borderRadius: '12px'
          }}
        >
          {config.label}
        </Tag>
      );
    }
  },
  {
    title: "地址(长文本)",
    dataIndex: "address",
    key: "address",
    editor: {
      type: 'text',
      props: {
        placeholder: '请输入地址',
      },
      onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'address')
    }
  },
];



  return (
    <div>
      <h3>等级编辑示例：</h3>
      <ul>
        <li>点击单元格进入编辑状态</li>
        <li>支持下拉选择等级</li>
        <li>不同等级显示不同样式</li>
      </ul>
      <InfinityTable
        columns={columns}
        dataSource={tableData}
        rowKey="id"
        bordered
        width="100%"
      />
    </div>
  );
};