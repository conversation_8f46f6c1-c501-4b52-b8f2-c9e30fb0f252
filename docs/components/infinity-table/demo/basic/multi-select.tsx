/**
 * height: 600
 */
import React from 'react';
import { message } from 'antd';
import { InfinityTable } from '@xc/infinity-table';

type CellValue = string[];

interface TableRecord {
  id: string;
  tags: string[];
  platforms: string[];
  permissions: string[];
  info: string;
}

// 定义选项常量
const TAG_OPTIONS = [
  { value: 'HOT', label: '热门' },
  { value: 'NEW', label: '新品' },
  { value: 'SALE', label: '促销' },
  { value: 'RECOMMEND', label: '推荐' },
];

const PLATFORM_OPTIONS = [
  { value: 'ANDROID', label: '安卓' },
  { value: 'IOS', label: 'iOS' },
  { value: 'WEB', label: '网页' },
  { value: 'MINI', label: '小程序' },
];

const PERMISSION_OPTIONS = [
  { value: 'READ', label: '查看' },
  { value: 'WRITE', label: '编辑' },
  { value: 'DELETE', label: '删除' },
  { value: 'PUBLISH', label: '发布' },
  { value: 'AUDIT', label: '审核' },
];


const data: TableRecord[] = [
  {
    id: '1',
    tags: ['HOT', 'NEW'],
    platforms: ['ANDROID', 'IOS'],
    permissions: ['READ', 'WRITE'],
    info: '示例数据1'
  },
  {
    id: '2',
    tags: ['SALE'],
    platforms: ['WEB', 'MINI'],
    permissions: ['READ', 'WRITE', 'PUBLISH'],
    info: '示例数据2'
  },
  {
    id: '3',
    tags: ['RECOMMEND', 'HOT'],
    platforms: ['ANDROID', 'WEB'],
    permissions: ['READ', 'WRITE', 'DELETE', 'AUDIT'],
    info: '示例数据3'
  },
  {
    id: '4',
    tags: ['NEW', 'SALE'],
    platforms: ['IOS', 'MINI'],
    permissions: ['READ', 'PUBLISH'],
    info: '示例数据4'
  }
];

export default () => {
  const [tableData, setTableData] = React.useState(data);
  

  const handleSave = async (
    value: CellValue,
    record: TableRecord,
    dataIndex: string
  ): Promise<void> => {
    try {
      console.log(
        `Saving ${dataIndex} for record ${record.id} with value:`,
        value
      );

      setTableData((prevData) =>
        prevData.map((item) =>
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );

      message.success("保存成功");
    } catch (error) {
      message.error("保存失败");
      throw error;
    }
  };

  const columns = [
    {
      title: '商品标签',
      dataIndex: 'tags',
      key: 'tags',
      width: 200,
      editor: {
        type: 'multi-select',
        props: {
          options: TAG_OPTIONS,
          placeholder: '请选择标签',
          maxTagCount: 2,
          allowClear: true
        },
        overlayProps: {
          showClear: true,
          onClear: ({ onChange }) => {
            onChange([]);
          }
        },
        onEditComplete: (value: CellValue, record: TableRecord) =>
          handleSave(value, record, 'tags')
      }
    },
    {
      title: '发布平台',
      dataIndex: 'platforms',
      key: 'platforms',
      width: 200,
      editor: { 
        type: 'multi-select',
        props: {
          options: PLATFORM_OPTIONS,
          placeholder: '请选择平台',
          maxTagCount: 3,
          allowClear: true
        },
        overlayProps: {
          showClear: true,
          onClear: ({ onChange }) => {
            onChange([]);
          }
        },
        onEditComplete: (value: CellValue, record: TableRecord) =>
          handleSave(value, record, 'platforms')
      }
    },
    {
      title: '权限设置',
      dataIndex: 'permissions',
      key: 'permissions',
      width: 220,
      editor: { 
        type: 'multi-select',
        props: {
          options: PERMISSION_OPTIONS,
          placeholder: '请选择权限',
          mode: 'multiple',
          maxTagCount: 2,
          allowClear: true
        },
        overlayProps: {
          showClear: true,
          onClear: ({ onChange }) => {
            onChange([]);
          }
        },
        onEditComplete: (value: CellValue, record: TableRecord) =>
          handleSave(value, record, 'permissions')
      }
    },
    {
      title: '其他信息',
      dataIndex: 'info',
      key: 'info',
      render: (text: string) => {
        return <span>{text}</span>;
      },
    },
  ];
  

  return (
    <InfinityTable
      columns={columns}
      dataSource={tableData}
      rowKey="id"
      bordered
      width="100%"
    />
  );
};