import React from "react";
import { message } from "antd";
import { InfinityTable } from "@xc/infinity-table";

interface TableRecord {
  id: number;
  name: string;
  nickname: string;
  address: string;
}

type CellValue = string;

const data = [
  {
    id: 1,
    name: "张三",
    nickname: "小张",
    address: "浙江省杭州市",
  },
  {
    id: 2,
    name: "李四",
    nickname: "小李",
    address: "浙江省宁波市",
  },
  {
    id: 3,
    name: "王五",
    nickname: "小王",
    address: "上海市",
  },
];

export default () => {
  const [tableData, setTableData] = React.useState(data);

  const columns = [
    {
      title: "姓名(限制5个字)",
      dataIndex: "name",
      key: "name",
      editor: {
        type: "text",
        props: {
          maxLength: 5,
          placeholder: "请输入姓名",
        },
        onEditComplete: (value: CellValue, record: TableRecord) =>
          handleSave(value, record, "name"),
      },
      width: 150,
    },
    {
      title: "花名(限制10个字)",
      dataIndex: "nickname",
      key: "nickname",
      width: 150,
      editor: {
        type: "text",
        props: {
          maxLength: 10,
          placeholder: "请输入你的花名",
        },
        onEditComplete: (value: CellValue, record: TableRecord) =>
          handleSave(value, record, "nickname"),
      },
    },
    {
      title: "地址(长文本)",
      dataIndex: "address",
      key: "address",
      editor: {
        type: "text",
        onEditComplete: (value: CellValue, record: TableRecord) =>
          handleSave(value, record, "address"),
      },
    },
  ];

  const handleSave = async (
    value: CellValue,
    record: TableRecord,
    dataIndex: string
  ): Promise<void> => {
    try {
      console.log(
        `Saving ${dataIndex} for record ${record.id} with value ${value}`
      );

      setTableData((prevData) =>
        prevData.map((item) =>
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );

      message.success("保存成功");
    } catch (error) {
      message.error("保存失败");
      throw error;
    }
  };


  return (
    <div>
      <InfinityTable
        columns={columns}
        dataSource={tableData}
        rowKey="id"
        width="100%"
      />
    </div>
  );
};
