/**
 * height: 800
 */
import React from 'react';
import { InfinityTable } from '@xc/infinity-table';
import moment from 'moment';

interface TableRecord {
  id: string;
  name: string;
  birthday: string;
  registerTime: string;
  lastLoginTime: string;
  remark: string;
}

type CellValue = string;

const data: TableRecord[] = [
  {
    id: '1',
    name: '张三',
    birthday: '1990-01-01',
    registerTime: '2021-01-01 12:00:00',
    lastLoginTime: '2024-03-20 15:30:00',
    remark: '示例数据1'
  },
  {
    id: '2',
    name: '李四',
    birthday: '1992-05-20',
    registerTime: '2021-06-15 09:30:00',
    lastLoginTime: '2024-03-19 18:45:00',
    remark: '示例数据2'
  },
  {
    id: '3',
    name: '王五',
    birthday: '1988-12-25',
    registerTime: '2022-03-01 14:20:00',
    lastLoginTime: '2024-03-18 10:15:00',
    remark: '示例数据3'
  },
  {
    id: '4',
    name: '赵六',
    birthday: '1995-08-15',
    registerTime: '2023-01-10 16:40:00',
    lastLoginTime: '2024-03-17 09:20:00',
    remark: '示例数据4'
  },
  {
    id: '5',
    name: '钱七',
    birthday: '1993-03-30',
    registerTime: '2022-12-25 11:15:00',
    lastLoginTime: '2024-03-16 14:50:00',
    remark: '示例数据5'
  },
  {
    id: '6',
    name: '孙八',
    birthday: '1991-11-11',
    registerTime: '2023-06-05 08:30:00',
    lastLoginTime: '2024-03-15 17:25:00',
    remark: '示例数据6'
  },
  {
    id: '7',
    name: '周九',
    birthday: '1994-07-07',
    registerTime: '2023-09-20 13:45:00',
    lastLoginTime: '2024-03-14 11:35:00',
    remark: '示例数据7'
  }
];

export default () => {
  const [tableData, setTableData] = React.useState(data);


const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name', 
    editor: { 
      type: 'text',
      props: {
        maxLength: 5,
        placeholder: '请输入姓名',
      },
      onEditComplete: (value: CellValue, record: TableRecord) =>
        handleSave(value, record, 'name'),
    },
    width: 120,
  },
  {
    title: '生日',
    dataIndex: 'birthday',
    key: 'birthday',
    width: 150,
    editor: { 
      type: 'date',
      props: {
        format: 'YYYY-MM-DD',
        placeholder: '请选择生日'
      },
      onEditComplete: (value: CellValue, record: TableRecord) =>
        handleSave(value, record, 'birthday'),
    }
  },
  {
    title: '注册时间',
    dataIndex: 'registerTime',
    key: 'registerTime',
    width: 180,
    editor: { 
      type: 'date',
      props: {
        format: 'YYYY-MM-DD',
        placeholder: '请选择注册时间',
        showTime: true
      },
      onEditComplete: (value: CellValue, record: TableRecord) =>
        handleSave(value, record, 'registerTime'),
    }
  },
  {
    title: '最后登录',
    dataIndex: 'lastLoginTime',
    key: 'lastLoginTime',
    width: 180,
    editor: { 
      type: 'date',
      props: {
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: '请选择最后登录时间',
        showTime: {
          defaultValue: moment('00:00:00', 'HH:mm:ss')
        }
      },
      onEditComplete: (value: CellValue, record: TableRecord) =>
        handleSave(value, record, 'lastLoginTime'),
    }
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
  },
];

  const handleSave = (value: CellValue, record: TableRecord, dataIndex: string) => {
    setTableData(prevData => 
      prevData.map(item => 
        item.id === record.id ? { ...item, [dataIndex]: value } : item
      )
    );
    return Promise.resolve(true);
  };

  return (
    <div>
      <InfinityTable
        columns={columns}
        dataSource={tableData}
        rowKey="id"
        width="100%"
      />
    </div>
  );
};