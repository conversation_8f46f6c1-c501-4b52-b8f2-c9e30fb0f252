/**
 * height: 600
 */
import React from 'react';
import { message } from 'antd';
import { InfinityTable } from '@xc/infinity-table';

interface TableRecord {
  id: string;
  priceRange: {
    min: number;
    max: number;
  };
  weightRange: {
    min: number;
    max: number;
  };
  ageRange: {
    min: number;
    max: number;
  };
  info: string;
}



const data: TableRecord[] = [
  {
    id: '1',
    priceRange: { min: 99.99, max: 199.99 },
    weightRange: { min: 0.5, max: 2.5 },
    ageRange: { min: 18, max: 30 },
    info: '示例数据1'
  },
  {
    id: '2',
    priceRange: { min: 299.99, max: 599.99 },
    weightRange: { min: 1.0, max: 5.0 },
    ageRange: { min: 25, max: 45 },
    info: '示例数据2'
  },
  {
    id: '3',
    priceRange: { min: 999.99, max: 1999.99 },
    weightRange: { min: 2.0, max: 10.0 },
    ageRange: { min: 30, max: 60 },
    info: '示例数据3'
  }
];

export default () => {
  const [tableData, setTableData] = React.useState(data);

  const columns = [
    {
      title: '价格区间(0~200)',
      dataIndex: 'priceRange',
      key: 'priceRange',
      width: 220,
      editor: {
        type: 'number-range',
        saveOnBlur: false,
        props: {
          min: 0,
          max: 200,
          precision: 2,
          unit: '元',
          placeholder: ['最小价格', '最大价格']
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'priceRange')
      }
    },
    {
      title: '重量区间(0~1000)',
      dataIndex: 'weightRange',
      key: 'weightRange',
      width: 220,
      editor: {
        type: 'number-range',
        props: {
          min: 0,
          max: 1000,
          precision: 1,
          unit: 'kg',
          placeholder: ['最小重量', '最大重量']
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'weightRange')
      }
    },
    {
      title: '年龄区间(6~99)',
      dataIndex: 'ageRange',
      key: 'ageRange',
      width: 200,
      editor: {
        type: 'number-range',
        props: {
          min: 6,
          max: 99,
          placeholder: ['最小年龄', '最大年龄']
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'ageRange')
      }
    },
    {
      title: '其他信息',
      dataIndex: 'info',
      key: 'info',
      render: (text: string) => {
        return <span>{text}</span>;
      },
    },
  ];

  const handleSave = async (value: CellValue, record: TableRecord, dataIndex: string): Promise<boolean> => {
    try {
      // 更新本地数据
      setTableData(prevData => 
        prevData.map(item => 
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );

      message.success('保存成功');
      return true;
    } catch (error) {
      message.error('保存失败');
      return false;
    }
  };

  return (
    <div>
      <InfinityTable
        columns={columns}
        dataSource={tableData}
        rowKey="id"
        width="100%"
      />
    </div>
  );
};