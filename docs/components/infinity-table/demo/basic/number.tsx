/**
 * height: 600
 */
import React from 'react';
import { InfinityTable } from '@xc/infinity-table';
import { message } from 'antd';

interface TableRecord {
  id: string;
  amount: number;
  price: number;
  total: number;
}

type CellValue = number;


const data = [
  {
    id: '1',
    amount: 10,
    price: 99.99,
    total: 999.90,
  },
  {
    id: '2', 
    amount: 5,
    price: 199.99,
    total: 999.95,
  },
  {
    id: '3',
    amount: 2,
    price: 299.99,
    total: 599.98,
  }
];

export default () => {
  const [tableData, setTableData] = React.useState(data);

  const columns = [
    {
      title: '数量(无单位 1~100)',
      dataIndex: 'amount',
      key: 'amount',
      editor: {
        type: 'number',
        props: {
          min: 1,
          max: 100,
          placeholder: '请输入数量',
        },
        onEditComplete: (value: CellValue, record: TableRecord) =>
          handleSave(value, record, 'amount'),
      },
      width: 150,
    },
    {
      title: '单价',
      dataIndex: 'price',
      key: 'price',
      width: 150,
      editor: {
        type: 'number',
        props: {
          precision: 2,
          suffix: '元',
          placeholder: '请输入单价',
        },
        onEditComplete: (value: CellValue, record: TableRecord) =>
          handleSave(value, record, 'price'),
      },
    },
    {
      title: '重量',
      dataIndex: 'total',
      key: 'total',
      width: 150,
      editor: {
        type: 'number',
        props: {
          precision: 2,
          suffix: 'kg',
        },
        onEditComplete: (value: CellValue, record: TableRecord) =>
          handleSave(value, record, 'total'),
      },
    },
    {
      title: '其他',
      dataIndex: 'options',
      key: 'options',
      render: () => {
        return <span>...</span>;
      },
    },
  ];
  

  const handleSave = async (
    value: CellValue,
    record: TableRecord,
    dataIndex: string
  ): Promise<void> => {
    try {
      console.log(
        `保存 ${dataIndex}: ${value} 到记录 ID: ${record.id}`
      );

      setTableData((prevData) =>
        prevData.map((item) =>
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );

      message.success('保存成功');
    } catch (error) {
      message.error('保存失败');
      throw error;
    }
  };

  return (
    <InfinityTable
      columns={columns}
      dataSource={tableData}
      rowKey="id"
    />
  );
};