import React, { useRef, useImperativeHandle, Component } from 'react';
import { message } from 'antd';
import { InfinityTable } from '@xc/infinity-table';
import type { CellValue } from '../../../../src/components/InfinityTable/types';

// 模拟图片上传API
const mockUploadApi = {
  upload: async (file: File) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      success: true,
      url: `https://s.xinc818.com/files/webcim3zonla0auicrk/111ailwindui.png?${Date.now()}`
    };
  }
};

interface TableRecord {
  id: string;
  images1: string[];
  images2: string[];
  customImage: string[];
}

const data: TableRecord[] = [
  {
    id: '1',
    images1: ['https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png'],
    images2: ['https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png'],
    customImage: ['https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png','https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png','https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png'],
  },
  {
    id: '2',
    images1: ['https://gw.alipayobjects.com/zos/antfincdn/LlvErxo8H9/photo-1503185912284-5271ff81b9a8.webp'],
    images2: ['https://gw.alipayobjects.com/zos/antfincdn/cV16ZqzMjW/photo-1473091540282-9b846e7965e3.webp'],
    customImage: ['https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png','https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png','https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png'],
  },
];

// 修改自定义图片编辑器组件，添加 ref 支持
const CustomImageEditor = React.forwardRef((props, ref) => {
  const { value, onChange } = props;

  useImperativeHandle(ref, () => ({
    clear: () => {
      onChange?.([]);
    }
  }));

  const handleUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      try {
        const response = await mockUploadApi.upload(file);
        if (response.success) {
          onChange?.([...(value || []), response.url]);
        }
      } catch (error) {
        message.error('上传失败');
      }
    }
  };

  return (
    <div style={{ 
      display: 'flex', 
      flexWrap: 'wrap',
      alignItems: 'center', 
      gap: '12px',
      padding: '12px',
      background: '#fff',
      borderRadius: '6px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
      minWidth: '400px',
      maxWidth: '800px'
    }}>
      <div style={{
        display: 'flex',
        gap: '12px',
        flexShrink: 0
      }}>
        <button
          style={{
            padding: '8px 16px',
            background: '#1890ff',
            color: '#fff',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            whiteSpace: 'nowrap'
          }}
          onClick={() => document.getElementById('file-upload')?.click()}
        >
          <svg viewBox="64 64 896 896" focusable="false" data-icon="upload" width="1em" height="1em" fill="currentColor">
            <path d="M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z" />
          </svg>
          上传图片
        </button>
        
        {value && value?.length > 0 && (
          <button
            onClick={() => onChange?.([])}
            style={{
              padding: '8px 16px',
              background: '#fff',
              color: '#ff4d4f',
              border: '1px solid #ff4d4f',
              borderRadius: '4px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              whiteSpace: 'nowrap'
            }}
          >
            <svg viewBox="64 64 896 896" focusable="false" data-icon="delete" width="1em" height="1em" fill="currentColor">
              <path d="M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z" />
            </svg>
            清除
          </button>
        )}
        <input
          id="file-upload"
          type="file"
          accept="image/*"
          onChange={handleUpload}
          style={{ display: 'none' }}
        />
      </div>

      {/* 图片预览区域 */}
      {value && value.length > 0 && (
        <div style={{ 
          display: 'flex', 
          flexWrap: 'wrap',
          gap: '8px', 
          alignItems: 'center',
          flex: 1,
          minWidth: '200px'
        }}>
          {value.map((url, index) => (
            <div 
              key={index}
              style={{
                width: '48px',
                height: '48px',
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
                overflow: 'hidden'
              }}
            >
              <img 
                src={url} 
                alt={`preview-${index}`}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
});

export default () => {
  const [tableData, setTableData] = React.useState(data);

  const handleSave = async (
    value: CellValue,
    record: TableRecord,
    dataIndex: string
  ): Promise<boolean> => {
    try {
      setTableData(prevData =>
        prevData.map(item =>
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );
      message.success('保存成功');
      return true;
    } catch (err) {
      message.error('保存失败');
      return false;
    }
  };

  const columns = [
    {
      title: '商品图片（最多3张）',
      dataIndex: 'images1',
      key: 'images1',
      width: 160,
      editor: { 
        type: 'images',
        props: {
          maxCount: 3,
          accept: 'image/*',
          maxSize: 5 * 1024 * 1024,
          customRequest: async ({ file, onSuccess, onError }) => {
            try {
              const response = await mockUploadApi.upload(file);
              if (response.success) {
                onSuccess?.(response);
              } else {
                onError?.(new Error('上传失败'));
              }
            } catch (error) {
              onError?.(error as Error);
            }
          }
        },
        overlayProps: {
          showClear: true,
          onClear: ({ onChange }) => {
            onChange([]);
          }
        },
        onEditComplete: (value: CellValue, record: TableRecord) => 
          handleSave(value, record, 'images1'),
      }
    },
    {
      title: '商品图片（最多5张）',
      dataIndex: 'images2',
      key: 'images2',
      width: 160,
      editor: {
        type: 'images',
        props: {
          maxCount: 5,
          accept: 'image/*',
          maxSize: 5 * 1024 * 1024,
          customRequest: async ({ file, onSuccess, onError }) => {
            try {
              const response = await mockUploadApi.upload(file);
              if (response.success) {
                onSuccess?.(response);
              } else {
                onError?.(new Error('上传失败'));
              }
            } catch (error) {
              onError?.(error as Error);
            }
          }
        },
        overlayProps: {
          showClear: true,
          onClear: ({ onChange }) => {
            onChange([]);
          }
        },
        onEditComplete: (value: CellValue, record: TableRecord) => 
          handleSave(value, record, 'images2'),
      }
    },
    {
      title: '自定义编辑器',
      dataIndex: 'customImage',
      key: 'customImage',
      width: 133,
      editor: {
        type: 'images',
        props: {
          maxCount: 3,
          // editorRender: (props: any, ref: any) => {
          //   console.log('ref~~', props);
          //   return <CustomImageEditor {...props} ref={ref} />;
          // }
        },
        editorRender:CustomImageEditor,
        overlayProps: {
          showClear: true,
          onClear: ({ editor, onChange }) => {
            console.log('ref~~', editor,onChange);
            if (editor?.current?.clear) {
              editor.current.clear();
            }
             // onChange([]); 如果内置clear方法没调用onChange的话，需要手动调用
          }
        },
        onEditComplete: (value: CellValue, record: TableRecord) => 
          handleSave(value, record, 'customImage'),
      }
    },
    {
      title: '其他',
      dataIndex: 'options',
      key: 'options',
      render: () => {
        return <span>...</span>;
      },
    },
  ];

  return (
    <div>
      <InfinityTable<TableRecord>
        columns={columns}
        dataSource={tableData}
        rowKey="id"
        width="100%"
      />
    </div>
  );
};
