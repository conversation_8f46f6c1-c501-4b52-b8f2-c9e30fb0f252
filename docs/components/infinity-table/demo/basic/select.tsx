/**
 * height: 600
 */
import React from 'react';
import { message } from 'antd';
import { InfinityTable } from '@xc/infinity-table';

type CellValue = string;

interface TableRecord {
  id: string;
  status: string;
  priority: string;
  category: string;
  info: string;
}

// 定义选项常量
const STATUS_OPTIONS = [
  { value: '0', label: '待处理' },
  { value: '1', label: '处理中' },
  { value: '2', label: '已完成' },
];

const PRIORITY_OPTIONS = [
  { value: 'HIGH', label: '高高高高高高高高高高高高高高高高高高高高高高' },
  { value: 'MEDIUM', label: '中' },
  { value: 'LOW', label: '低' },
];

const CATEGORY_OPTIONS = [
  { value: 'FOOD', label: '食品' },
  { value: 'CLOTHING', label: '服装' },
  { value: 'ELECTRONICS', label: '电子' },
  { value: 'BOOKS', label: '图书' },
];



const data: TableRecord[] = [
  {
    id: '1',
    status: '',
    priority: 'HIGH',
    category: 'FOOD',
    info: '示例数据1'
  },
  {
    id: '2',
    status: '1',
    priority: 'MEDIUM',
    category: 'CLOTHING',
    info: '示例数据2'
  },
  {
    id: '3',
    status: '2',
    priority: 'LOW',
    category: 'ELECTRONICS',
    info: '示例数据3'
  },
  {
    id: '4',
    status: '1',
    priority: 'HIGH',
    category: 'BOOKS',
    info: '示例数据4'
  }
];

export default () => {
  const [tableData, setTableData] = React.useState(data);

  const handleSave = async (
    value: CellValue,
    record: TableRecord,
    dataIndex: string
  ): Promise<void> => {
    try {
      console.log(
        `Saving ${dataIndex} for record ${record.id} with value ${value}`
      );
  
      setTableData((prevData) =>
        prevData.map((item) =>
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );
  
      message.success("保存成功");
    } catch (error) {
      message.error("保存失败");
      throw error;
    }
  };
  
  const columns = [
    {
      title: '处理状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      editor: {
        type: 'select',
        props: {
          options: STATUS_OPTIONS,
          placeholder: '请选择状态'
        },
        overlayProps: {
          showClear: true,
          onClear: ({ onChange }) => {
            onChange([]);
          }
        },
        onEditComplete: (value: CellValue, record: TableRecord) =>
          handleSave(value, record, 'status')
      }
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 120,
      editor: { 
        type: 'select',
        props: {
          options: PRIORITY_OPTIONS,
          placeholder: '请选择优先级'
        },
        overlayProps: {
          showClear: true,
          onClear: ({ onChange }) => {
            onChange([]);
          }
        },
        onEditComplete: (value: CellValue, record: TableRecord) =>
          handleSave(value, record, 'priority')
      }
    },
    {
      title: '商品类别',
      dataIndex: 'category',
      key: 'category',
      width: 140,
      editor: { 
        type: 'select',
        props: {
          options: CATEGORY_OPTIONS,
          placeholder: '请选择类别'
        },
        overlayProps: {
          showClear: true,
          onClear: ({ onChange }) => {
            onChange([]);
          }
        },
        onEditComplete: (value: CellValue, record: TableRecord) =>
          handleSave(value, record, 'category')
      }
    },
    {
      title: '其他信息',
      dataIndex: 'info',
      key: 'info',
      render: (text: string) => {
        return <span>{text}</span>;
      },
    },
  ];

  return (
    <InfinityTable
      columns={columns}
      dataSource={tableData}
      rowKey="id"
      debug={{ enabled: true }}
      width="100%"
    />
  );
};