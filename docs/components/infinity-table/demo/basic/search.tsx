/**
 * height: 600
 */
import React from 'react';
import { message } from 'antd';
import { InfinityTable } from '@xc/infinity-table';

interface TableRecord {
  id: string;
  product: string;
  supplier: string;
  category: string;
  info: string;
}

// 生成带随机数的标签
const generateLabel = (base: string) => `${base} #${Math.floor(Math.random() * 1000)}`;

// 模拟搜索API
const mockSearchApi = {
  // 动态搜索产品
  searchProducts: async (params: SearchParams) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const allProducts = [
      { value: 'honey_bee', label: generateLabel('小蜜蜂牌~') },
      { value: 'honey_pollen', label: generateLabel('小蜜蜂牌花粉') },
      { value: 'royal_jelly', label: generateLabel('蜂王浆') },
      { value: 'mature_honey', label: generateLabel('成熟蜂蜜') },
      { value: 'wild_honey', label: generateLabel('野生蜂蜜') },
      { value: 'raw_honey', label: generateLabel('土蜂蜜') },
      { value: 'linden_honey', label: generateLabel('椴树蜜') },
      { value: 'mixed_honey', label: generateLabel('百花蜜') },
      { value: 'citrus_honey', label: generateLabel('柑橘蜜') },
      { value: 'vitex_honey', label: generateLabel('荆条蜜') },
      { value: 'date_honey', label: generateLabel('枣花蜜') },
      { value: 'acacia_honey', label: generateLabel('槐花蜜') },
      { value: 'longan_honey', label: generateLabel('龙眼蜜') },
      { value: 'dandelion_honey', label: generateLabel('蒲公英蜜') },
      { value: 'clover_honey', label: generateLabel('紫云英蜜') },
    ];
    
    // params 包含搜索参数:
    // - keyword: 搜索关键词
    // - field: 当前搜索的字段名
    // - record: 当前行的完整数据记录
    return allProducts.filter(item => 
      item.label.toLowerCase().includes(params.keyword.toLowerCase())
    );
  },
  
  // 动态搜索供应商
  searchSuppliers: async (params: SearchParams) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log('~searchSuppliers', params)
    const allSuppliers = [
      { value: 'hangzhou_bee', label: generateLabel('杭州小蜜蜂养殖场杭州小蜜蜂养殖场') },
      { value: 'hangzhou_honey', label: generateLabel('杭州蜂蜜专业户') },
      { value: 'nanjing_base', label: generateLabel('南京蜂产品基地') },
      { value: 'anhui_coop', label: generateLabel('安徽蜂蜜合作社') },
      { value: 'shandong_corp', label: generateLabel('山东蜂产品公司') },
      { value: 'henan_farm', label: generateLabel('河南养蜂基地') },
      { value: 'hangzhou_highland', label: generateLabel('杭州高原蜂场') },
      { value: 'sichuan_park', label: generateLabel('四川蜂蜜产业园') },
      { value: 'guangxi_market', label: generateLabel('广西蜂蜜集散地') },
      { value: 'jiangxi_factory', label: generateLabel('江西蜂蜜加工厂') },
    ];
    
    return allSuppliers.filter(item => 
      item.label.toLowerCase().includes(params.keyword.toLowerCase())
    );
  }
};

const data: TableRecord[] = [
  {
    id: '1',
    product: { value: 'honey_bee', label: '小蜜蜂牌~' },
    supplier: { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场杭州小蜜蜂养殖场' },
    category: 'FOOD',
    info: '示例数据1'
  },
  {
    id: '2',
    product: { value: 'honey_pollen', label: '小蜜蜂牌花粉' },
    supplier: { value: 'hangzhou_honey', label: '杭州蜂蜜专业户' },
    category: 'FOOD',
    info: '示例数据2'
  },
  {
    id: '3',
    product: { value: 'honey_bee', label: '小蜜蜂牌~' },
    supplier: { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场' },
    category: 'FOOD',
    info: '示例数据3'
  },
  {
    id: '4',
    product: { value: 'honey_bee', label: '小蜜蜂牌~' },
    supplier: { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场' },
    category: 'FOOD',
    info: '示例数据4'
  },
  {
    id: '5',
    product: { value: 'honey_bee', label: '小蜜蜂牌~' },
    supplier: { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场' },
    category: 'FOOD',
    info: '示例数据5'
  }
];

export default () => {
  const [tableData, setTableData] = React.useState(data);

  const handleSave = async (value: CellValue, record: TableRecord, dataIndex: string): Promise<boolean> => {
    try {
      console.log('Saving:', { value, record, dataIndex });  // 添加日志
      setTableData(prevData => 
        prevData.map(item => 
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );
      message.success('保存成功');
      return true;
    } catch (error) {
      message.error('保存失败');
      return false;
    }
  };

  const columns = [
    {
      title: '产品名称（搜索:蜜蜂）',
      dataIndex: 'product',
      key: 'product',
      width: 220,
      editor: {
        type: 'search-select',
        props: {
          placeholder: '搜索产品',
          onSearch: mockSearchApi.searchProducts,
          initialOptions: [
            { value: 'honey_bee', label: '小蜜蜂牌蜂蜜' },
            { value: 'honey_pollen', label: '小蜜蜂牌花粉' },
            { value: 'royal_jelly', label: '蜂王浆' },
            { value: 'mature_honey', label: '成熟蜂蜜' },
            { value: 'wild_honey', label: '野生蜂蜜' },
          ]
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'product')
      },
    },
    {
      title: '供应商（搜索:杭州）',
      dataIndex: 'supplier',
      key: 'supplier',
      width: 220,
      editor: {
        type: 'search-select',
        props: {
          placeholder: '搜索供应商',
          onSearch: mockSearchApi.searchSuppliers,
          initialOptions: [
            { value: 'hangzhou_bee', label: '杭州小蜜蜂养殖场' },
            { value: 'hangzhou_honey', label: '杭州蜂蜜专业户' },
            { value: 'nanjing_base', label: '南京蜂产品基地' },
          ]
        },
        onEditComplete: (value: CellValue, record: TableRecord) => handleSave(value, record, 'supplier')
      }
    },
    {
      title: '其他信息',
      dataIndex: 'info',
      key: 'info',
      render: (text: string) => {
        return <span>{text}</span>;
      },
    },
  ];

  return (
    <div>
      <h3>搜索选择器示例：</h3>
      <ul>
        <li>产品名称：演示动态搜索模式，搜索时从服务端获取数据</li>
        <li>供应商：演示动态搜索模式，搜索时从服务端获取数据</li>
        <li>支持模糊搜索，匹配文本会高亮显示</li>
        <li>支持键盘上下键选择</li>
      </ul>
      <InfinityTable
        columns={columns}
        dataSource={tableData}
        rowKey="id"
        bordered
        width="100%"
        debug={{ enabled: true }}
      />
    </div>
  );
}; 