import React, { useState, useEffect, useRef } from 'react';
import { Tag } from 'antd';

const TagDemo: React.FC = () => {
  const [expanded, setExpanded] = useState(false);
  const [showButton, setShowButton] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const tagsRef = useRef<HTMLDivElement>(null);

  // 模拟不同长度的标签数据
  const tags = [
    '电子科技', '人工智能', '互联网', '大数据分析与挖掘', 
    '云计算服务', '物联网技术', '区块链', '智能制造',
    '新能源科技', '医疗器械', '生物科技', '环保节能',
    '移动互联', '软件开发', '网络安全', '数字营销',
    '电子商务', '金融科技', '智慧城市', '5G通信',
    '虚拟现实', '增强现实', '机器学习', '深度学','你好啊',
    '自动驾驶', '量子计算', '边缘计算', '信息安全',
    '半导体', '芯片设计', '通信技术', '工业自动化',
    '新能源科技', '医疗器械', '生物科技', '环保节能',
    '移动互联', '软件开发', '网络安全', '数字营销',
    '电子商务', '金融科技', '智慧城市', '5G通信',
    '虚拟现实', '增强现实', '机器学习', '深度学习','我好啊啊123',
    '机器人技术', '传感器', '微电子', '光电技术',
  ];

  useEffect(() => {
    const checkOverflow = () => {
      if (containerRef.current && tagsRef.current) {
        const tagsHeight = tagsRef.current.scrollHeight;
        const lineHeight = 32; // 每行大约高度
        
        if (tagsHeight > lineHeight * 2) {
          setShowButton(true);
        } else {
          setShowButton(false);
        }
      }
    };

    checkOverflow();
    window.addEventListener('resize', checkOverflow);
    return () => window.removeEventListener('resize', checkOverflow);
  }, []);

  return (
    <div 
      ref={containerRef}
      style={{
        position: 'relative',
        border: '1px solid #f0f0f0',
        padding: '8px',
        borderRadius: '4px',
      }}
    >
      <div
        style={{
          maxHeight: expanded ? 'none' : '64px',
          overflow: 'hidden',
          position: 'relative',
        }}
      >
            {showButton && !expanded && (
          <div
          className='nccc'
            style={{
              float: 'right',
              width: '60px', // 按钮实际宽度
              height: '32px', // 按钮实际高度
              position: 'relative',
              top: '32px', // 将占位元素下移到第二行
              // 使用 polygon 创建一个实际的按钮区域
              shapeOutside: 'polygon(0px 0px, 44px 0px, 44px 22px, 0px 22px)',
            }}
          />
        )}
        
        <div 
          ref={tagsRef}
          style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '8px',
          }}
        >
          {tags.map((tag, index) => (
            <Tag 
              key={index}
              style={{ 
                marginBottom: 0,
                lineHeight: '24px',
                height: '24px',
              }}
            >
              {tag}
            </Tag>
          ))}
        </div>

        {showButton && !expanded && (
          <div
            style={{
              position: 'absolute',
              right: 0,
              bottom: 0,
              height: '32px',
              width: '60px',
              background: 'linear-gradient(to right, transparent, white 30%)',
              pointerEvents: 'none',
            }}
          />
        )}
      </div>

      {showButton && (
        <button
          onClick={() => setExpanded(!expanded)}
          style={{
            position: 'absolute',
            right: 12,
            bottom: 12,
            border: 'none',
            background: 'white',
            color: '#1890ff',
            cursor: 'pointer',
            padding: '0 8px',
            fontSize: '14px',
          }}
        >
          {expanded ? '收起' : '展开'}
        </button>
      )}
    </div>
  );
};

export default TagDemo;