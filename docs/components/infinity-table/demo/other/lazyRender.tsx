import React, { useState } from 'react';
import { InfinityTable } from '@xc/infinity-table';
import type { ColumnType } from '../../../../src/components/InfinityTable/types/index';

interface DataType extends Record<string, unknown> {
  id: string;
  name: string;
  age: number;
  address: string;
  description: string;
  longText: string;
}

// 生成模拟数据
const generateData = (count: number): DataType[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: index.toString(),
    name: `Name ${index}`,
    age: Math.floor(Math.random() * 50) + 20,
    address: `Street ${index}, City ${Math.floor(index / 10)}`,
    description: `This is a description for person ${index}. ${Array(5).fill(`More details about ${index}.`).join(' ')}`,
    longText: Array(20).fill(`Long text content ${index}`).join(' '),
  }));
};

const LazyRenderDemo: React.FC = () => {
  const [data] = useState<DataType[]>(() => generateData(100));

  const columns: ColumnType<DataType>[] = [
    {
      key: 'name',
      title: '姓名',
      dataIndex: 'name',
      width: 150,
    },
    {
      key: 'age',
      title: '年龄',
      dataIndex: 'age',
      width: 100,
    },
    {
      key: 'address',
      title: '地址',
      dataIndex: 'address',
      width: 200,
    },
    {
      key: 'description',
      title: '描述',
      dataIndex: 'description',
      width: 400,
    },
    {
      key: 'longText',
      title: '长文本',
      dataIndex: 'longText',
      width: 800,
    },
  ];

  return (
    <div>
      <h3>虚拟滚动 + 分片加载示例：</h3>
      <div>
        <InfinityTable<DataType>
          columns={columns}
          dataSource={data}
          rowKey="id"
          scroll={{ x: 'max-content', y: 500 }}
          pagination={false}
          infinityLoad={{
            enabled: true
          }}
        />
      </div>
    </div>
  );
};

export default LazyRenderDemo;
