/**
 * height: 600
 */
import React from 'react';
import { message } from 'antd';
import { InfinityTable } from '@xc/infinity-table';
import type { CellValue } from '../../../../src/components/InfinityTable/types';

// 模拟API请求
const mockApi = {
  updateTableData: async (params: {
    rowKey: string;
    dataIndex: string;
    value: any;
  }) => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 模拟随机错误
    if (Math.random() < 0.1) { // 10%的概率失败
      throw new Error('模拟更新失败');
    }

    return { success: true };
  }
};

// 模拟检查名称是否存在的API
const checkNameExists = async (name: string): Promise<boolean> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // 模拟已存在的名称列表
  const existingNames = ['张三', '李四', '王五', '赵六', '钱七'];
  
  return existingNames.includes(name);
};

// 更多的测试数据
const data = [
  {
    id: '1',
    name: '张三',
    nickname: '小张',
    address: '浙江省杭州市',
  },
  {
    id: '2',
    name: '李四',
    nickname: '小李',
    address: '浙江省宁波市',
  },
  {
    id: '3',
    name: '王五',
    nickname: '小王',
    address: '上海市',
  },
  {
    id: '4',
    name: '赵六',
    nickname: '小赵',
    address: '北京市',
  },
  {
    id: '5',
    name: '钱七',
    nickname: '��',
    address: '广州市',
  }
];

export default () => {
  const [tableData, setTableData] = React.useState(data);

  // 处理保存
  const handleSave = async (
    value: CellValue,
    record: any,
    dataIndex: string
  ): Promise<void> => {
    try {
      // 调用模拟 API
      await mockApi.updateTableData({
        rowKey: record.id,
        dataIndex,
        value
      });

      // 更新本地数据
      setTableData(prevData =>
        prevData.map(item =>
          item.id === record.id ? { ...item, [dataIndex]: value } : item
        )
      );

      message.success('保存成功');
    } catch (error) {
      message.error('保存失败');
      throw error; // 抛出错误，让上层知道保存失败
    }
  };

  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      editor: {
        type: 'text',
        validators: {
          required: {
            value: true,
            message: '名称不能为空'
          },
          custom: async (value) => {
            // 长度校验
            if (String(value).length > 10) {
              return {
                valid: false,
                message: '名称长度不能超过10个字符'
              };
            }

            // 唯一性校验
            try {
              const exists = await checkNameExists(value);
              if (exists) {
                return {
                  valid: false,
                  message: '该名称已被使用'
                };
              }
            } catch (error) {
              return {
                valid: false,
                message: '验证失败，请重试'
              };
            }

            // 其他业务规则校验
            if (value === 'admin') {
              return {
                valid: false,
                message: '不能使用保留名称'
              };
            }

            return { valid: true };
          }
        },
        props: {
          maxLength: 12,
          placeholder: '请输入姓名',
        },
        onEditComplete: (value: CellValue, record: any) => handleSave(value, record, 'name'),
      },
      width: 150,
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
      key: 'nickname',
      width: 150,
      editor: {
        type: 'text',
        props: {
          maxLength: 10,
          placeholder: '请输入你的花名',
        },
        onEditComplete: (value: CellValue, record: any) => handleSave(value, record, 'nickname'),
      },
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      editor: {
        type: 'text',
        props: {
          placeholder: '请输入地址',
        },
        onEditComplete: (value: CellValue, record: any) => handleSave(value, record, 'address'),
      },
      width: 200,
    },
  ];

  return (
    <div>
      <h3>可编辑表格示例：</h3>
      <ul>
        <li>双击单元格进入编辑状态</li>
        <li>编辑完成后自动保存</li>
        <li>有 10% 的概率保存失败（用于测试）</li>
        <li>支持全局编辑完成回调</li>
      </ul>
      <InfinityTable
        columns={columns}
        dataSource={tableData}
        debug={{ enabled: true }}
        rowKey="id"
        bordered
        height={400}
        width="100%"
        onEditStateChange={async ({ value, record, column, type }) => {
          switch(type) {
            case 'unchanged':
              console.log('值未变更');
              break;
            case 'invalid':
              console.log('验证失败');
              break;
            case 'success':
              console.log('保存成功');
              break;
          }
          message.info(`编辑状态变更: ${type}`);
        }}
      />
    </div>
  );
};