---
hero:
  title: infinity-table
  description: 基于 Antd Table 的高性能可编辑表格组件
  actions:
    - text: 快速上手
      link: /guide/getting-started
    - text: 组件介绍
      link: /components/infinity-table
features:
  - title: 灵活的编辑模式
    emoji: 💎
    description: 支持单元格内联编辑和弹出式编辑,可自由切换编辑模式
  - title: 丰富的编辑器
    emoji: 🌈
    description: 内置文本、数字、日期、图片等多种编辑器,支持自定义扩展
  - title: 高性能展示
    emoji: 🚀
    description: 采用虚拟滚动技术,轻松应对大数据量场景
  - title: 强大的列管理
    emoji: 🔧
    description: 支持列拖拽排序、显示/隐藏、固定列等特性
  - title: 丰富的展示组件
    emoji: 🎨
    description: 内置多种数据展示组件,满足不同展示需求
  - title: TypeScript 支持
    emoji: 💪
    description: 使用 TypeScript 开发,提供完整的类型定义
---

