{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@table/*": ["src/components/InfinityTable/*"], "@components/*": ["src/components/InfinityTable/components/*"], "@utils/*": ["src/components/InfinityTable/utils/*"], "@types": ["src/components/InfinityTable/types/index"], "@constants/*": ["src/components/InfinityTable/constants/*"]}}, "include": ["src"]}