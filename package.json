{"name": "@xc/infinity-table", "private": false, "version": "1.0.1-alpha.11", "description": "Infinity1429", "type": "module", "files": ["dist", "lib", "es"], "main": "lib/index.js", "module": "es/index.js", "types": "es/index.d.ts", "scripts": {"start": "dumi dev", "ui": "playwright test --ui", "ra": "npm version prerelease --preid=alpha && npm publish --tag alpha", "docs:build": "dumi build", "docs:deploy": "gh-pages -d docs-dist", "deploy": "npm run docs:build && npm run docs:deploy", "dev": "vite", "clean": "rimraf lib es dist", "build": "npm run clean && father build", "prepublishOnly": "npm run build", "prepare2": "dumi setup", "doctor": "father doctor", "prettier": "prettier --write \"**/*.{js,jsx,tsx,ts,less,md,json}\"", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug"}, "dependencies": {"@ant-design/icons": "^4.8.0", "ahooks": "^3.8.1", "antd": "4.24.12", "classnames": "^2.5.1", "dayjs": "^1.11.10", "immutability-helper": "^3.1.1", "less": "^4.2.0", "less-loader": "^12.2.0", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "^18.3.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-router-dom": "^6.27.0"}, "devDependencies": {"@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@eslint/js": "^9.13.0", "@million/lint": "^1.0.14", "@playwright/test": "^1.42.1", "@types/lodash": "^4.14.195", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/react-window": "^1.8.8", "@umijs/lint": "^4.0.0", "@vitejs/plugin-react": "^4.3.3", "babel-plugin-import": "^1.13.8", "dumi": "^2.2.0", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.13", "father": "^4.1.0", "gh-pages": "^3.2.3", "globals": "^15.11.0", "husky": "^8.0.1", "lint-staged": "^13.0.3", "playwright": "^1.42.1", "prettier": "^2.7.1", "prettier-plugin-organize-imports": "^3.0.0", "prettier-plugin-packagejson": "^2.2.18", "typescript": "~5.6.2", "typescript-eslint": "^8.10.0", "vite": "^5.4.9"}, "peerDependencies": {"antd": ">=4.24.0", "react": ">=16.9.0", "react-dom": ">=16.9.0"}, "publishConfig": {"access": "public"}, "authors": []}