"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.registry = exports.registerComponent = exports.getEditor = exports.getDisplay = void 0;
var _constants = require("../../constants");
var _DatePicker = require("../displays/DatePicker");
var _ImageGallery = require("../displays/ImageGallery");
var _MultiSelect = require("../displays/MultiSelect");
var _NumericLabel = require("../displays/NumericLabel");
var _NumberRange = require("../displays/NumberRange");
var _SearchSelect = require("../displays/SearchSelect");
var _Select = require("../displays/Select");
var _TextLabel = require("../displays/TextLabel");
var _DatePicker2 = require("../editors/DatePicker");
var _ImageUploader = require("../editors/ImageUploader");
var _MultiSelect2 = require("../editors/MultiSelect");
var _NumericField = require("../editors/NumericField");
var _NumberRange2 = require("../editors/NumberRange");
var _SearchSelect2 = require("../editors/SearchSelect");
var _Select2 = require("../editors/Select");
var _TextField = require("../editors/TextField");
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); } /**
 * 单元格显示组件导入
 * 按照字母顺序排列，便于维护和查找
 */ /**
 * 单元格编辑器组件导入
 * 按照字母顺序排列，与显示组件保持对应关系
 */
/**
 * 组件注册表类
 * 管理所有单元格的显示和编辑组件
 */
var ComponentRegistry = /*#__PURE__*/function () {
  function ComponentRegistry() {
    _classCallCheck(this, ComponentRegistry);
    _defineProperty(this, "components", new Map());
  }
  _createClass(ComponentRegistry, [{
    key: "register",
    value:
    /**
     * 注册一个新的组件类型
     * @param type - 组件类型标识符
     * @param component - 包含Display和Editor的组件对象
     */
    function register(type, component) {
      if (!type || !(component !== null && component !== void 0 && component.Display) || !(component !== null && component !== void 0 && component.Editor)) {
        throw new Error('Invalid component registration parameters');
      }
      this.components.set(type, component);
    }

    /**
     * 获取指定类型的显示组件
     * @param type - 组件类型标识符
     * @returns 对应的显示组件，如果未找到则返回undefined
     */
  }, {
    key: "getDisplayComponent",
    value: function getDisplayComponent(type) {
      var _this$components$get;
      return (_this$components$get = this.components.get(type)) === null || _this$components$get === void 0 ? void 0 : _this$components$get.Display;
    }

    /**
     * 获取指定类型的编辑器组件
     * @param type - 组件类型标识符
     * @returns 对应的编辑器组件，如果未找到则返回undefined
     */
  }, {
    key: "getEditorComponent",
    value: function getEditorComponent(type) {
      var _this$components$get2;
      return (_this$components$get2 = this.components.get(type)) === null || _this$components$get2 === void 0 ? void 0 : _this$components$get2.Editor;
    }
  }]);
  return ComponentRegistry;
}(); // 创建全局单例注册表实例
var registry = exports.registry = new ComponentRegistry();

/**
 * 注册所有内置组件
 * 使用对象映射方式，便于维护和扩展
 */
var registerBuiltInComponents = function registerBuiltInComponents() {
  var builtInComponents = _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, _constants.EDITOR_TYPES.TEXT, {
    Display: _TextLabel.TextDisplay,
    Editor: _TextField.TextEditor
  }), _constants.EDITOR_TYPES.NUMBER, {
    Display: _NumericLabel.NumberDisplay,
    Editor: _NumericField.NumberEditor
  }), _constants.EDITOR_TYPES.DATE, {
    Display: _DatePicker.DateDisplay,
    Editor: _DatePicker2.DateEditor
  }), _constants.EDITOR_TYPES.SELECT, {
    Display: _Select.SelectDisplay,
    Editor: _Select2.SelectEditor
  }), _constants.EDITOR_TYPES.SEARCH_SELECT, {
    Display: _SearchSelect.SearchSelectDisplay,
    Editor: _SearchSelect2.SearchSelectEditor
  }), _constants.EDITOR_TYPES.MULTI_SELECT, {
    Display: _MultiSelect.MultiSelectDisplay,
    Editor: _MultiSelect2.MultiSelectEditor
  }), _constants.EDITOR_TYPES.NUMBER_RANGE, {
    Display: _NumberRange.NumberRangeDisplay,
    Editor: _NumberRange2.NumberRangeEditor
  }), _constants.EDITOR_TYPES.IMAGES, {
    Display: _ImageGallery.ImagesDisplay,
    Editor: _ImageUploader.ImagesEditor
  });
  Object.entries(builtInComponents).forEach(function (_ref) {
    var _ref2 = _slicedToArray(_ref, 2),
      type = _ref2[0],
      component = _ref2[1];
    registry.register(type, component);
  });
};

// 初始化注册内置组件
registerBuiltInComponents();

/**
 * 便捷方法：获取编辑器组件
 * @param type - 组件类型标识符
 */
var getEditor = exports.getEditor = function getEditor(type) {
  return registry.getEditorComponent(type);
};

/**
 * 便捷方法：获取显示组件
 * @param type - 组件类型标识符
 */
var getDisplay = exports.getDisplay = function getDisplay(type) {
  return registry.getDisplayComponent(type);
};

/**
 * 注册自定义组件的方法
 * @param type - 自定义组件类型标识符
 * @param displayComponent - 自定义显示组件
 * @param editorComponent - 自定义编辑器组件
 * @throws 当参数无效时抛出错误
 */
var registerComponent = exports.registerComponent = function registerComponent(type, displayComponent, editorComponent) {
  if (!type || !displayComponent || !editorComponent) {
    throw new Error('Invalid custom component registration parameters');
  }
  registry.register(type, {
    Display: displayComponent,
    Editor: editorComponent
  });
};