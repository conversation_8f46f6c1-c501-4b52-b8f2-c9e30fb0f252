"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SelectEditor = void 0;
var _react = _interopRequireWildcard(require("react"));
require("./index.less");
var _jsxRuntime = require("react/jsx-runtime");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * @file Select Editor Component
 * @description 表格单元格下拉选择器编辑器组件
 * @review 2024/12/19
 */

/**
 * 下拉选项接口定义
 */

/**
 * Select编辑器属性接口
 */

/** 每个选项的固定高度（包含内边距），单位：像素 */
var OPTION_HEIGHT = 32;

/** 组件类名前缀 */
var PREFIX_CLS = 'infinity-table-select-editor';

/**
 * 下拉选择器编辑器组件
 */
var SelectEditor = exports.SelectEditor = function SelectEditor(_ref) {
  var value = _ref.value,
    onCellChange = _ref.onCellChange,
    _ref$options = _ref.options,
    options = _ref$options === void 0 ? [] : _ref$options,
    _ref$placeholder = _ref.placeholder,
    placeholder = _ref$placeholder === void 0 ? '请选择' : _ref$placeholder;
  // 计算下拉列表容器高度
  var listHeight = (0, _react.useMemo)(function () {
    return options.length * OPTION_HEIGHT + 2;
  }, [options.length]);

  // 处理选项点击事件
  var handleOptionClick = (0, _react.useCallback)(function (optionValue) {
    onCellChange === null || onCellChange === void 0 || onCellChange(optionValue);
  }, [onCellChange]);

  // 渲染选项列表
  var renderOptions = (0, _react.useCallback)(function () {
    return /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
      className: "options-list",
      children: options.map(function (option) {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
          className: "option-item ".concat(value === option.value ? 'selected' : ''),
          onClick: function onClick() {
            return handleOptionClick(option.value);
          },
          style: option.color ? {
            color: option.color
          } : undefined,
          title: option.label // 添加title属性，方便文本过长时查看完整内容
          ,
          children: option.label
        }, option.value);
      })
    });
  }, [options, value, handleOptionClick]);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
    className: PREFIX_CLS,
    style: {
      height: "".concat(listHeight, "px")
    },
    children: options.length > 0 ? renderOptions() : /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
      className: "placeholder",
      title: placeholder,
      children: placeholder
    })
  });
};

// 设置组件显示名称，方便调试
SelectEditor.displayName = 'SelectEditor';