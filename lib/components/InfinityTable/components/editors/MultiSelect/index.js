"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MultiSelectEditor = void 0;
var _react = _interopRequireWildcard(require("react"));
require("./index.less");
var _jsxRuntime = require("react/jsx-runtime");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }
function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; } /**
 * @file MultiSelect Editor Component
 * @description 表格单元格多选编辑器组件
 * @review 2024/12/19
 */ /** 选项高度配置（包含padding） */
var OPTION_HEIGHT = 32;

/** 选项数据结构定义 */

/** 组件属性定义 */

/**
 * 多选编辑器组件
 * @param props - 组件属性
 * @returns React组件
 */
var MultiSelectEditor = exports.MultiSelectEditor = function MultiSelectEditor(_ref) {
  var _ref$value = _ref.value,
    value = _ref$value === void 0 ? [] : _ref$value,
    onCellChange = _ref.onCellChange,
    _ref$options = _ref.options,
    options = _ref$options === void 0 ? [] : _ref$options,
    _ref$placeholder = _ref.placeholder,
    placeholder = _ref$placeholder === void 0 ? '请选择' : _ref$placeholder;
  // 将输入值统一转换为字符串数组
  var selectedValues = (0, _react.useMemo)(function () {
    if (!Array.isArray(value)) return [];
    return value.map(function (v) {
      return String(v);
    }).filter(Boolean);
  }, [value]);

  // 计算列表容器高度
  var listHeight = (0, _react.useMemo)(function () {
    return options.length * OPTION_HEIGHT + 2;
  }, [options.length]);

  // 处理选项点击事件
  var handleOptionClick = (0, _react.useCallback)(function (optionValue) {
    var newValues = selectedValues.includes(optionValue) ? selectedValues.filter(function (v) {
      return v !== optionValue;
    }) : [].concat(_toConsumableArray(selectedValues), [optionValue]);
    onCellChange === null || onCellChange === void 0 || onCellChange(newValues);
  }, [selectedValues, onCellChange]);

  // 渲染选项列表
  var renderOptions = (0, _react.useCallback)(function () {
    return options.map(function (option) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
        className: "option-item ".concat(selectedValues.includes(option.value) ? 'selected' : ''),
        onClick: function onClick() {
          return handleOptionClick(option.value);
        },
        style: option.color ? {
          color: option.color
        } : undefined,
        children: option.label
      }, option.value);
    });
  }, [options, selectedValues, handleOptionClick]);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
    className: "infinity-table-select-editor",
    style: {
      height: "".concat(listHeight, "px")
    },
    children: options.length > 0 ? /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
      className: "options-list",
      children: renderOptions()
    }) : /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
      className: "placeholder",
      children: placeholder
    })
  });
};

// 设置组件显示名称，用于调试
MultiSelectEditor.displayName = 'MultiSelectEditor';