.multi-select-editor {
  width: 100%;
  height: 100%;

  &:focus {
    box-shadow: none;
  }

  &:hover {
    background-color: #fafafa;
  }

  &[class$="-select"] {
    border: none;
  }

  &[class$="-select-focused"] {
    box-shadow: none;
  }
}

/* 确保下拉框在编辑器上方 */
[class$="-select-dropdown"] {
  z-index: 1100 !important;
}

/* 优化下拉框样式 */
.multi-select-editor {
  [class$="-select-selector"] {
    border: none !important;
    box-shadow: none !important;
    padding: 0 8px !important;
  }

  [class$="-select-selection-search"] {
    left: 8px !important;
  }

  [class$="-select-selection-placeholder"] {
    padding-left: 8px;
  }

  /* 标签样式 */
  [class$="-select-selection-item"] {
    margin: 2px;
    height: 22px;
    line-height: 20px;
  }
}