"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ImagesEditor = void 0;
require("antd/es/upload/style");
var _upload = _interopRequireDefault(require("antd/es/upload"));
require("antd/es/message/style");
var _message2 = _interopRequireDefault(require("antd/es/message"));
var _react = _interopRequireWildcard(require("react"));
var _icons = require("@ant-design/icons");
require("./index.less");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["value", "onCellChange", "style", "maxCount", "accept", "maxSize", "customRequest", "disabled"]; // 添加内部使用的类型
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }
function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }
function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }
var ImagesEditor = exports.ImagesEditor = function ImagesEditor(_ref) {
  var _ref$value = _ref.value,
    value = _ref$value === void 0 ? [] : _ref$value,
    onCellChange = _ref.onCellChange,
    style = _ref.style,
    _ref$maxCount = _ref.maxCount,
    maxCount = _ref$maxCount === void 0 ? 3 : _ref$maxCount,
    _ref$accept = _ref.accept,
    accept = _ref$accept === void 0 ? 'image/*' : _ref$accept,
    _ref$maxSize = _ref.maxSize,
    maxSize = _ref$maxSize === void 0 ? 5 * 1024 * 1024 : _ref$maxSize,
    customRequest = _ref.customRequest,
    disabled = _ref.disabled,
    props = _objectWithoutProperties(_ref, _excluded);
  // 确保 value 始终是数组
  var normalizedValue = _react.default.useMemo(function () {
    return Array.isArray(value) ? value : [];
  }, [value]);

  // 转换为文件列表
  var _React$useState = _react.default.useState(function () {
      return normalizedValue.map(function (url, index) {
        return {
          uid: "-".concat(index),
          name: "\u56FE\u7247".concat(index + 1),
          status: 'done',
          url: url,
          thumbUrl: url
        };
      });
    }),
    _React$useState2 = _slicedToArray(_React$useState, 2),
    internalFileList = _React$useState2[0],
    setInternalFileList = _React$useState2[1];

  // 同步外部值变化
  _react.default.useEffect(function () {
    var newFileList = normalizedValue.map(function (url, index) {
      return {
        uid: "-".concat(index),
        name: "\u56FE\u7247".concat(index + 1),
        status: 'done',
        url: url,
        thumbUrl: url
      };
    });
    setInternalFileList(newFileList);
  }, [normalizedValue]);

  // 处理文件大小检查
  var handleBeforeUpload = (0, _react.useCallback)(function (file) {
    try {
      if (maxSize && file.size > maxSize) {
        _message2.default.error("\u6587\u4EF6\u5927\u5C0F\u4E0D\u80FD\u8D85\u8FC7 ".concat(maxSize / 1024 / 1024, "MB"));
        return _upload.default.LIST_IGNORE;
      }
      if (accept) {
        var acceptTypes = accept.split(',').map(function (type) {
          return type.trim();
        });
        var isValidType = acceptTypes.some(function (type) {
          var regex = new RegExp(type.replace('*', '.*'));
          return regex.test(file.type);
        });
        if (!isValidType) {
          _message2.default.error("\u8BF7\u4E0A\u4F20 ".concat(accept, " \u683C\u5F0F\u7684\u6587\u4EF6"));
          return _upload.default.LIST_IGNORE;
        }
      }
      return true;
    } catch (error) {
      console.error('文件校验失败:', error);
      _message2.default.error('文件校验失败');
      return _upload.default.LIST_IGNORE;
    }
  }, [maxSize, accept]);

  // 处理移除文件
  var handleRemove = (0, _react.useCallback)(function (file) {
    var index = internalFileList.findIndex(function (item) {
      return item.uid === file.uid;
    });
    if (index > -1) {
      var newUrls = Array.isArray(value) ? _toConsumableArray(value) : [];
      newUrls.splice(index, 1);
      onCellChange === null || onCellChange === void 0 || onCellChange(newUrls);
    }
    return true;
  }, [value, internalFileList, onCellChange]);

  // 处理上传变化
  var handleChange = (0, _react.useCallback)(function (_ref2) {
    var file = _ref2.file,
      fileList = _ref2.fileList;
    // 更新内部状态
    setInternalFileList(fileList);

    // 仅在文件上传完成时更新外部值
    if (file.status === 'done' && file.response) {
      var newUrls = fileList.filter(function (file) {
        return file.status === 'done';
      }).map(function (file) {
        var _file$response;
        return ((_file$response = file.response) === null || _file$response === void 0 ? void 0 : _file$response.url) || file.url;
      }).filter(function (url) {
        return Boolean(url);
      });
      onCellChange === null || onCellChange === void 0 || onCellChange(newUrls);
    }
  }, [onCellChange]);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
    className: "infinity-table-images-editor",
    children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_upload.default, _objectSpread(_objectSpread({
      listType: "picture-card",
      fileList: internalFileList,
      beforeUpload: handleBeforeUpload,
      onRemove: handleRemove,
      customRequest: customRequest,
      accept: accept,
      disabled: disabled,
      multiple: true
    }, props), {}, {
      onChange: handleChange,
      children: internalFileList.length < maxCount && !disabled && /*#__PURE__*/(0, _jsxRuntime.jsxs)("div", {
        className: "upload-button",
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_icons.PlusOutlined, {}), /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
          className: "upload-text",
          children: "\u4E0A\u4F20"
        })]
      })
    }))
  });
};
ImagesEditor.displayName = 'ImagesEditor';