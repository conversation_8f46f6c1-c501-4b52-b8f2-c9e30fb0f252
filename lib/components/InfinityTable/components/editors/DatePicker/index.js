"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.DateEditor = void 0;
require("antd/es/calendar/style");
var _calendar = _interopRequireDefault(require("antd/es/calendar"));
var _react = _interopRequireWildcard(require("react"));
var _moment = _interopRequireDefault(require("moment"));
require("moment/locale/zh-cn");
var _zh_CN = _interopRequireDefault(require("antd/es/calendar/locale/zh_CN"));
var _constants = require("../../../constants");
require("./index.less");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["value", "onCellChange", "style", "format"];
/**
 * @file DatePicker Editor Component
 * @description 表格单元格日期选择器编辑器组件
 * @review 2024/12/19
 */
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }
function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }
/**
 * 日期选择器编辑器组件
 * @component DateEditor
 */
var DateEditor = exports.DateEditor = function DateEditor(_ref) {
  var value = _ref.value,
    onCellChange = _ref.onCellChange,
    style = _ref.style,
    _ref$format = _ref.format,
    format = _ref$format === void 0 ? 'YYYY-MM-DD' : _ref$format,
    props = _objectWithoutProperties(_ref, _excluded);
  // 设置 moment 的语言为中文
  _moment.default.locale('zh-cn');

  /**
   * 将输入值转换为 Moment 对象
   * @param inputValue - 输入的日期值
   * @returns Moment 对象
   */
  var parseDateValue = function parseDateValue(inputValue) {
    if (!inputValue) return (0, _moment.default)();
    try {
      var parsedDate = (0, _moment.default)(inputValue);
      return parsedDate.isValid() ? parsedDate : (0, _moment.default)();
    } catch (_unused) {
      console.warn('[DateEditor] 无效的日期值:', inputValue);
      return (0, _moment.default)();
    }
  };

  // 当前日期值
  var currentValue = (0, _react.useMemo)(function () {
    return parseDateValue(value);
  }, [value]);

  /**
   * 处理日期选择回调
   * @param date - 选中的日期
   */
  var handleSelect = (0, _react.useCallback)(function (date) {
    if (!date.isValid()) {
      console.warn('[DateEditor] 选择的日期无效');
      return;
    }
    try {
      var formattedValue = date.format(format);
      onCellChange === null || onCellChange === void 0 || onCellChange(formattedValue);
    } catch (error) {
      console.error('[DateEditor] 日期格式化错误:', error);
      onCellChange === null || onCellChange === void 0 || onCellChange('');
    }
  }, [format, onCellChange]);

  /**
   * 处理日期变更回调
   * @param date - 变更后的日期
   */
  var handleChange = (0, _react.useCallback)(function (date) {
    handleSelect(date);
  }, [handleSelect]);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
    className: "infinity-table-date-editor",
    style: _objectSpread({
      width: _constants.EDITOR_CONFIGS.DATE.width
    }, style),
    children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_calendar.default, _objectSpread(_objectSpread({
      fullscreen: false,
      value: currentValue,
      onSelect: handleSelect,
      locale: _zh_CN.default
    }, props), {}, {
      onChange: handleChange
    }))
  });
};
DateEditor.displayName = 'DateEditor';
var _default = exports.default = DateEditor;