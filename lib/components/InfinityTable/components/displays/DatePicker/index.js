"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.DateDisplay = void 0;
var _react = _interopRequireDefault(require("react"));
var _dayjs = _interopRequireDefault(require("dayjs"));
require("./index.less");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
var DateDisplay = exports.DateDisplay = function DateDisplay(_ref) {
  var value = _ref.value,
    style = _ref.style,
    _ref$format = _ref.format,
    format = _ref$format === void 0 ? 'YYYY-MM-DD' : _ref$format;
  return /*#__PURE__*/(0, _jsxRuntime.jsx)("span", {
    className: "date-display",
    style: style,
    children: value ? (0, _dayjs.default)(value).format(format) : '-'
  });
};
DateDisplay.displayName = 'DateDisplay';