"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MultiSelectDisplay = void 0;
var _react = _interopRequireDefault(require("react"));
var _ClearButton = require("../../common/ClearButton");
require("./index.less");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var MultiSelectDisplay = exports.MultiSelectDisplay = function MultiSelectDisplay(_ref) {
  var _ref$value = _ref.value,
    value = _ref$value === void 0 ? [] : _ref$value,
    style = _ref.style,
    _ref$options = _ref.options,
    options = _ref$options === void 0 ? [] : _ref$options,
    onClear = _ref.onClear,
    overlayProps = _ref.overlayProps,
    _ref$maxWidth = _ref.maxWidth,
    maxWidth = _ref$maxWidth === void 0 ? 200 : _ref$maxWidth;
  var tags = _react.default.useMemo(function () {
    if (!Array.isArray(value)) return [];
    return value.map(function (itemValue) {
      var option = options.find(function (opt) {
        return opt.value === itemValue;
      });
      return option || {
        value: itemValue,
        label: itemValue
      };
    });
  }, [value, options]);
  var renderTag = function renderTag(option) {
    return /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
      style: {
        backgroundColor: option.color || 'rgba(134, 144, 156, 0.1)'
      },
      className: "infinity-table-multiselect-item",
      children: option.label
    }, option.value);
  };
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)("div", {
    className: "infinity-table-multiselect-display",
    style: _objectSpread(_objectSpread({}, style), {}, {
      maxWidth: maxWidth
    }),
    children: [(overlayProps === null || overlayProps === void 0 ? void 0 : overlayProps.showClear) && onClear && options.length > 0 && /*#__PURE__*/(0, _jsxRuntime.jsx)(_ClearButton.ClearButton, {
      onClear: onClear
    }), tags.map(renderTag)]
  });
};
MultiSelectDisplay.displayName = 'MultiSelectDisplay';