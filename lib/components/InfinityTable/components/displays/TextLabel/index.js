"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TextDisplay = void 0;
var _react = _interopRequireDefault(require("react"));
var _debug = require("../../../utils/debug");
require("./index.less");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
var TextDisplay = exports.TextDisplay = function TextDisplay(_ref) {
  var value = _ref.value,
    style = _ref.style,
    _ref$ellipsis = _ref.ellipsis,
    ellipsis = _ref$ellipsis === void 0 ? true : _ref$ellipsis,
    maxLength = _ref.maxLength;
  (0, _debug.useRenderTracker)('TextDisplay');
  var textRef = _react.default.useRef(null);
  var _React$useState = _react.default.useState(false),
    _React$useState2 = _slicedToArray(_React$useState, 2),
    isThreeLines = _React$useState2[0],
    setIsThreeLines = _React$useState2[1];
  _react.default.useEffect(function () {
    if (textRef.current) {
      var height = textRef.current.offsetHeight;
      setIsThreeLines(height > 42); // 72px 是三行文本的高度
    }
  }, [value]);
  var displayValue = _react.default.useMemo(function () {
    if (value == null) return '-';
    var text = String(value);
    if (maxLength && text.length > maxLength) {
      text = text.slice(0, maxLength) + '...';
    }
    return text;
  }, [value, maxLength]);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)("span", {
    ref: textRef,
    className: "infinity-table-text-display ".concat(ellipsis ? 'ellipsis' : '', " ").concat(isThreeLines ? 'three-lines' : ''),
    style: style,
    title: ellipsis ? String(value) : undefined,
    children: displayValue
  });
};
TextDisplay.displayName = 'TextDisplay';