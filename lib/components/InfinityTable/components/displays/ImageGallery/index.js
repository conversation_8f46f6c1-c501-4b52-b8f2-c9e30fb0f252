"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ImagesDisplay = void 0;
require("antd/es/space/style");
var _space = _interopRequireDefault(require("antd/es/space"));
require("antd/es/image/style");
var _image = _interopRequireDefault(require("antd/es/image"));
var _react = _interopRequireDefault(require("react"));
var _ClearButton = require("../../common/ClearButton");
var _utils = require("./utils");
require("./index.less");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
// 添加工具函数
var formatImageUrl = function formatImageUrl(url, width, height) {
  if (!url || typeof url !== 'string' || !url.startsWith('http') || url.includes('?')) {
    return url;
  }
  var size = (0, _utils.formatWH)(width, height);
  var process = ['?x-oss-process=image', 'auto-orient,1', 'quality,Q_80', 'bright,-1', "resize,s_".concat(size) // 使用短边自适应缩放
  ];
  if (_utils.isSupportWebp) {
    process.push('format,webp');
  }
  return "".concat(url).concat(process.join('/'));
};
var ImagesDisplay = exports.ImagesDisplay = function ImagesDisplay(_ref) {
  var _ref$value = _ref.value,
    value = _ref$value === void 0 ? [] : _ref$value,
    style = _ref.style,
    _ref$width = _ref.width,
    width = _ref$width === void 0 ? 48 : _ref$width,
    _ref$height = _ref.height,
    height = _ref$height === void 0 ? 48 : _ref$height,
    onClear = _ref.onClear,
    overlayProps = _ref.overlayProps;
  var DISPLAY_COUNT = 2;
  var images = Array.isArray(value) ? value : [];
  var displayImages = images.slice(0, DISPLAY_COUNT);
  var totalCount = images.length;
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)("div", {
    className: "infinity-table-images-display-wrapper",
    children: [(overlayProps === null || overlayProps === void 0 ? void 0 : overlayProps.showClear) && onClear && images.length > 0 && /*#__PURE__*/(0, _jsxRuntime.jsx)(_ClearButton.ClearButton, {
      onClear: onClear
    }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_space.default, {
      size: 4,
      className: "infinity-table-images-display",
      style: style,
      children: displayImages.map(function (url, index) {
        return /*#__PURE__*/(0, _jsxRuntime.jsxs)("div", {
          className: "image-wrapper",
          style: {
            width: width,
            height: height,
            borderRadius: 2
          },
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_image.default, {
            src: formatImageUrl(url, width, height),
            width: width,
            height: height,
            style: {
              objectFit: 'cover',
              borderRadius: 2
            }
          }), index === DISPLAY_COUNT - 1 && totalCount > DISPLAY_COUNT && /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
            className: "image-overlay",
            children: /*#__PURE__*/(0, _jsxRuntime.jsxs)("span", {
              children: [totalCount, "\u5F20"]
            })
          })]
        }, url);
      })
    })]
  });
};
ImagesDisplay.displayName = 'ImagesDisplay';