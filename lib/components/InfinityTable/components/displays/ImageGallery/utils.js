"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.formatWH = formatWH;
exports.getNumber = getNumber;
exports.isSupportWebp = void 0;
var isSupportWebp = exports.isSupportWebp = false;
function checkIsSupportWebp() {
  var webp = new Image();
  webp.src = 'data:image/webp;base64,UklGRjIAAABXRUJQVlA4ICYAAACyAgCdASoBAAEALmk0mk0iIiIiIgBoSygABc6zbAAA/v56QAAAAA==';
  webp.onerror = function () {
    exports.isSupportWebp = isSupportWebp = false;
  };
  webp.onload = function () {
    exports.isSupportWebp = isSupportWebp = true;
  };
}
checkIsSupportWebp();

// 显示器像素比例
var pixelRatio = Math.ceil(window.devicePixelRatio || 1);
function getNumber(value) {
  if (typeof value === 'string') {
    return Number(value.replace('px', ''));
  }
  return value;
}
function formatWH(width, height) {
  var w = getNumber(width);
  var h = getNumber(height);
  if (!w && !h) return 0;
  return Math.floor(pixelRatio * Math.max(w, h));
}