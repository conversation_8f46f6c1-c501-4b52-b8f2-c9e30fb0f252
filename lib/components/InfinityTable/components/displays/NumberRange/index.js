"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.NumberRangeDisplay = void 0;
var _react = _interopRequireDefault(require("react"));
require("./index.less");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
var NumberRangeDisplay = exports.NumberRangeDisplay = function NumberRangeDisplay(_ref) {
  var value = _ref.value,
    style = _ref.style,
    _ref$unit = _ref.unit,
    unit = _ref$unit === void 0 ? '' : _ref$unit,
    _ref$precision = _ref.precision,
    precision = _ref$precision === void 0 ? 0 : _ref$precision,
    formatter = _ref.formatter;
  var formatNumber = _react.default.useCallback(function (num) {
    if (num == null) return '-';
    return formatter ? formatter(num) : num.toFixed(precision);
  }, [formatter, precision]);
  var renderUnit = _react.default.useCallback(function (unitText) {
    return unitText ? /*#__PURE__*/(0, _jsxRuntime.jsx)("span", {
      className: "unit",
      children: " ".concat(unitText)
    }) : '';
  }, []);
  var formattedValue = _react.default.useMemo(function () {
    if (!value || _typeof(value) !== 'object') return '-';
    var _ref2 = value,
      min = _ref2.min,
      max = _ref2.max;
    if (min == null && max == null) {
      return '';
    }
    if (min != null && max == null) {
      return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [formatNumber(min), renderUnit(unit)]
      });
    }
    if (min == null && max != null) {
      return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [formatNumber(max), renderUnit(unit)]
      });
    }
    if (min === max) {
      return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [formatNumber(min), renderUnit(unit)]
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [formatNumber(min), " ~ ", formatNumber(max), renderUnit(unit)]
    });
  }, [value, formatNumber, unit, renderUnit]);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)("span", {
    className: "infinity-table-number-range-display",
    style: style,
    children: formattedValue
  });
};
NumberRangeDisplay.displayName = 'NumberRangeDisplay';