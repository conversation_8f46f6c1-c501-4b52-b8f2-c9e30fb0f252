"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.NumberDisplay = void 0;
var _react = _interopRequireDefault(require("react"));
require("./index.less");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
var NumberDisplay = exports.NumberDisplay = function NumberDisplay(_ref) {
  var value = _ref.value,
    style = _ref.style,
    prefix = _ref.prefix,
    suffix = _ref.suffix;
  var formattedValue = _react.default.useMemo(function () {
    if (value == null) return '';

    //暂时不考虑精度参数
    var displayValue = "".concat(value);
    if (prefix) displayValue = prefix + displayValue;
    if (suffix) displayValue = displayValue + ' ' + suffix;
    return displayValue;
  }, [value, prefix, suffix]);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
    className: "infinity-table-number-display ".concat(suffix ? 'with-suffix' : ''),
    style: style,
    children: formattedValue
  });
};
NumberDisplay.displayName = 'NumberDisplay';