"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useColumns = useColumns;
var _react = _interopRequireWildcard(require("react"));
var _ColumnManager = require("../features/ColumnManager");
var _jsxRuntime = require("react/jsx-runtime");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }
function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); } // 扩展 antd 的列类型，添加我们需要的属性
// 内部组件，不需要导出
var ManagerCell = function ManagerCell(_ref) {
  var columns = _ref.columns,
    onChange = _ref.onChange,
    title = _ref.title,
    defaultColumns = _ref.defaultColumns;
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between'
    },
    children: [title && /*#__PURE__*/(0, _jsxRuntime.jsx)("span", {
      children: title
    }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_ColumnManager.ColumnManager, {
      columns: columns,
      onChange: onChange,
      defaultColumns: defaultColumns
    })]
  });
};
function useColumns(_ref2) {
  var columns = _ref2.columns,
    rules = _ref2.rules,
    columnManager = _ref2.columnManager;
  // 处理列配置，为禁用列添加className
  var processedColumns = (0, _react.useMemo)(function () {
    return columns.map(function (column) {
      return _objectSpread(_objectSpread({}, column), {}, {
        className: "".concat(column.className || '', " ").concat(rules.includes(column.dataIndex) ? 'disabled-column' : '').trim()
      });
    });
  }, [columns, rules]);

  // 过滤出要显示的列
  var visibleColumns = (0, _react.useMemo)(function () {
    return processedColumns.filter(function (col) {
      return col.show !== false;
    });
  }, [processedColumns]);

  // 处理列管理
  var finalColumns = (0, _react.useMemo)(function () {
    if (!columnManager) return visibleColumns;

    // 如果没有可见列，返回带有列管理器的空列
    if (visibleColumns.length === 0) {
      return [{
        key: 'column-manager',
        dataIndex: 'column-manager',
        title: /*#__PURE__*/(0, _jsxRuntime.jsx)(ManagerCell, {
          columns: columns,
          onChange: columnManager.onChange,
          defaultColumns: columnManager.defaultColumns
        }),
        width: 100
      }];
    }

    // 添加列管理按钮到最后列
    var lastColumn = visibleColumns[visibleColumns.length - 1];
    var enhancedLastColumn = _objectSpread(_objectSpread({}, lastColumn), {}, {
      title: /*#__PURE__*/(0, _jsxRuntime.jsx)(ManagerCell, {
        columns: columns,
        onChange: columnManager.onChange,
        defaultColumns: columnManager.defaultColumns,
        title: lastColumn.title
      })
    });
    return [].concat(_toConsumableArray(visibleColumns.slice(0, -1)), [enhancedLastColumn]);
  }, [visibleColumns, columns, columnManager]);
  return {
    finalColumns: finalColumns
  };
}