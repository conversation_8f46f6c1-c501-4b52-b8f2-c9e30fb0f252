"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TableContext = void 0;
exports.TableContextProvider = TableContextProvider;
exports.useTableContext = useTableContext;
var _react = _interopRequireWildcard(require("react"));
var _debug = require("../utils/debug");
var _jsxRuntime = require("react/jsx-runtime");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; } /* eslint-disable react-refresh/only-export-components */ /**
 * TableContext - 表格状态管理上下文
 * 
 * 核心功能:
 * 1. 管理表格编辑状态 - 跟踪哪些单元格正在编辑
 * 2. 提供数据源访问 - 统一管理表格数据
 * 3. 处理单元格选中/编辑 - 控制单元格的编辑生命周期
 * 4. 统一管理调试配置 - 开发环境下的调试工具
 */ // 创建泛型上下文
// eslint-disable-next-line @typescript-eslint/no-explicit-any
var TableContext = exports.TableContext = /*#__PURE__*/(0, _react.createContext)(null);
/**
 * TableContextProvider - 表格上下文提供者组件
 * 
 * @param children - 子组件
 * @param prefixCls - 样式前缀
 * @param dataSource - 表格数据源
 * @param debug - 调试配置
 */
function TableContextProvider(_ref) {
  var children = _ref.children,
    prefixCls = _ref.prefixCls,
    dataSource = _ref.dataSource,
    debug = _ref.debug;
  // 使用 Map 存储编辑状态，key 为单元格唯一标识，value 为编辑状态
  // 使用 useRef 确保在重渲染时保持引用不变
  var editingCellsRef = (0, _react.useRef)(new Map());

  // updateTrigger 用于强制组件重新渲染
  // 当编辑状态改变时，增加计数器触发更新
  var _useState = (0, _react.useState)(0),
    _useState2 = _slicedToArray(_useState, 2),
    updateTrigger = _useState2[0],
    setUpdateTrigger = _useState2[1];

  // 存储单元格开始编辑时的初始值，用于取消编辑时恢复
  var _useState3 = (0, _react.useState)(null),
    _useState4 = _slicedToArray(_useState3, 2),
    initialCellValue = _useState4[0],
    setInitialCellValue = _useState4[1];

  /**
   * 生成单元格的唯一标识
   * 格式: `${rowIndex}-${columnKey}`
   * 示例: 
   * - 第0行name列: "0-name"
   * - 第1行age列: "1-age"
   */
  var generateCellKey = (0, _react.useCallback)(function (rowIndex, columnKey) {
    return "".concat(rowIndex, "-").concat(columnKey);
  }, []);

  /**
   * 开始/结束编辑的核心处理函数
   * 
   * @param from - 触发来源，用于区分不同的编辑场景
   * @param cell - 要编辑的单元格信息
   * @param currentCell - 当前正在编辑的单元格
   * 
   * 数据流转示例:
   * 1. 开始编辑:
   *    cell = { rowIndex: 0, column: { key: 'name' }, value: '张三' }
   *    → Map { "0-name" => { initialValue: '张三', value: '张三' } }
   * 
   * 2. 修改值:
   *    Map { "0-name" => { initialValue: '张三', value: '李四' } }
   * 
   * 3. 取消编辑:
   *    → Map { "0-name" => { initialValue: '张三', value: '张三' } }
   *    → Map 删除该条目
   * 
   * 4. 确认编辑:
   *    → Map 直接删除该条目，新值通过其他逻辑保存
   */
  var startEdit = (0, _react.useCallback)(function (_ref2) {
    var from = _ref2.from,
      cell = _ref2.cell,
      currentCell = _ref2.currentCell;
    if (cell === null && currentCell) {
      // 结束当前单元格的编辑
      var cellKey = generateCellKey(currentCell.rowIndex, currentCell.columnKey);

      // 取消编辑时恢复初始值
      if (from === 'handleClose_cancel') {
        var editingCell = editingCellsRef.current.get(cellKey);
        if (editingCell) {
          editingCell.value = editingCell.initialValue;
        }
      }
      editingCellsRef.current.delete(cellKey);
    } else if (cell === null) {
      // 清除所有编辑状态（通常用于表格重置）
      editingCellsRef.current.clear();
    } else {
      // 开始新单元格的编辑
      var _cellKey = generateCellKey(cell.rowIndex, cell.column.key);
      var existingCell = editingCellsRef.current.get(_cellKey);

      // 保存初始值，用于后续可能的取消操作
      editingCellsRef.current.set(_cellKey, _objectSpread(_objectSpread({}, cell), {}, {
        initialValue: existingCell ? existingCell.initialValue : cell.value
      }));
    }

    // 触发重渲染，确保UI更新
    setUpdateTrigger(function (prev) {
      return prev + 1;
    });
  }, [generateCellKey]);

  /**
   * 检查指定单元格是否处于编辑状态
   * 
   * 示例:
   * Map包含: { "0-name" => {...} }
   * isEditing(0, "name") → true
   * isEditing(0, "age") → false
   */
  var isEditing = (0, _react.useCallback)(function (rowIndex, columnKey) {
    var cellKey = generateCellKey(rowIndex, columnKey);
    return editingCellsRef.current.has(cellKey);
  }, [generateCellKey]);

  /**
   * 获取指定单元格的编辑状态
   * 返回完整的编辑信息或null
   */
  var getEditingCell = (0, _react.useCallback)(function (rowIndex, columnKey) {
    var cellKey = generateCellKey(rowIndex, columnKey);
    return editingCellsRef.current.get(cellKey) || null;
  }, [generateCellKey]);

  // 通过 useMemo 缓存上下文值，避免不必要的重渲染
  var contextValue = (0, _react.useMemo)(function () {
    return {
      dataSource: dataSource,
      editingCellsMap: editingCellsRef.current,
      isEditing: isEditing,
      getEditingCell: getEditingCell,
      initialCellValue: initialCellValue,
      setInitialCellValue: setInitialCellValue,
      debug: debug,
      prefixCls: prefixCls,
      startEdit: startEdit,
      updateTrigger: updateTrigger,
      generateCellKey: generateCellKey
    };
  }, [dataSource, updateTrigger, initialCellValue, debug, prefixCls]);

  // 开发环境下跟踪组件渲染
  (0, _debug.useRenderTracker)('TableContextProvider');
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(TableContext.Provider, {
    value: contextValue,
    children: children
  });
}

/**
 * useTableContext - 获取表格上下文的自定义Hook
 * 
 * 使用示例:
 * ```tsx
 * const { isEditing, startEdit } = useTableContext<UserData>();
 * ```
 */
function useTableContext() {
  var context = (0, _react.useContext)(TableContext);
  if (!context) {
    throw new Error('useTableContext必须在TableContextProvider内部使用');
  }
  return context;
}

/**
 * editingCellsRef 数据结构示例:
 * 
 * 假设正在编辑第0行的name列和第1行的age列:
 * 
 * editingCellsRef.current = Map {
 *   "0-name" => {
 *     rowIndex: 0,
 *     column: { key: "name", title: "姓名" },
 *     record: { id: 1, name: "张三", age: 25 },
 *     value: "李四",        // 当前编辑值
 *     initialValue: "张三"  // 开始编辑时的初始值
 *   },
 *   "1-age" => {
 *     rowIndex: 1,
 *     column: { key: "age", title: "年龄" },
 *     record: { id: 2, name: "王五", age: 30 },
 *     value: 31,           // 当前编辑值
 *     initialValue: 30     // 开始编辑时的初始值
 *   }
 * }
 * 
 * 数据流转过程:
 * 1. 开始编辑 → 保存初始值
 * 2. 编辑过程 → 只更新value
 * 3. 取消编辑 → value恢复为initialValue，然后删除Map条目
 * 4. 确认编辑 → 直接删除Map条目，新值由外部逻辑处理
 */