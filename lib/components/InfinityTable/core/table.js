"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TableCore = void 0;
require("antd/es/table/style");
var _table = _interopRequireDefault(require("antd/es/table"));
require("antd/es/message/style");
var _message2 = _interopRequireDefault(require("antd/es/message"));
var _react = _interopRequireWildcard(require("react"));
var _Cell = require("../components/renderers/Cell");
var _context2 = require("./context");
var _constants = require("../constants");
var _getPrefixCls = require("../utils/getPrefixCls");
var _Tooltip = require("../components/overlay/Tooltip");
var _useTooltip2 = require("../hooks/useTooltip");
var _cellEditor = require("./cellEditor");
var _debug = require("../utils/debug");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["columns", "height", "width", "disableControl", "editingLock", "rowKey", "rowSelection", "onEditStateChange"];
/**
 * TableCore 组件 - 表格核心组件
 * 
 * @description
 * 核心功能包括:
 * 1. 表格基础渲染与布局
 * 2. 单元格编辑状态管理
 * 3. 行选择处理
 * 4. 单元格禁用和锁定状态控制
 * 5. 工具提示显示
 */
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _regeneratorRuntime() { "use strict"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = "function" == typeof Symbol ? Symbol : {}, a = i.iterator || "@@iterator", c = i.asyncIterator || "@@asyncIterator", u = i.toStringTag || "@@toStringTag"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, ""); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, "_invoke", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: "normal", arg: t.call(e, r) }; } catch (t) { return { type: "throw", arg: t }; } } e.wrap = wrap; var h = "suspendedStart", l = "suspendedYield", f = "executing", s = "completed", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { ["next", "throw", "return"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if ("throw" !== c.type) { var u = c.arg, h = u.value; return h && "object" == _typeof(h) && n.call(h, "__await") ? e.resolve(h.__await).then(function (t) { invoke("next", t, i, a); }, function (t) { invoke("throw", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke("throw", t, i, a); }); } a(c.arg); } var r; o(this, "_invoke", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw new Error("Generator is already running"); if (o === s) { if ("throw" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if ("next" === n.method) n.sent = n._sent = n.arg;else if ("throw" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else "return" === n.method && n.abrupt("return", n.arg); o = f; var p = tryCatch(e, r, n); if ("normal" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } "throw" === p.type && (o = s, n.method = "throw", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, "throw" === n && e.iterator.return && (r.method = "return", r.arg = t, maybeInvokeDelegate(e, r), "throw" === r.method) || "return" !== n && (r.method = "throw", r.arg = new TypeError("The iterator does not provide a '" + n + "' method")), y; var i = tryCatch(o, e.iterator, r.arg); if ("throw" === i.type) return r.method = "throw", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, "return" !== r.method && (r.method = "next", r.arg = t), r.delegate = null, y) : a : (r.method = "throw", r.arg = new TypeError("iterator result is not an object"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = "normal", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: "root" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || "" === e) { var r = e[a]; if (r) return r.call(e); if ("function" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(_typeof(e) + " is not iterable"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, "constructor", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, "constructor", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, "GeneratorFunction"), e.isGeneratorFunction = function (t) { var e = "function" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || "GeneratorFunction" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, "GeneratorFunction")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, "Generator"), define(g, a, function () { return this; }), define(g, "toString", function () { return "[object Generator]"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = "next", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) "t" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if ("throw" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = "throw", a.arg = e, r.next = n, o && (r.method = "next", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if ("root" === i.tryLoc) return handle("end"); if (i.tryLoc <= this.prev) { var c = n.call(i, "catchLoc"), u = n.call(i, "finallyLoc"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw new Error("try statement without catch or finally"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, "finallyLoc") && this.prev < o.finallyLoc) { var i = o; break; } } i && ("break" === t || "continue" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = "next", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if ("throw" === t.type) throw t.arg; return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if ("throw" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw new Error("illegal catch attempt"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, "next" === this.method && (this.arg = t), y; } }, e; }
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }
function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }
function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }
var TableCore = exports.TableCore = function TableCore(_ref) {
  var columns = _ref.columns,
    height = _ref.height,
    propWidth = _ref.width,
    _ref$disableControl = _ref.disableControl,
    disableControl = _ref$disableControl === void 0 ? {
      // 禁用控制配置
      rules: [],
      tooltip: {
        show: true,
        message: "暂无编辑权限"
      }
    } : _ref$disableControl,
    _ref$editingLock = _ref.editingLock,
    editingLock = _ref$editingLock === void 0 ? {
      // 编辑锁定配置
      editingRows: [],
      tooltip: {
        show: true,
        message: "他人正在编辑"
      }
    } : _ref$editingLock,
    _ref$rowKey = _ref.rowKey,
    rowKey = _ref$rowKey === void 0 ? "id" : _ref$rowKey,
    rowSelection = _ref.rowSelection,
    onEditStateChange = _ref.onEditStateChange,
    restProps = _objectWithoutProperties(_ref, _excluded);
  (0, _debug.useRenderTracker)('TableCore');

  // ================ Hooks & Context ================
  var prefixCls = (0, _getPrefixCls.usePrefixCls)("table");
  var _useTableContext = (0, _context2.useTableContext)(),
    startEdit = _useTableContext.startEdit,
    dataSource = _useTableContext.dataSource,
    setInitialCellValue = _useTableContext.setInitialCellValue,
    editingCellsMap = _useTableContext.editingCellsMap;
  var _useTooltip = (0, _useTooltip2.useTooltip)(),
    tooltipState = _useTooltip.tooltipState,
    tooltipRef = _useTooltip.tooltipRef,
    handleCellHover = _useTooltip.handleCellHover,
    handleCellLeave = _useTooltip.handleCellLeave,
    clearTooltip = _useTooltip.clearTooltip;

  // ================ State ================
  // 选中单元格状态
  var _useState = (0, _react.useState)(null),
    _useState2 = _slicedToArray(_useState, 2),
    selectedCell = _useState2[0],
    setSelectedCell = _useState2[1];

  // ================ Utility Functions ================
  /**
   * 获取行的唯一键值
   * @param record 行数据记录
   * @returns 行的唯一标识符
   */
  var getRowKey = function getRowKey(record) {
    var key = typeof rowKey === "function" ? rowKey(record) : record[rowKey];
    return String(key);
  };

  /**
   * 判断行是否被锁定
   * @param rowKey 行标识
   */
  var checkRowEditingLockStatus = (0, _react.useCallback)(function (rowKey) {
    var _editingLock$editingR;
    return (editingLock === null || editingLock === void 0 || (_editingLock$editingR = editingLock.editingRows) === null || _editingLock$editingR === void 0 ? void 0 : _editingLock$editingR.some(function (row) {
      return row.id === rowKey;
    })) || false;
  }, [editingLock === null || editingLock === void 0 ? void 0 : editingLock.editingRows]);

  /**
   * 获取行的锁定提示消息
   * @param rowKey 行标识
   */
  var getRowEditingLockMessage = (0, _react.useCallback)(function (rowKey) {
    var _editingLock$editingR2, _editingLock$tooltip;
    var lockedRow = editingLock === null || editingLock === void 0 || (_editingLock$editingR2 = editingLock.editingRows) === null || _editingLock$editingR2 === void 0 ? void 0 : _editingLock$editingR2.find(function (row) {
      return row.id === rowKey;
    });
    return (lockedRow === null || lockedRow === void 0 ? void 0 : lockedRow.message) || (editingLock === null || editingLock === void 0 || (_editingLock$tooltip = editingLock.tooltip) === null || _editingLock$tooltip === void 0 ? void 0 : _editingLock$tooltip.message) || '';
  }, [editingLock]);

  /**
   * 判断单元格是否禁用
   * @param record 行数据
   * @param column 列配置
   */
  var isDisabledCell = (0, _react.useCallback)(function (record, column) {
    // 检查全局禁用规则
    if (disableControl !== null && disableControl !== void 0 && disableControl.rules) {
      var isGlobalDisabled = disableControl.rules.some(function (rule) {
        if (typeof rule === "string") return rule === column.dataIndex;
        if (_typeof(rule) === "object" && "field" in rule && "match" in rule) {
          return rule.field === column.dataIndex && rule.match(record);
        }
        return false;
      });
      if (isGlobalDisabled) return true;
    }

    // 检查列级别禁用配置
    if (column.disabled !== undefined) {
      return typeof column.disabled === "function" ? column.disabled(record) : column.disabled;
    }
    return false;
  }, [disableControl === null || disableControl === void 0 ? void 0 : disableControl.rules]);

  // ================ Event Handlers ================
  /**
   * 处理单元格点击事件
   * @param record 行数据
   * @param column 列配置
   */
  var handleCellClick = function handleCellClick(record, column) {
    var _disableControl$rules;
    if ((_disableControl$rules = disableControl.rules) !== null && _disableControl$rules !== void 0 && _disableControl$rules.includes(column.dataIndex)) return;
    var currentRowKey = getRowKey(record);
    if (checkRowEditingLockStatus(currentRowKey)) return;
    setSelectedCell({
      rowKey: currentRowKey,
      dataIndex: column.dataIndex
    });
  };

  /**
   * 处理单元格双击事件
   * @param e 鼠标事件
   * @param record 行数据
   * @param column 列配置
   */
  var handleCellDoubleClick = /*#__PURE__*/function () {
    var _ref2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(e, record, column) {
      var _column$editor;
      var currentRowKey, canEdit, findTableCell, target, cell, rect, tableBody, tableWrapper, wrapperRect, position, value;
      return _regeneratorRuntime().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return new Promise(function (resolve) {
              return setTimeout(resolve, 45);
            });
          case 2:
            if (!(editingCellsMap.size > 0)) {
              _context.next = 5;
              break;
            }
            _message2.default.info({
              content: "操作过于频繁，请稍后再试",
              key: 'editing-in-progress',
              duration: 2
            });
            return _context.abrupt("return");
          case 5:
            if (!isDisabledCell(record, column)) {
              _context.next = 7;
              break;
            }
            return _context.abrupt("return");
          case 7:
            currentRowKey = getRowKey(record);
            if (!checkRowEditingLockStatus(currentRowKey)) {
              _context.next = 10;
              break;
            }
            return _context.abrupt("return");
          case 10:
            if (column.editor) {
              _context.next = 12;
              break;
            }
            return _context.abrupt("return");
          case 12:
            if (!((_column$editor = column.editor) !== null && _column$editor !== void 0 && _column$editor.beforeEdit)) {
              _context.next = 25;
              break;
            }
            _context.prev = 13;
            _context.next = 16;
            return column.editor.beforeEdit(record);
          case 16:
            canEdit = _context.sent;
            if (canEdit) {
              _context.next = 19;
              break;
            }
            return _context.abrupt("return");
          case 19:
            _context.next = 25;
            break;
          case 21:
            _context.prev = 21;
            _context.t0 = _context["catch"](13);
            console.error("BeforeEdit check failed:", _context.t0);
            return _context.abrupt("return");
          case 25:
            // 获取单元格DOM元素
            findTableCell = function findTableCell(element) {
              if (!element) return null;
              if (element.classList.contains("infinity-table-cell")) {
                return element;
              }
              return findTableCell(element.parentElement);
            }; // 计算编辑器位置
            target = e.target;
            cell = e.currentTarget || findTableCell(target);
            rect = cell.getBoundingClientRect();
            tableBody = cell.closest(".".concat(prefixCls, "-body"));
            if (tableBody) {
              _context.next = 32;
              break;
            }
            return _context.abrupt("return");
          case 32:
            tableWrapper = cell.closest(".infinity-table-wrapper");
            if (tableWrapper) {
              _context.next = 35;
              break;
            }
            return _context.abrupt("return");
          case 35:
            wrapperRect = tableWrapper.getBoundingClientRect();
            position = {
              top: rect.top - wrapperRect.top + (tableBody.scrollTop || 0),
              left: rect.left - wrapperRect.left + (tableBody.scrollLeft || 0),
              width: rect.width,
              height: rect.height
            }; // 启动编辑模式
            value = record[column.dataIndex];
            setInitialCellValue(value);
            startEdit({
              from: 'handleCellDoubleClick',
              cell: {
                rowIndex: dataSource.indexOf(record),
                rowKey: getRowKey(record),
                dataIndex: column.dataIndex,
                value: value,
                record: record,
                column: column,
                editor: column.editor,
                position: position,
                cellTarget: cell
              }
            });
          case 40:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[13, 21]]);
    }));
    return function handleCellDoubleClick(_x, _x2, _x3) {
      return _ref2.apply(this, arguments);
    };
  }();

  /**
   * 处理复制功能
   * @param e 键盘事件
   */
  var handleCopy = (0, _react.useCallback)(function (e) {
    if ((e.metaKey || e.ctrlKey) && e.key === 'c') {
      // 直接通过选中单元格的类名获取元素
      var cell = document.querySelector('.infinity-table-cell-selected');
      if (cell) {
        // 检查是否为图片类型单元格
        var hasImageDisplay = cell.querySelector('.infinity-table-images-display');
        if (hasImageDisplay) {
          // 如果是图片类型，则不执行复制
          return;
        }

        // 尝试多种方式获取文本内容
        var textToCopy = '';

        // 1. 首先尝试获取.infinity-table-display-cell的内容
        var displayCell = cell.querySelector('.infinity-table-display-cell');
        if (displayCell) {
          textToCopy = displayCell.textContent || '';
        }

        // 2. 如果没有找到.infinity-table-display-cell，尝试获取单元格的直接文本内容
        if (!textToCopy) {
          textToCopy = cell.textContent || '';
        }

        // 3. 清理文本内容（去除多余空格）
        textToCopy = textToCopy.trim();

        // 复制到剪贴板
        if (textToCopy) {
          navigator.clipboard.writeText(textToCopy).then(function () {
            _message2.default.success({
              content: '已复制到剪贴板',
              key: 'copy-success',
              duration: 1
            });
          }).catch(function () {
            _message2.default.error({
              content: '复制失败',
              key: 'copy-error',
              duration: 1
            });
          });
        }
      }
    }
  }, []); // 不再需要依赖 selectedCell

  // 添加键盘事件监听
  (0, _react.useEffect)(function () {
    var handler = function handler(e) {
      return handleCopy(e);
    };
    document.addEventListener('keydown', handler);
    return function () {
      document.removeEventListener('keydown', handler);
    };
  }, [handleCopy]);

  // ================ Render Functions ================
  /**
   * 渲染单元格内容
   * @param value 单元格值
   * @param record 行数据
   * @param column 列配置
   */
  var renderCell = function renderCell(value, record, column) {
    var _column$editor2, _column$editor3, _column$editor4;
    var isLocked = checkRowEditingLockStatus(getRowKey(record));
    var isDisabled = isDisabledCell(record, column);
    var content = /*#__PURE__*/(0, _jsxRuntime.jsx)(_Cell.DisplayCell, _objectSpread({
      mode: "display",
      type: ((_column$editor2 = column.editor) === null || _column$editor2 === void 0 ? void 0 : _column$editor2.type) || "text",
      value: value,
      displayRender: (_column$editor3 = column.editor) === null || _column$editor3 === void 0 ? void 0 : _column$editor3.displayRender
    }, (_column$editor4 = column.editor) === null || _column$editor4 === void 0 ? void 0 : _column$editor4.props));
    return isLocked || isDisabled ? /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
      className: "infinity-table-display-cell ".concat(isDisabled ? "disabled-cell-content" : "", " ").concat(isLocked ? "locked-cell-content" : ""),
      children: content
    }) : content;
  };

  // ================ Column Configuration ================
  /**
   * 处理列配置，增强列的交互能力
   * 
   * 业务流程:
   * 1. 基础展示
   *    - 渲染单元格内容
   *    - 应用列样式和宽度
   * 
   * 2. 交互控制
   *    - 双击进入编辑模式
   *    - 单击选中单元格
   *    - 处理单元格的hover效果
   * 
   * 3. 权限控制
   *    - 检查单元格是否可编辑
   *    - 处理被锁定的行
   *    - 显示禁用/锁定状态的样式
   * 
   * 4. 提示信息
   *    - 权限不足时显示tooltip
   *    - 行被锁定时显示提示
   */
  var processedColumns = (0, _react.useMemo)(function () {
    return columns.map(function (column) {
      var baseColumn = _objectSpread(_objectSpread({}, column), {}, {
        onCell: function onCell(record) {
          var _disableControl$rules2;
          var rowKey = getRowKey(record);
          var isLocked = checkRowEditingLockStatus(rowKey);
          var isDisabled = isDisabledCell(record, column);
          var isGlobalDisabled = disableControl === null || disableControl === void 0 || (_disableControl$rules2 = disableControl.rules) === null || _disableControl$rules2 === void 0 ? void 0 : _disableControl$rules2.some(function (rule) {
            if (typeof rule === "string") return rule === column.dataIndex;
            if (_typeof(rule) === "object" && "field" in rule && "match" in rule) {
              return rule.field === column.dataIndex && rule.match(record);
            }
            return false;
          });

          // 判断单元格是否被选中
          var isSelected = (selectedCell === null || selectedCell === void 0 ? void 0 : selectedCell.rowKey) === rowKey && (selectedCell === null || selectedCell === void 0 ? void 0 : selectedCell.dataIndex) === column.dataIndex;
          // 判断是否为固定列                 
          var isFixedColumn = column.fixed === "left" || column.fixed === "right";
          return {
            // 设置单元格类名和data属性
            className: "infinity-table-cell \n              ".concat(!isFixedColumn && isSelected ? "infinity-table-cell-selected" : "", " \n              ").concat(isDisabled ? "disabled-cell-content" : "", " \n              ").concat(isLocked ? "locked-cell-content" : ""),
            // 双击编辑处理
            onDoubleClick: isDisabled || isLocked ? undefined : function (e) {
              return handleCellDoubleClick(e, record, column);
            },
            // 单击选中处理    
            onClick: isDisabled || isLocked ? undefined : function () {
              return handleCellClick(record, column);
            },
            // 悬停提示处理    
            onMouseEnter: function onMouseEnter(e) {
              var _disableControl$toolt;
              return handleCellHover(e, isGlobalDisabled, isLocked && getRowEditingLockMessage(rowKey) || isGlobalDisabled && (disableControl === null || disableControl === void 0 || (_disableControl$toolt = disableControl.tooltip) === null || _disableControl$toolt === void 0 ? void 0 : _disableControl$toolt.message));
            },
            onMouseLeave: handleCellLeave
          };
        },
        // 单元格渲染逻辑
        render: function render(value, record) {
          return column.render ?
          /*#__PURE__*/
          // 使用自定义渲染
          (0, _jsxRuntime.jsx)("div", {
            className: "infinity-table-display-cell ".concat(column.className || "", " \n                ").concat(isDisabledCell(record, column) ? "disabled-cell-content" : "", " \n                ").concat(checkRowEditingLockStatus(getRowKey(record)) ? "locked-cell-content" : ""),
            style: {
              height: "".concat(_constants.TABLE_LAYOUT_CONFIG.CELL_HEIGHT, "px"),
              overflow: "hidden",
              padding: "12px 12px",
              width: "100%"
            },
            children: column.render(value, record, dataSource.indexOf(record))
          }) :
          // 使用默认单元格渲染
          renderCell(value, record, column);
        }
      });
      return baseColumn;
    });
  }, [columns, selectedCell, disableControl.rules, checkRowEditingLockStatus, isDisabledCell, handleCellHover, handleCellLeave,
  //handleCellDoubleClick,
  //handleCellClick,
  dataSource, getRowKey]);

  // ================ Effects ================
  // 监听编辑锁定状态变化
  (0, _react.useEffect)(function () {
    clearTooltip();
  }, [editingLock === null || editingLock === void 0 ? void 0 : editingLock.editingRows, clearTooltip]);

  // ================ Render ================
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)("div", {
    className: "infinity-table-wrapper",
    style: {
      height: height,
      width: propWidth
    },
    children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_table.default, _objectSpread(_objectSpread({}, restProps), {}, {
      columns: processedColumns,
      dataSource: dataSource,
      rowKey: rowKey,
      pagination: false,
      className: "infinity-table-container infinity-table",
      sticky: true,
      prefixCls: "",
      rowSelection: rowSelection
    })), /*#__PURE__*/(0, _jsxRuntime.jsx)(_cellEditor.CellEditor, {
      onEditStateChange: onEditStateChange
    }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_Tooltip.Tooltip, {
      tooltipState: tooltipState,
      disableControl: disableControl,
      editingLock: editingLock,
      tooltipRef: tooltipRef
    })]
  });
};