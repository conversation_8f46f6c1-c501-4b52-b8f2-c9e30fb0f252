function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
var _excluded = ["dataSource", "width", "columns", "rules", "columnManager", "onEditStateChange", "prefixCls", "debug"];
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }
function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }
/**
 * InfinityTable - 高性能虚拟滚动表格组件
 */
import React from 'react';
import { TableCore } from "./core/table";
import { TableContextProvider } from "./core/context";
import { useColumns } from "./hooks/useColumns";
import "./styles/index.less";
import { jsx as _jsx } from "react/jsx-runtime";
export function InfinityTable(_ref) {
  var dataSource = _ref.dataSource,
    width = _ref.width,
    columns = _ref.columns,
    _ref$rules = _ref.rules,
    rules = _ref$rules === void 0 ? [] : _ref$rules,
    columnManager = _ref.columnManager,
    onEditStateChange = _ref.onEditStateChange,
    _ref$prefixCls = _ref.prefixCls,
    prefixCls = _ref$prefixCls === void 0 ? 'ant' : _ref$prefixCls,
    _ref$debug = _ref.debug,
    debug = _ref$debug === void 0 ? {
      enabled: false
    } : _ref$debug,
    props = _objectWithoutProperties(_ref, _excluded);
  // 根据规则和列管理器配置处理和转换列
  var _useColumns = useColumns({
      columns: columns,
      rules: rules,
      columnManager: columnManager
    }),
    finalColumns = _useColumns.finalColumns;

  // 缓存表格上下文值以避免不必要的重渲染
  var contextValue = React.useMemo(function () {
    return {
      dataSource: dataSource,
      debug: debug,
      prefixCls: prefixCls
    };
  }, [dataSource, debug, prefixCls]);
  return /*#__PURE__*/_jsx("div", {
    className: "infinity-table-root",
    "data-testid": "infinity-table",
    children: /*#__PURE__*/_jsx(TableContextProvider, _objectSpread(_objectSpread({}, contextValue), {}, {
      children: /*#__PURE__*/_jsx(TableCore, _objectSpread({
        width: width,
        columns: finalColumns,
        dataSource: dataSource,
        onEditStateChange: onEditStateChange
      }, props))
    }))
  });
}

// 导出组件类型供外部使用