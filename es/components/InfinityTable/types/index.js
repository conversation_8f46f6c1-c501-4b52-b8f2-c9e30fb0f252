/**
 * InfinityTable 类型定义文件
 * 包含组件所需的所有类型定义,包括:
 * - 组件属性类型
 * - 编辑器类型
 * - 渲染器类型
 * - 验证器类型
 * - 工具类型
 */

import { ColumnType } from 'antd/es/table';

/**
 * 扩展的表格列类型
 * @template T 数据类型
 * @extends ColumnType<T> antd 表格列类型
 */

/**
 * 列项配置
 * @template T 数据类型
 * @extends TableColumnType<T> 扩展的表格列类型
 */

/**
 * 可拖拽项的类型定义
 */

/**
 * 单元格值的基础类型
 * 支持字符串、数字、布尔值、null 和 undefined
 */

/**
 * 编辑中的单元格信息
 * @template T 数据类型
 */

/**
 * 列配置类型
 * @template T 数据类型
 */

/**
 * 表格组件属性
 * @template T 数据类型
 * @extends TableProps<T> antd 表格属性
 */

/**
 * 编辑器属性
 * @template T 值类型
 * @template R 行数据类型
 */

/**
 * 渲染器属性
 * @template T 值类型
 */

/**
 * 单元格格式规则
 * @template T 值类型
 */

/**
 * 验证器函数类型
 * 返回 true 表示验证通过,返回字符串表示验证失败信息
 */

/**
 * 选中单元格信息
 */

/**
 * 表格上下文类型
 * @template T 数据类型
 */

/**
 * 编辑器配置
 * @template T 数据类型
 */

/**
 * 单元格属性
 */

/**
 * 显示组件属性
 * @template T 值类型
 */

/**
 * 列管理器属性
 */

/**
 * 调试配置
 */

/**
 * 列管理器配置
 */

/**
 * 扩展列类型
 * @template T 数据类型
 */

/**
 * 提示框状态
 */

/**
 * 权限配置
 * @template T 数据类型
 */

/**
 * 编辑锁定配置
 */

/**
 * 编辑器组件属性
 * @template T 值类型
 */

/**
 * 搜索选择器选项
 */

/**
 * 搜索选择器属性
 */

/**
 * 搜索参数
 */

/**
 * 编辑锁定行配置
 */

/**
 * 编辑锁定配置
 */