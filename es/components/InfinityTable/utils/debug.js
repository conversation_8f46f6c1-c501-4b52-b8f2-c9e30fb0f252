function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
import "antd/es/notification/style";
import _notification from "antd/es/notification";
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
import { useTableContext } from "../core/context";
import { useEffect, useRef } from 'react';

/**
 * 日志类型定义
 * @description 用于统一管理日志的类型
 */

/**
 * 日志配置接口
 * @description 定义日志的配置选项
 */

/**
 * 默认日志配置
 * @description 统一的日志配置，方便后续维护和修改
 */
var DEFAULT_LOG_OPTIONS = {
  placement: 'topRight',
  duration: 3
};

/**
 * 日志处理函数映射
 * @description 统一管理不同类型日志的控制台输出方法
 */
var LOG_FUNCTIONS = {
  info: console.log,
  warn: console.warn,
  error: console.error,
  render: console.log
};

/**
 * 通知处理函数映射
 * @description 统一管理不同类型日志的通知显示方法
 */
var NOTIFICATION_FUNCTIONS = {
  info: _notification.info,
  warn: _notification.warning,
  error: _notification.error,
  render: _notification.info
};

/**
 * 格式化日志消息
 * @param content - 日志内容
 * @returns 格式化后的日志内容
 */
var formatLogMessage = function formatLogMessage(content) {
  return "[InfinityTable] ".concat(content);
};

/**
 * 发送日志消息
 * @param type - 日志类型
 * @param title - 日志标题
 * @param content - 日志内容
 * @param options - 日志配置选项
 */
var logMessage = function logMessage(type, title, content) {
  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : DEFAULT_LOG_OPTIONS;
  var showNotification = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;
  LOG_FUNCTIONS[type](formatLogMessage(content));
  if (showNotification && type !== 'render') {
    NOTIFICATION_FUNCTIONS[type](_objectSpread(_objectSpread({
      message: title,
      description: content
    }, DEFAULT_LOG_OPTIONS), options));
  }
};

/**
 * 组件渲染追踪钩子
 * @param componentName - 组件名称
 * @returns void
 */
export var useRenderTracker = function useRenderTracker(componentName) {
  var renderCount = useRef(0);
  useEffect(function () {
    if (process.env.NODE_ENV === 'development' && window.location.href.includes('debug')) {
      renderCount.current += 1;
      console.log("%c[InfinityTable]%c ".concat(componentName, " %c\u6E32\u67D3\u6B21\u6570: %c").concat(renderCount.current), 'color: #1890ff; font-weight: bold', 'color: #52c41a; font-weight: bold', 'color: #722ed1', 'color: #f5222d; font-weight: bold');
    }
  });
};

/**
 * Table调试钩子
 * @description 提供表格组件的调试功能
 * @returns 调试工具对象
 */
export var useTableDebug = function useTableDebug() {
  var _useTableContext = useTableContext(),
    debug = _useTableContext.debug;

  /**
   * 统一的日志处理函数
   * @param title - 日志标题
   * @param content - 日志内容
   * @param type - 日志类型
   * @param options - 日志配置选项
   */
  var log = function log(title, content) {
    var type = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'info';
    var options = arguments.length > 3 ? arguments[3] : undefined;
    if (!(debug !== null && debug !== void 0 && debug.enabled)) return;
    logMessage(type, title, content, options);
  };
  return {
    /**
     * 信息日志
     * @param title - 日志标题
     * @param content - 日志内容
     * @param options - 日志配置选项
     */
    info: function info(title, content, options) {
      return log(title, content, 'info', options);
    },
    /**
     * 警告日志
     * @param title - 日志标题
     * @param content - 日志内容
     * @param options - 日志配置选项
     */
    warn: function warn(title, content, options) {
      return log(title, content, 'warn', options);
    },
    /**
     * 错误日志
     * @param title - 日志标题
     * @param content - 日志内容
     * @param options - 日志配置选项
     */
    error: function error(title, content, options) {
      return log(title, content, 'error', options);
    },
    /**
     * 调试开关状态
     */
    enabled: debug === null || debug === void 0 ? void 0 : debug.enabled
  };
};