import { useContext } from 'react';
import { TableContext } from "../core/context";
/**
 * 默认样式前缀
 * @private
 */
var DEFAULT_PREFIX = 'ant';

/**
 * 生成带前缀的类名工具函数
 * 
 * @description 
 * 用于生成符合组件库规范的 CSS 类名，支持自定义前缀
 * 
 * @param {string} suffixCls - 后缀类名，例如: 'table', 'table-body'
 * @param {string} [customPrefixCls] - 可选的自定义前缀，默认为 'ant'
 * @returns {string} 完整的类名，例如: 'ant-table', 'custom-table-body'
 * 
 * @example
 * ```ts
 * getPrefixCls('table') // -> 'ant-table'
 * getPrefixCls('table-body', 'custom') // -> 'custom-table-body'
 * ```
 */
export var getPrefixCls = function getPrefixCls(suffixCls, customPrefixCls) {
  if (!suffixCls) {
    throw new Error('[getPrefixCls] suffixCls is required');
  }
  var prefix = customPrefixCls || DEFAULT_PREFIX;
  return "".concat(prefix, "-").concat(suffixCls);
};

/**
 * 从 Context 中获取并生成类名的 Hook
 * 
 * @description
 * 用于组件内部获取统一的类名前缀，自动从 TableContext 中获取 prefixCls
 * 
 * @param {string} suffixCls - 后缀类名，例如: 'table', 'table-body'
 * @returns {string} 根据 context 中的 prefixCls 生成的完整类名
 * 
 * @example
 * ```tsx
 * const MyComponent: React.FC = () => {
 *   const cls = usePrefixCls('table-cell');
 *   return <div className={cls}>content</div>;
 * };
 * ```
 */
export var usePrefixCls = function usePrefixCls(suffixCls) {
  var context = useContext(TableContext);
  return getPrefixCls(suffixCls, context === null || context === void 0 ? void 0 : context.prefixCls);
};

/**
 * @fileoverview
 * 提供类名前缀相关的工具函数
 * 
 * 主要功能:
 * 1. 生成带前缀的类名
 * 2. 提供从 Context 获取前缀的 Hook
 * 
 * @example
 * ```tsx
 * // 直接使用工具函数
 * const cls = getPrefixCls('table'); // 'ant-table'
 * 
 * // 在组件中使用 Hook
 * const MyComponent = () => {
 *   const cls = usePrefixCls('table');
 *   return <div className={cls} />;
 * };
 * ```
 */