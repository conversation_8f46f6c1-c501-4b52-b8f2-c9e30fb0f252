function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
import "antd/es/message/style";
import _message from "antd/es/message";
function _regeneratorRuntime() { "use strict"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = "function" == typeof Symbol ? Symbol : {}, a = i.iterator || "@@iterator", c = i.asyncIterator || "@@asyncIterator", u = i.toStringTag || "@@toStringTag"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, ""); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, "_invoke", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: "normal", arg: t.call(e, r) }; } catch (t) { return { type: "throw", arg: t }; } } e.wrap = wrap; var h = "suspendedStart", l = "suspendedYield", f = "executing", s = "completed", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { ["next", "throw", "return"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if ("throw" !== c.type) { var u = c.arg, h = u.value; return h && "object" == _typeof(h) && n.call(h, "__await") ? e.resolve(h.__await).then(function (t) { invoke("next", t, i, a); }, function (t) { invoke("throw", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke("throw", t, i, a); }); } a(c.arg); } var r; o(this, "_invoke", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw new Error("Generator is already running"); if (o === s) { if ("throw" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if ("next" === n.method) n.sent = n._sent = n.arg;else if ("throw" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else "return" === n.method && n.abrupt("return", n.arg); o = f; var p = tryCatch(e, r, n); if ("normal" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } "throw" === p.type && (o = s, n.method = "throw", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, "throw" === n && e.iterator.return && (r.method = "return", r.arg = t, maybeInvokeDelegate(e, r), "throw" === r.method) || "return" !== n && (r.method = "throw", r.arg = new TypeError("The iterator does not provide a '" + n + "' method")), y; var i = tryCatch(o, e.iterator, r.arg); if ("throw" === i.type) return r.method = "throw", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, "return" !== r.method && (r.method = "next", r.arg = t), r.delegate = null, y) : a : (r.method = "throw", r.arg = new TypeError("iterator result is not an object"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = "normal", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: "root" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || "" === e) { var r = e[a]; if (r) return r.call(e); if ("function" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(_typeof(e) + " is not iterable"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, "constructor", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, "constructor", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, "GeneratorFunction"), e.isGeneratorFunction = function (t) { var e = "function" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || "GeneratorFunction" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, "GeneratorFunction")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, "Generator"), define(g, a, function () { return this; }), define(g, "toString", function () { return "[object Generator]"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = "next", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) "t" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if ("throw" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = "throw", a.arg = e, r.next = n, o && (r.method = "next", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if ("root" === i.tryLoc) return handle("end"); if (i.tryLoc <= this.prev) { var c = n.call(i, "catchLoc"), u = n.call(i, "finallyLoc"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw new Error("try statement without catch or finally"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, "finallyLoc") && this.prev < o.finallyLoc) { var i = o; break; } } i && ("break" === t || "continue" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = "next", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if ("throw" === t.type) throw t.arg; return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if ("throw" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw new Error("illegal catch attempt"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, "next" === this.method && (this.arg = t), y; } }, e; }
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }
function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
import React, { useEffect, useState, useRef, useMemo, memo } from "react";
import { DisplayCell } from "../components/renderers/Cell";
import { EditorCell } from "../components/renderers/EditableCell";
import { EditorPopup } from "../components/overlay/EditorPopup";
import { useTableContext } from "./context";
import { useTableDebug } from "../utils/debug";
import { validateCell } from "../utils/validator";
import isEqual from "lodash/isEqual";
import { jsx as _jsx } from "react/jsx-runtime";
/**
 * CellEditor 组件 - 单元格编辑器
 * 
 * 业务功能:
 * 1. 编辑状态管理
 *    - 管理单元格的编辑状态
 *    - 处理值的变更和临时存储
 *    - 提供乐观更新支持
 * 
 * 2. 数据验证与保存
 *    - 执行字段验证规则
 *    - 处理保存和取消操作
 *    - 支持自定义保存逻辑
 * 
 * 3. 界面交互
 *    - 渲染编辑器弹出层
 *    - 支持自定义编辑器
 *    - 处理编辑器的显示和隐藏
 * 
 * 4. 状态同步
 *    - 同步编辑状态到上层组件
 *    - 处理编辑状态变更回调
 */
export var CellEditor = /*#__PURE__*/memo(function (_ref) {
  var _editingCell$editor10, _editingCell$editor11, _editingCell$editor12, _editingCell$editor13;
  var onEditStateChange = _ref.onEditStateChange;
  // ================ Hooks & Context ================
  var _useTableContext = useTableContext(),
    startEdit = _useTableContext.startEdit,
    setInitialCellValue = _useTableContext.setInitialCellValue,
    editingCellsMap = _useTableContext.editingCellsMap,
    updateTrigger = _useTableContext.updateTrigger,
    generateCellKey = _useTableContext.generateCellKey;
  var debug = useTableDebug();

  // ================ State ================
  var _useState = useState(""),
    _useState2 = _slicedToArray(_useState, 2),
    currentValue = _useState2[0],
    setCurrentValue = _useState2[1];
  var valueRef = useRef(currentValue);
  var editorRef = useRef(null);

  // 乐观更新的临时值状态
  var _useState3 = useState(null),
    _useState4 = _slicedToArray(_useState3, 2),
    optimisticValue = _useState4[0],
    setOptimisticValue = _useState4[1];

  // ================ Computed Values ================
  // 获取当前编辑的单元格
  var editingCell = useMemo(function () {
    if (!(editingCellsMap !== null && editingCellsMap !== void 0 && editingCellsMap.size)) return null;
    return Array.from(editingCellsMap.values())[0];
  }, [editingCellsMap, updateTrigger]);

  // ================ Effects ================
  /**
   * 监听编辑单元格变化
   * 当开始编辑新单元格时，初始化相关状态
   */
  useEffect(function () {
    if (editingCell) {
      setCurrentValue(editingCell.value);
      valueRef.current = editingCell.value;
      setInitialCellValue(editingCell.value);
    }
  }, [editingCell, setInitialCellValue]);

  // 如果没有正在编辑的单元格，不渲染任何内容
  if (!editingCell) return null;

  // ================ Event Handlers ================
  /**
   * 处理值变更
   * 更新内部状态并同步到编辑上下文
   * @param newValue 新的单元格值
   */
  var handleValueChange = function handleValueChange(newValue) {
    var _currentEditingCell$i;
    setCurrentValue(newValue);
    valueRef.current = newValue;
    var cellKey = generateCellKey(editingCell.rowIndex, editingCell.column.key);
    var currentEditingCell = editingCellsMap.get(cellKey);
    startEdit({
      from: 'handleValueChange',
      cell: editingCell ? _objectSpread(_objectSpread({}, editingCell), {}, {
        value: newValue,
        initialValue: (_currentEditingCell$i = currentEditingCell === null || currentEditingCell === void 0 ? void 0 : currentEditingCell.initialValue) !== null && _currentEditingCell$i !== void 0 ? _currentEditingCell$i : editingCell.value
      }) : null
    });
  };

  /**
   * 验证单元格值
   * @param value 要验证的值
   * @param record 当前行数据
   * @returns 验证结果
   */
  var validateValue = /*#__PURE__*/function () {
    var _ref2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(value, record) {
      var _editingCell$column$e, _validators$required;
      var validators;
      return _regeneratorRuntime().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            validators = (_editingCell$column$e = editingCell.column.editor) === null || _editingCell$column$e === void 0 ? void 0 : _editingCell$column$e.validators;
            if (validators) {
              _context.next = 3;
              break;
            }
            return _context.abrupt("return", true);
          case 3:
            return _context.abrupt("return", validateCell(value, {
              required: (_validators$required = validators === null || validators === void 0 ? void 0 : validators.required) !== null && _validators$required !== void 0 ? _validators$required : false,
              custom: validators !== null && validators !== void 0 && validators.custom ? function (value) {
                var _validators$custom, _validators$custom2;
                return (_validators$custom = (_validators$custom2 = validators.custom) === null || _validators$custom2 === void 0 ? void 0 : _validators$custom2.call(validators, value, record)) !== null && _validators$custom !== void 0 ? _validators$custom : true;
              } : undefined
            }));
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function validateValue(_x, _x2) {
      return _ref2.apply(this, arguments);
    };
  }();

  /**
   * 处理保存操作
   * 执行验证、乐观更新和保存流程
   */
  var handleSave = /*#__PURE__*/function () {
    var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee2(customData) {
      var latestValue, cellKey, currentEditingCell, _currentEditingCell$c, isValid, _currentEditingCell$c2;
      return _regeneratorRuntime().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            latestValue = valueRef.current;
            cellKey = generateCellKey(editingCell.rowIndex, editingCell.column.key);
            currentEditingCell = editingCellsMap.get(cellKey);
            _context2.prev = 3;
            if (currentEditingCell) {
              _context2.next = 7;
              break;
            }
            console.warn('找不到对应的编辑单元格:', cellKey);
            return _context2.abrupt("return");
          case 7:
            if (!isEqual(latestValue, currentEditingCell.initialValue)) {
              _context2.next = 14;
              break;
            }
            if (!onEditStateChange) {
              _context2.next = 11;
              break;
            }
            _context2.next = 11;
            return onEditStateChange({
              value: latestValue,
              record: currentEditingCell.record,
              column: currentEditingCell.column,
              type: "unchanged"
            });
          case 11:
            debug.info("值未变更，退出编辑", "");
            startEdit({
              from: 'handleSave_unchanged',
              cell: null,
              currentCell: {
                rowIndex: editingCell.rowIndex,
                columnKey: editingCell.column.key
              }
            });
            return _context2.abrupt("return");
          case 14:
            if (!((_currentEditingCell$c = currentEditingCell.column.editor) !== null && _currentEditingCell$c !== void 0 && _currentEditingCell$c.validators)) {
              _context2.next = 24;
              break;
            }
            _context2.next = 17;
            return validateValue(latestValue, currentEditingCell.record);
          case 17:
            isValid = _context2.sent;
            if (isValid) {
              _context2.next = 24;
              break;
            }
            if (!onEditStateChange) {
              _context2.next = 22;
              break;
            }
            _context2.next = 22;
            return onEditStateChange({
              value: latestValue,
              record: currentEditingCell.record,
              column: currentEditingCell.column,
              type: "invalid"
            });
          case 22:
            startEdit({
              from: 'handleSave_invalid',
              cell: null,
              currentCell: {
                rowIndex: editingCell.rowIndex,
                columnKey: editingCell.column.key
              }
            });
            return _context2.abrupt("return");
          case 24:
            // 乐观更新处理
            setOptimisticValue(latestValue);
            _context2.prev = 25;
            if (!((_currentEditingCell$c2 = currentEditingCell.column.editor) !== null && _currentEditingCell$c2 !== void 0 && _currentEditingCell$c2.onEditComplete)) {
              _context2.next = 33;
              break;
            }
            _context2.next = 29;
            return currentEditingCell.column.editor.onEditComplete(latestValue, currentEditingCell.record, customData);
          case 29:
            if (!onEditStateChange) {
              _context2.next = 32;
              break;
            }
            _context2.next = 32;
            return onEditStateChange({
              value: latestValue,
              record: currentEditingCell.record,
              column: currentEditingCell.column,
              type: "success"
            });
          case 32:
            debug.info("单元格保存", "编辑器");
          case 33:
            // 保存成功，清理状态
            setOptimisticValue(null);
            startEdit({
              from: 'handleSave_success',
              cell: null,
              currentCell: {
                rowIndex: editingCell.rowIndex,
                columnKey: editingCell.column.key
              }
            });
            _context2.next = 41;
            break;
          case 37:
            _context2.prev = 37;
            _context2.t0 = _context2["catch"](25);
            // 保存失败，回滚显示
            setOptimisticValue(null);
            throw _context2.t0;
          case 41:
            _context2.next = 47;
            break;
          case 43:
            _context2.prev = 43;
            _context2.t1 = _context2["catch"](3);
            console.error("Save failed:", _context2.t1);
            _message.error(_context2.t1 instanceof Error ? _context2.t1.message : "保存失败");
          case 47:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[3, 43], [25, 37]]);
    }));
    return function handleSave(_x3) {
      return _ref3.apply(this, arguments);
    };
  }();

  /**
   * 处理编辑器关闭
   * @param reason 关闭原因: save | cancel | blur
   * @param customData 自定义数据
   */
  var handleClose = function handleClose(reason, customData) {
    var _editingCell$editor;
    if (reason === "blur" && (editingCell === null || editingCell === void 0 || (_editingCell$editor = editingCell.editor) === null || _editingCell$editor === void 0 ? void 0 : _editingCell$editor.saveOnBlur) === false) {
      startEdit({
        from: 'handleClose_blur',
        cell: null,
        currentCell: {
          rowIndex: editingCell.rowIndex,
          columnKey: editingCell.column.key
        }
      });
      if (onEditStateChange) {
        onEditStateChange({
          value: editingCell.value,
          record: editingCell.record,
          column: editingCell.column,
          type: "unchanged"
        });
      }
      return;
    }
    if (reason === "cancel") {
      startEdit({
        from: 'handleClose_cancel',
        cell: null,
        currentCell: {
          rowIndex: editingCell.rowIndex,
          columnKey: editingCell.column.key
        }
      });
      if (onEditStateChange) {
        onEditStateChange({
          value: editingCell.value,
          record: editingCell.record,
          column: editingCell.column,
          type: "cancelled"
        });
      }
      return;
    }
    handleSave(customData);
  };

  // ================ Render Functions ================

  /**
   * 渲染编辑器内容
   * 支持自定义编辑器和默认编辑器
   */
  var renderEditor = function renderEditor() {
    var _editingCell$editor2, _editingCell$editor4, _editingCell$editor5;
    var displayValue = optimisticValue !== null && optimisticValue !== void 0 ? optimisticValue : currentValue;
    var CustomEditor = (_editingCell$editor2 = editingCell.editor) === null || _editingCell$editor2 === void 0 ? void 0 : _editingCell$editor2.editorRender;
    if (CustomEditor) {
      var _editingCell$editor3;
      return /*#__PURE__*/_jsx(CustomEditor, _objectSpread({
        mode: "edit",
        value: displayValue,
        onChange: handleValueChange,
        ref: editorRef,
        record: editingCell.record,
        onSave: function onSave(params) {
          var _params$value;
          handleValueChange((_params$value = params === null || params === void 0 ? void 0 : params.value) !== null && _params$value !== void 0 ? _params$value : displayValue);
          handleClose("save", params === null || params === void 0 ? void 0 : params.customData);
        },
        onCancel: function onCancel() {
          return handleClose("cancel");
        }
      }, ((_editingCell$editor3 = editingCell.editor) === null || _editingCell$editor3 === void 0 ? void 0 : _editingCell$editor3.props) || {}));
    }
    return /*#__PURE__*/_jsx(EditorCell, _objectSpread({
      mode: "edit",
      type: ((_editingCell$editor4 = editingCell.editor) === null || _editingCell$editor4 === void 0 ? void 0 : _editingCell$editor4.type) || "text",
      value: displayValue,
      record: editingCell.record,
      onCellChange: handleValueChange,
      onSave: function onSave() {
        return handleClose("save");
      },
      onCancel: function onCancel() {
        return handleClose("cancel");
      }
    }, ((_editingCell$editor5 = editingCell.editor) === null || _editingCell$editor5 === void 0 ? void 0 : _editingCell$editor5.props) || {}));
  };

  /**
   * 渲染编辑器悬浮内容
   * 用于显示预览和辅助信息
   */
  var renderEditingOverlay = function renderEditingOverlay() {
    var _editingCell$editor6, _editingCell$editor7, _editingCell$editor8;
    if (!editingCell) return null;
    return /*#__PURE__*/_jsx(DisplayCell, _objectSpread(_objectSpread({}, (_editingCell$editor6 = editingCell.editor) === null || _editingCell$editor6 === void 0 ? void 0 : _editingCell$editor6.props), {}, {
      type: ((_editingCell$editor7 = editingCell.editor) === null || _editingCell$editor7 === void 0 ? void 0 : _editingCell$editor7.type) || "text",
      value: currentValue,
      editor: editingCell.editor,
      displayRender: (_editingCell$editor8 = editingCell.editor) === null || _editingCell$editor8 === void 0 ? void 0 : _editingCell$editor8.displayRender,
      onClear: function onClear() {
        var _editingCell$editor9;
        if ((_editingCell$editor9 = editingCell.editor) !== null && _editingCell$editor9 !== void 0 && (_editingCell$editor9 = _editingCell$editor9.overlayProps) !== null && _editingCell$editor9 !== void 0 && _editingCell$editor9.onClear) {
          editingCell.editor.overlayProps.onClear({
            record: editingCell.record,
            editor: editorRef,
            onChange: function onChange(value) {
              return handleValueChange(value);
            }
          });
        } else {
          handleValueChange(null);
        }
      }
    }));
  };

  // ================ Component Render ================
  return /*#__PURE__*/_jsx(EditorPopup, {
    position: editingCell === null || editingCell === void 0 ? void 0 : editingCell.position,
    cellTarget: editingCell === null || editingCell === void 0 ? void 0 : editingCell.cellTarget,
    type: (_editingCell$editor10 = editingCell.editor) === null || _editingCell$editor10 === void 0 ? void 0 : _editingCell$editor10.type,
    placement: (editingCell === null || editingCell === void 0 || (_editingCell$editor11 = editingCell.editor) === null || _editingCell$editor11 === void 0 ? void 0 : _editingCell$editor11.type) === "text" || (editingCell === null || editingCell === void 0 || (_editingCell$editor12 = editingCell.editor) === null || _editingCell$editor12 === void 0 ? void 0 : _editingCell$editor12.type) === "number" ? "overlay" : "below",
    visible: Boolean(editingCell),
    onClose: handleClose,
    popupProps: ((_editingCell$editor13 = editingCell.editor) === null || _editingCell$editor13 === void 0 ? void 0 : _editingCell$editor13.popupProps) || {},
    overlayContent: renderEditingOverlay(),
    children: renderEditor()
  });
});
CellEditor.displayName = 'CellEditor';