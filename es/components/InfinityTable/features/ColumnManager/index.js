function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
import "antd/es/modal/style";
import _Modal from "antd/es/modal";
import "antd/es/empty/style";
import _Empty from "antd/es/empty";
import "antd/es/tooltip/style";
import _Tooltip from "antd/es/tooltip";
import "antd/es/button/style";
import _Button from "antd/es/button";
import "antd/es/image/style";
import _Image from "antd/es/image";
function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }
function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
/**
 * @file Column Manager Component
 * @description 表格列管理器组件，用于管理表格列的显示、隐藏和排序
 * @review 2024/12/19
 */

import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { SettingOutlined } from '@ant-design/icons';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import { useTableDebug } from "../../utils/debug";
import "./index.less";

/**
 * 可拖拽标签组件属性
 */
import { jsx as _jsx } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
import { Fragment as _Fragment } from "react/jsx-runtime";
/**
 * 可拖拽标签组件
 * 用于已启用列区域的标签显示
 */
var DraggableTag = function DraggableTag(_ref) {
  var title = _ref.title,
    onClose = _ref.onClose,
    unremovable = _ref.unremovable;
  return /*#__PURE__*/_jsxs("div", {
    className: "infinity-table-column-manager-tag infinity-table-column-manager-tag-draggable",
    children: [/*#__PURE__*/_jsx("span", {
      children: title
    }), !unremovable && /*#__PURE__*/_jsx(_Image, {
      src: "https://s.xinc818.com/files/webcim4tkc1x5ry9500/remove.png",
      preview: false,
      width: 16,
      height: 16,
      className: "infinity-table-column-manager-tag-close-icon",
      onClick: function onClick(e) {
        e.stopPropagation();
        onClose();
      }
    })]
  });
};

/**
 * 可选择标签组件属性
 */

/**
 * 可选择标签组件
 * 用于未启用列区域的标签显示
 */
var SelectableTag = function SelectableTag(_ref2) {
  var title = _ref2.title,
    onAdd = _ref2.onAdd;
  return /*#__PURE__*/_jsxs("div", {
    className: "infinity-table-column-manager-tag",
    children: [/*#__PURE__*/_jsx("span", {
      children: title
    }), /*#__PURE__*/_jsx(_Image, {
      src: "https://s.xinc818.com/files/webcim4tkotv25xzbny/add.png",
      preview: false,
      width: 16,
      height: 16,
      className: "infinity-table-column-manager-tag-add-icon",
      onClick: function onClick(e) {
        e.stopPropagation();
        onAdd();
      }
    })]
  });
};

/**
 * 拖拽包装器组件属性
 */

/**
 * 拖拽包装器组件
 * 现列拖拽排序功能
 */
var DraggableWrapper = function DraggableWrapper(_ref3) {
  var column = _ref3.column,
    index = _ref3.index,
    moveColumn = _ref3.moveColumn,
    onClose = _ref3.onClose;
  var ref = useRef(null);
  var _useDrag = useDrag({
      type: 'draggable-column',
      item: function item() {
        return {
          index: index,
          column: column // 类型断言解决类型不兼容问题
        };
      },
      collect: function collect(monitor) {
        return {
          isDragging: monitor.isDragging()
        };
      }
    }),
    _useDrag2 = _slicedToArray(_useDrag, 2),
    isDragging = _useDrag2[0].isDragging,
    drag = _useDrag2[1];
  var _useDrop = useDrop({
      accept: 'draggable-column',
      hover: function hover(item) {
        if (!ref.current) return;
        var dragIndex = item.index;
        var hoverIndex = index;
        if (dragIndex === hoverIndex) return;
        moveColumn(dragIndex, hoverIndex);
        item.index = hoverIndex;
      }
    }),
    _useDrop2 = _slicedToArray(_useDrop, 2),
    drop = _useDrop2[1];
  drag(drop(ref));
  return /*#__PURE__*/_jsx("div", {
    ref: ref,
    style: {
      opacity: isDragging ? 0.3 : 1
    },
    className: "infinity-table-column-manager-draggable-wrapper",
    children: /*#__PURE__*/_jsx(DraggableTag, {
      title: extractTitle(column.title),
      onClose: onClose,
      unremovable: column.unremovable
    })
  });
};

/**
 * 添加辅助函数来提取真实的标题文本
 */
var extractTitle = function extractTitle(titleNode) {
  //console.log('titleNode', titleNode);
  if (typeof titleNode === 'string') {
    return titleNode;
  }
  // 如果是 TitleRender 组件，提取 name 属性
  if ( /*#__PURE__*/React.isValidElement(titleNode) && 'props' in titleNode) {
    return titleNode.props.name || '';
  }
  return '';
};

/**
 * 列管理器组件属性
 */

/**
 * 列管理器组件
 * 管理表格列的显示、隐藏和排序
 */
export function ColumnManager(_ref4) {
  var _ref4$columns = _ref4.columns,
    columns = _ref4$columns === void 0 ? [] : _ref4$columns,
    _ref4$defaultColumns = _ref4.defaultColumns,
    defaultColumns = _ref4$defaultColumns === void 0 ? [] : _ref4$defaultColumns,
    onChange = _ref4.onChange;
  var debug = useTableDebug();
  var _useState = useState(false),
    _useState2 = _slicedToArray(_useState, 2),
    visible = _useState2[0],
    setVisible = _useState2[1];

  // 添加状态管理
  var _useState3 = useState([]),
    _useState4 = _slicedToArray(_useState3, 2),
    enabledItems = _useState4[0],
    setEnabledItems = _useState4[1];
  var _useState5 = useState([]),
    _useState6 = _slicedToArray(_useState5, 2),
    disabledItems = _useState6[0],
    setDisabledItems = _useState6[1];

  // 过滤出可以管理的列（非固定列）
  var managableColumns = useMemo(function () {
    return columns.filter(function (col) {
      return !col.fixed;
    });
  }, [columns]);

  // 初始化列状态
  var initializeColumns = useCallback(function () {
    var enabled = [];
    var disabled = [];

    // 使用 managableColumns 作为初始数据源
    managableColumns.forEach(function (col) {
      var columnItem = _objectSpread(_objectSpread({}, col), {}, {
        key: col.dataIndex,
        enabled: col.show !== false
      });
      (columnItem.enabled ? enabled : disabled).push(columnItem);
    });
    return {
      enabled: enabled,
      disabled: disabled
    };
  }, [managableColumns]);

  // 每次打开弹窗时初始化列状态
  useEffect(function () {
    if (visible) {
      var _initializeColumns = initializeColumns(),
        enabled = _initializeColumns.enabled,
        disabled = _initializeColumns.disabled;
      setEnabledItems(enabled);
      setDisabledItems(disabled);
    }
  }, [visible, initializeColumns]);

  // 重置为默认设置
  var resetToDefault = useCallback(function () {
    if (!(defaultColumns !== null && defaultColumns !== void 0 && defaultColumns.length)) {
      var _enabled = managableColumns.map(function (col) {
        return _objectSpread(_objectSpread({}, col), {}, {
          key: col.dataIndex,
          enabled: true
        });
      });
      setEnabledItems(_enabled);
      setDisabledItems([]);
      return;
    }

    // 如果有 defaultColumns，则使用它作为数据源
    var enabled = [];
    var disabled = [];
    defaultColumns.forEach(function (col) {
      var columnItem = _objectSpread(_objectSpread({}, col), {}, {
        key: col.dataIndex,
        enabled: col.show !== false
      });
      (columnItem.enabled ? enabled : disabled).push(columnItem);
    });
    setEnabledItems(enabled);
    setDisabledItems(disabled);
  }, [defaultColumns, managableColumns]);

  // 处理列拖拽排序
  var moveColumn = useCallback(function (dragIndex, hoverIndex) {
    setEnabledItems(function (prevColumns) {
      return update(prevColumns, {
        $splice: [[dragIndex, 1], [hoverIndex, 0, prevColumns[dragIndex]]]
      });
    });
  }, []);

  // 切换列的启用状态
  var toggleColumn = useCallback(function (item) {
    if (item.enabled) {
      setEnabledItems(function (prev) {
        return prev.filter(function (col) {
          return col.key !== item.key;
        });
      });
      setDisabledItems(function (prev) {
        return [].concat(_toConsumableArray(prev), [_objectSpread(_objectSpread({}, item), {}, {
          enabled: false
        })]);
      });
    } else {
      setDisabledItems(function (prev) {
        return prev.filter(function (col) {
          return col.key !== item.key;
        });
      });
      setEnabledItems(function (prev) {
        return [].concat(_toConsumableArray(prev), [_objectSpread(_objectSpread({}, item), {}, {
          enabled: true
        })]);
      });
    }
  }, []);

  // 启用所有列(debug功能)
  var enableAllColumns = useCallback(function () {
    setEnabledItems(function (prev) {
      return [].concat(_toConsumableArray(prev), _toConsumableArray(disabledItems.map(function (item) {
        return _objectSpread(_objectSpread({}, item), {}, {
          enabled: true
        });
      })));
    });
    setDisabledItems([]);
  }, [disabledItems]);

  // 保存列设置
  var handleSave = useCallback(function () {
    var fixedColumns = columns.filter(function (col) {
      return col.fixed;
    });
    var managedColumns = enabledItems.map(function (item) {
      return item.dataIndex;
    });
    var fixedColumnKeys = fixedColumns.map(function (col) {
      return col.dataIndex;
    });
    var newColumnKeys = [].concat(_toConsumableArray(managedColumns), _toConsumableArray(fixedColumnKeys));
    onChange(newColumnKeys);
    setVisible(false);
  }, [columns, enabledItems, onChange]);
  return /*#__PURE__*/_jsxs(_Fragment, {
    children: [/*#__PURE__*/_jsx(_Tooltip, {
      title: "\u5217\u8BBE\u7F6E",
      placement: "topRight",
      overlayClassName: "infinity-table-column-manager-tooltip",
      children: /*#__PURE__*/_jsx(_Button, {
        type: "text",
        icon: /*#__PURE__*/_jsx(SettingOutlined, {
          style: {
            color: '#8E97A2',
            fontSize: '14px'
          }
        }),
        onClick: function onClick() {
          return setVisible(true);
        },
        className: "infinity-table-column-manager-settings-button"
      })
    }), /*#__PURE__*/_jsx(_Modal, {
      title: "\u81EA\u5B9A\u4E49\u663E\u793A\u5217",
      open: visible,
      width: 800,
      onCancel: function onCancel() {
        return setVisible(false);
      },
      footer: /*#__PURE__*/_jsxs("div", {
        className: "infinity-table-column-manager-footer",
        children: [/*#__PURE__*/_jsxs("div", {
          className: "infinity-table-column-manager-footer-left",
          children: [/*#__PURE__*/_jsx(_Button, {
            className: "infinity-table-column-manager-default-btn",
            onClick: resetToDefault,
            children: "\u6062\u590D\u9ED8\u8BA4"
          }), (debug === null || debug === void 0 ? void 0 : debug.enabled) && /*#__PURE__*/_jsx(_Button, {
            className: "infinity-table-column-manager-debug-btn",
            onClick: enableAllColumns,
            children: "\u4E00\u952E\u8BBE\u7F6E"
          })]
        }), /*#__PURE__*/_jsxs("div", {
          className: "infinity-table-column-manager-footer-right",
          children: [/*#__PURE__*/_jsx(_Button, {
            onClick: handleSave,
            className: "infinity-table-column-manager-confirm-btn",
            children: "\u786E\u5B9A"
          }), /*#__PURE__*/_jsx(_Button, {
            onClick: function onClick() {
              return setVisible(false);
            },
            className: "infinity-table-column-manager-cancel-btn",
            children: "\u53D6\u6D88"
          })]
        })]
      }),
      className: "infinity-table-column-manager-modal",
      children: /*#__PURE__*/_jsxs("div", {
        className: "infinity-table-column-manager",
        children: [/*#__PURE__*/_jsxs("div", {
          className: "infinity-table-column-manager-enabled-columns",
          children: [/*#__PURE__*/_jsx("div", {
            className: "infinity-table-column-manager-header",
            children: "\u62D6\u52A8\u533A\u5757\u8C03\u6574\u663E\u793A\u987A\u5E8F"
          }), /*#__PURE__*/_jsx(DndProvider, {
            backend: HTML5Backend,
            children: /*#__PURE__*/_jsx("div", {
              className: "infinity-table-column-manager-draggable-container",
              children: enabledItems.map(function (column, index) {
                return /*#__PURE__*/_jsx(DraggableWrapper, {
                  column: column,
                  index: index,
                  moveColumn: moveColumn,
                  onClose: function onClose() {
                    return toggleColumn(column);
                  }
                }, column.key);
              })
            })
          })]
        }), /*#__PURE__*/_jsxs("div", {
          className: "infinity-table-column-manager-disabled-columns",
          children: [/*#__PURE__*/_jsx("div", {
            className: "infinity-table-column-manager-header",
            children: "\u9009\u62E9\u663E\u793A\u5217"
          }), /*#__PURE__*/_jsx("div", {
            className: "infinity-table-column-manager-static-container",
            children: disabledItems.length > 0 ? disabledItems.map(function (column) {
              return /*#__PURE__*/_jsx("div", {
                className: "infinity-table-column-manager-draggable-wrapper",
                children: /*#__PURE__*/_jsx(SelectableTag, {
                  title: extractTitle(column.title),
                  onAdd: function onAdd() {
                    return toggleColumn(column);
                  }
                })
              }, column.key);
            }) : /*#__PURE__*/_jsx(_Empty, {
              image: "https://s.xinc818.com/files/webcilk3f2akdfsqng6/<EMAIL>",
              imageStyle: {
                width: '100px',
                height: '100px'
              },
              description: "\u6682\u65E0\u53EF\u6DFB\u52A0\u5217",
              style: {
                margin: '20px auto',
                color: '#86909C'
              }
            })
          })]
        })]
      })
    })]
  });
}