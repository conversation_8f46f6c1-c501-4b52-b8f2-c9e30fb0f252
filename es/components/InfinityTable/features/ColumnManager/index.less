.infinity-table-column-manager-settings-button {
  height: 100% !important;
  width: 36px;
  position: absolute;
  right: -15px;
  z-index: 2;
  background: #F2F3F5 !important;
  top: 0;
  &:hover {
    background: #E5E6EB !important;
  }
}

.infinity-table-column-manager-tag {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background: #fff;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: border-color 0.3s;
  span {
    margin-right: 4px;
  }

  &:hover {
    border-color: #40a9ff;

    .infinity-table-column-manager-tag-add-icon {
      opacity: 1;
    }
  }

  &.infinity-table-column-manager-tag-draggable {
    cursor: move;
  }

  .infinity-table-column-manager-tag-close-icon {
    position: absolute;
    width: 16px;
    height: 16px;
    color: #1890ff;
    background: #fff;
    border-radius: 50%;
    border: 1px solid #1890ff;
    opacity: 0;
    transition: opacity 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    font-size: 12px;
    
    &:hover {
      cursor: pointer;
    }
  }

  .infinity-table-column-manager-tag-add-icon {
    position: absolute;
    width: 16px;
    height: 16px;
    color: #1890ff;
    background: #fff;
    border-radius: 50%;
    border: 1px solid #1890ff;
    opacity: 1;
    transition: opacity 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    font-size: 12px;
    
    &:hover {
      cursor: pointer;
    }
  }
}

.infinity-table-column-manager-modal {
  font-family: PingFangSC-Regular;
  .infinity-table-column-manager-enabled-columns {
    // border-radius: 8px;
    margin-bottom: 16px;
    // min-height: 120px;

    .infinity-table-column-manager-header {
      padding: 12px 16px;
      color: #86909C;
      font-size: 12px;
    }
  }

  .infinity-table-column-manager-disabled-columns {
    .infinity-table-column-manager-header {
      margin-bottom: 12px;
      color: #1A1A1A;
      font-weight: bold;
    }
  }
}

.infinity-table-column-manager {
  .infinity-table-column-manager-enabled-columns {
    border: 1px dashed #d9d9d9;
    
    .infinity-table-column-manager-draggable-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding: 0 16px 16px;
      background: transparent;
      border-radius: 4px;
      // min-height: 120px;

      > * {
        flex: 0 0 auto;
      }
    }
  }

  .infinity-table-column-manager-disabled-columns {
    .infinity-table-column-manager-static-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding: 0px;
    }
  }

  .infinity-table-column-manager-tag {
    display: inline-flex;
    align-items: center;
    padding-left: 8px;
    padding-right: 4px;
    height: 20px;
    line-height: 20px;
    background: #F7F8FA;
    border-radius: 2px;
    color: #1D2129;
    font-size: 12px;
    
    &.infinity-table-column-manager-tag-draggable {
      cursor: move;
    }

    .infinity-table-column-manager-tag-close-icon,
    .infinity-table-column-manager-tag-add-icon {
      position: static;
      width: 16px;
      height: 16px;
      // margin-left: 4px;
      font-size: 12px;
      color: #999;
      background: none;
      border: none;
      opacity: 1;
      
      &:hover {
        color: #666;
        background: none;
      }
    }
  }


  &-default-btn {
    background: #FFFFFF;
    border: 1px solid #86909C;
    border-radius: 2px;
    color: #4E5969;
    margin-right: 8px;
    text-align: center;
    font-size: 12px;
    padding: 0;
    width: 72px;
    height: 24px;;
    &:hover, &:focus {
      background: #FFFFFF;
      border: 1px solid #86909C;
      color: #4E5969;
    }
  }

  &-debug-btn {
    background: #FFFFFF;
    border: 1px solid #86909C;
    border-radius: 2px;
  }

  &-confirm-btn {
    background: #FF3029;
    border-color: #FF3029;
    color: #FFFFFF;
    width: 48px;
    padding: 0;
    height: 24px;
    font-size: 12px;
    margin-right: 8px;
    &:hover, &:focus {
      background: #FF3029;
      border-color: #FF3029;
      color: #FFFFFF;
    }
  }

  &-cancel-btn {
    background: #FFFFFF;
    border: 1px solid #86909C;
    color: #4E5969;
    width: 48px;
    font-size: 12px;
    padding: 0;
    height: 24px;
    margin-right: 8px;
    &:hover ,  &:focus {
      background: #FFFFFF;
      border: 1px solid #86909C;
      color: #4E5969;
    }
  }

  &-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-left {
      display: flex;
      align-items: center;
    }

    &-right {
      display: flex;
      align-items: center;
    }
  }
}

.infinity-table-column-manager-draggable-wrapper {
  display: inline-block;
  height: fit-content;
  width: fit-content;
}

.infinity-table-column-manager-tooltip {
  [class$="-tooltip-content"] {
    transform: translateY(5px); /* 向上移动 Tooltip 内容 */
  }
  [class$="-tooltip-arrow"] {
    right: 7px !important;
  }
  [class$="-tooltip-inner"] {
    font-size: 12px !important;
    width: 60px !important;
    min-width: 60px !important;
    text-align: center;
    padding: 7px 12px !important;
    margin-top: 6px !important;
  }
}