function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
import { useState, useCallback, useRef, useEffect } from 'react';
import { throttle } from 'lodash';
var INITIAL_STATE = {
  visible: false,
  isPermissionTip: false,
  message: undefined,
  position: {
    x: 0,
    y: 0
  }
};
export var useTooltip = function useTooltip() {
  // 1. 使用 useRef 存储状态,避免重渲染
  var _useState = useState(INITIAL_STATE),
    _useState2 = _slicedToArray(_useState, 2),
    tooltipState = _useState2[0],
    setTooltipState = _useState2[1];
  var tooltipRef = useRef(null);

  // 2. 将 throttle 函数移到 useRef 外部
  var handleMouseMove = useCallback(function (e) {
    setTooltipState(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        position: {
          x: e.clientX,
          y: e.clientY - 10
        }
      });
    });
  }, []);

  // 3. 使用 useRef 存储 throttle 函数
  var throttledMouseMove = useRef(throttle(handleMouseMove, 16));

  // 4. 优化 handleCellHover,减少依赖
  var handleCellHover = useCallback(function (event, isPermissionTip, message) {
    event.persist();
    if (!message && !isPermissionTip) {
      return;
    }
    setTooltipState({
      visible: true,
      isPermissionTip: isPermissionTip,
      message: message,
      position: {
        x: event.clientX,
        y: event.clientY - 10
      }
    });
    document.addEventListener('mousemove', throttledMouseMove.current);
  }, []);

  // 5. 抽取重置状态逻辑
  var resetTooltipState = useCallback(function () {
    setTooltipState(INITIAL_STATE);
    document.removeEventListener('mousemove', throttledMouseMove.current);
  }, []);

  // 6. 简化 handleCellLeave
  var handleCellLeave = useCallback(function () {
    // 无论是权限提示还是锁定提示，鼠标离开时都应该清除
    resetTooltipState();
  }, [resetTooltipState]);

  // 7. 清理函数优化
  useEffect(function () {
    var currentThrottled = throttledMouseMove.current;
    return function () {
      currentThrottled.cancel();
      document.removeEventListener('mousemove', currentThrottled);
    };
  }, []);

  // 8. 导出 clearTooltip 方法
  var clearTooltip = useCallback(function () {
    // 如果当前显示的是权限提示(disableControl)，则不清除
    if (tooltipState.isPermissionTip) {
      return;
    }
    // 其他情况使用 resetTooltipState
    resetTooltipState();
  }, [resetTooltipState, tooltipState.isPermissionTip]);
  return {
    tooltipState: tooltipState,
    tooltipRef: tooltipRef,
    handleCellHover: handleCellHover,
    handleCellLeave: handleCellLeave,
    clearTooltip: clearTooltip
  };
};