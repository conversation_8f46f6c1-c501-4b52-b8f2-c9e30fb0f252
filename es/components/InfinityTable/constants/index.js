/**
 * InfinityTable 组件常量配置
 * @description 集中管理组件所需的常量配置，便于统一维护和扩展
 * <AUTHOR>
 * @since 2024-03-21
 */

/**
 * 编辑器类型枚举
 * @description 定义表格支持的所有编辑器类型
 */
export var EDITOR_TYPES = {
  TEXT: 'text',
  // 文本编辑器
  NUMBER: 'number',
  // 数字编辑器
  SELECT: 'select',
  // 单选下拉框
  MULTI_SELECT: 'multi-select',
  // 多选下拉框
  DATE: 'date',
  // 日期选择器
  IMAGES: 'images',
  // 图片上传
  PRICE: 'price',
  // 价格输入
  NUMBER_RANGE: 'number-range',
  // 数字范围
  SEARCH_SELECT: 'search-select' // 搜索选择器
};

// 导出编辑器类型的 TypeScript 类型

/**
 * 编辑器配置对象
 * @description 定义各类编辑器的尺寸和层级配置
 */
export var EDITOR_CONFIGS = {
  // 文本编辑器配置
  TEXT: {
    height: 72,
    zIndex: 10
  },
  // 数字编辑器配置
  NUMBER: {
    height: 72,
    zIndex: 10
  },
  // 日期选择器配置
  DATE: {
    width: 320,
    height: 320,
    zIndex: 100
  },
  // 单选下拉框配置
  SELECT: {
    // width: 232,
    // height: 89,
    // height:64,
    zIndex: 100
  },
  // 多选下拉框配置
  MULTI_SELECT: {
    // width: 200,
    zIndex: 100
  },
  // 图片上传配置, 先注释 业务用到 否则高度不对
  IMAGES: {
    // width: 360,
    // height: 220,
    zIndex: 100
  },
  NUMBER_RANGE: {
    width: 232,
    height: 89,
    zIndex: 100
  },
  // 搜索选择器配置
  SEARCH_SELECT: {
    width: 240,
    height: 228,
    zIndex: 100
  }
};

/**
 * 表格布局配置
 * @description 定义表格的基础布局尺寸
 */
export var TABLE_LAYOUT_CONFIG = {
  HEADER_HEIGHT: 36,
  // 表头高度
  CELL_HEIGHT: 72 // 单元格高度
};

// 导出配置类型，便于类型检查

/**
 * @todo 
 * 1. 考虑添加响应式布局配置
 * 2. 可以添加主题相关的常量配置
 * 3. 国际化相关的常量配置
 */

/** Tooltip 组件的 z-index 值 */
export var TOOLTIP_Z_INDEX = 1000;

/** 默认表格高度 */
export var DEFAULT_TABLE_HEIGHT = 400;

/** 表格前缀类名 */
export var DEFAULT_PREFIX_CLS = 'ant';

/** 默认调试配置 */
export var DEFAULT_DEBUG_CONFIG = {
  enabled: false
};