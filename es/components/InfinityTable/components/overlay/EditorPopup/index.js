function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
import React, { useEffect, useRef, useState } from 'react';
import { EDITOR_CONFIGS } from "../../../constants";
import { useClickAway } from 'ahooks';
import "./index.less";
import { useTableDebug } from "../../../utils/debug";
import ReactDOM from 'react-dom';
import { usePrefixCls } from "../../../utils/getPrefixCls";
import { Fragment as _Fragment } from "react/jsx-runtime";
import { jsx as _jsx } from "react/jsx-runtime";
export var EditorPopup = function EditorPopup(_ref) {
  var children = _ref.children,
    overlayContent = _ref.overlayContent,
    _ref$type = _ref.type,
    type = _ref$type === void 0 ? 'text' : _ref$type,
    visible = _ref.visible,
    onClose = _ref.onClose,
    _ref$placement = _ref.placement,
    placement = _ref$placement === void 0 ? 'below' : _ref$placement,
    cellTarget = _ref.cellTarget,
    _ref$popupProps = _ref.popupProps,
    popupProps = _ref$popupProps === void 0 ? {} : _ref$popupProps;
  var popupRef = useRef(null);
  var editorConfig = type ? EDITOR_CONFIGS[type.toUpperCase()] : null;
  var debug = useTableDebug();
  var prefixCls = usePrefixCls('table');
  var _React$useState = React.useState(0),
    _React$useState2 = _slicedToArray(_React$useState, 2),
    editorKey = _React$useState2[0],
    setEditorKey = _React$useState2[1];

  // 使用两个状态来控制渲染
  var _React$useState3 = React.useState(false),
    _React$useState4 = _slicedToArray(_React$useState3, 2),
    isReady = _React$useState4[0],
    setIsReady = _React$useState4[1];
  var _React$useState5 = React.useState(null),
    _React$useState6 = _slicedToArray(_React$useState5, 2),
    currentTarget = _React$useState6[0],
    setCurrentTarget = _React$useState6[1];

  // 添加一个新的 ref 用于测量实际高度
  var measureRef = useRef(null);
  var _useState = useState(null),
    _useState2 = _slicedToArray(_useState, 2),
    actualHeight = _useState2[0],
    setActualHeight = _useState2[1];

  // 控制渲染时机
  useEffect(function () {
    if (visible && cellTarget) {
      // 先确保旧内容被清除
      setIsReady(false);
      setCurrentTarget(null);

      // 使用两个 RAF 来确保 DOM 完全更新
      requestAnimationFrame(function () {
        requestAnimationFrame(function () {
          setEditorKey(function (prev) {
            return prev + 1;
          });
          setCurrentTarget(cellTarget);
          setIsReady(true);
        });
      });
    } else {
      setIsReady(false);
      setCurrentTarget(null);
    }
  }, [visible, cellTarget]);
  useEffect(function () {
    if (visible) {
      debug.info('编辑浮层', '显示');
    }
  }, [visible]);

  // 修改 useClickAway - 保留点击外部关闭功能
  useClickAway(function (event) {
    if (visible) {
      var _event$composedPath;
      var target = event.target;
      var isClickInCell = cellTarget === null || cellTarget === void 0 ? void 0 : cellTarget.contains(target);
      var clickPath = ((_event$composedPath = event.composedPath) === null || _event$composedPath === void 0 ? void 0 : _event$composedPath.call(event)) || [];
      var isClickInPortal = clickPath.some(function (node) {
        if (node instanceof Element) {
          var _node$getAttribute, _node$getAttribute2, _node$getAttribute3;
          return node.getAttribute('role') === 'listbox' || node.getAttribute('role') === 'tooltip' || node.getAttribute('role') === 'dialog' || ((_node$getAttribute = node.getAttribute('class')) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.includes('-dropdown')) || ((_node$getAttribute2 = node.getAttribute('class')) === null || _node$getAttribute2 === void 0 ? void 0 : _node$getAttribute2.includes('-popup')) || ((_node$getAttribute3 = node.getAttribute('class')) === null || _node$getAttribute3 === void 0 ? void 0 : _node$getAttribute3.includes('-picker-dropdown'));
        }
        return false;
      });
      if (!isClickInCell && !isClickInPortal) {
        setTimeout(function () {
          debug.info('编辑浮层', '关闭');
          var closeAction = type === 'number-range' ? 'cancel' : 'blur';
          onClose(closeAction);
        }, 30);
      }
    }
  }, popupRef, 'mousedown');

  // 处理 overlayContent 的渲染
  useEffect(function () {
    if (type === 'text' || type === 'number') return;
    if (visible && cellTarget && overlayContent) {
      console.log('overlayContent', visible, cellTarget, overlayContent);
      var overlayContainer = document.createElement('div');
      overlayContainer.className = 'editor-cell-overlay';
      overlayContainer.style.cssText = "\n          position: absolute;\n          top: 0px;\n          left: 0px;\n          width: 100%;\n          height: 100%;\n          background: #fff;\n          box-shadow: 0 2px 12px 0 #00000033;\n          // border: 1px solid #4E5969;\n          z-index: 99;\n        ";
      cellTarget.style.position = 'relative';
      cellTarget.appendChild(overlayContainer);
      ReactDOM.render( /*#__PURE__*/_jsx(_Fragment, {
        children: overlayContent
      }), overlayContainer);
      return function () {
        if (cellTarget.contains(overlayContainer)) {
          ReactDOM.unmountComponentAtNode(overlayContainer);
          cellTarget.removeChild(overlayContainer);
        }
        cellTarget.style.position = '';
      };
    }
  }, [visible, cellTarget, overlayContent, type]);

  // 获取编辑器预期高度
  var getEditorHeight = function getEditorHeight() {
    // 如果已经测量到实际高度，优先使用
    if (actualHeight !== null) {
      //console.log('actualHeight',actualHeight);
      return actualHeight;
    }

    // 其他默认逻辑保持不变...
    var configKey = type.toUpperCase().replace('-', '_');
    var editorConfig = EDITOR_CONFIGS[configKey];
    if (configKey.toUpperCase() === 'SELECT') {
      var _children$props;
      var options = (children === null || children === void 0 || (_children$props = children.props) === null || _children$props === void 0 ? void 0 : _children$props.options) || [];
      var itemCount = options.length;
      var contentHeight = itemCount * 32;
      return contentHeight;
    }
    if (editorConfig !== null && editorConfig !== void 0 && editorConfig.height) {
      return editorConfig.height;
    }
    if (popupProps.height) {
      return typeof popupProps.height === 'number' ? popupProps.height : parseInt(popupProps.height);
    }
    if (popupProps.maxHeight) {
      return typeof popupProps.maxHeight === 'number' ? popupProps.maxHeight : parseInt(popupProps.maxHeight);
    }

    //console.log('popupProps.height',300);
    return 300;
  };

  // 计算是否需要向上展示
  var shouldShowAbove = function shouldShowAbove() {
    if (placement === 'overlay') return false;
    var tableBody = cellTarget.closest(".".concat(prefixCls, "-body"));
    if (!tableBody) return false;
    var cellRect = cellTarget.getBoundingClientRect();
    var tableRect = tableBody.getBoundingClientRect();
    var editorHeight = getEditorHeight();

    // 检查底部空间是否足够
    var spaceBelow = tableRect.bottom - cellRect.bottom;
    return spaceBelow < editorHeight;
  };

  // 获取编辑器宽度
  var getEditorWidth = function getEditorWidth() {
    // 1. 优先使用配置的宽度
    var configKey = type.toUpperCase().replace('-', '_');
    var editorConfig = EDITOR_CONFIGS[configKey];

    //console.log(editorConfig,configKey);

    if (editorConfig !== null && editorConfig !== void 0 && editorConfig.width) {
      return editorConfig.width;
    }

    // 2. 其次使用外部传入的 width
    if (popupProps.width) {
      if (popupProps.width === 'cell') {
        return (cellTarget === null || cellTarget === void 0 ? void 0 : cellTarget.getBoundingClientRect().width) || 'auto';
      }
      return typeof popupProps.width === 'number' ? popupProps.width : parseInt(popupProps.width);
    }

    // 3. 最后使用单元格宽度作为默认值
    return (cellTarget === null || cellTarget === void 0 ? void 0 : cellTarget.getBoundingClientRect().width) || 'auto';
  };

  // 修改 MeasureComponent 组件
  var MeasureComponent = function MeasureComponent() {
    useEffect(function () {
      try {
        if (measureRef.current) {
          var height = measureRef.current.getBoundingClientRect().height;
          setActualHeight(height);
        }
      } catch (error) {
        console.error('Failed to measure editor height:', error);
      }
    }, []);

    // useEffect(() => {
    //   return () => {
    //     setActualHeight(null); // 组件卸载时重置高度
    //   };
    // }, []);

    return /*#__PURE__*/_jsx("div", {
      ref: measureRef,
      style: {
        position: 'absolute',
        visibility: 'hidden',
        pointerEvents: 'none',
        width: getEditorWidth(),
        left: '-9999px',
        top: '-9999px'
      },
      children: /*#__PURE__*/React.cloneElement(children, {
        key: 'measure'
      })
    });
  };

  // 只有当完全准备好时才渲染
  if (!isReady || !currentTarget) {
    // 即使未准备好显示，也要渲染测量组件
    return /*#__PURE__*/ReactDOM.createPortal( /*#__PURE__*/_jsx(MeasureComponent, {}), document.body);
  }

  // 创建编辑器内容
  var editorContent = /*#__PURE__*/_jsx("div", {
    ref: popupRef,
    className: "infinity-table-editor-popup ".concat(placement, " ").concat(popupProps.className || ''),
    style: _objectSpread({
      position: 'absolute',
      top: placement === 'overlay' ? 0 : shouldShowAbove() ? "-".concat(actualHeight || getEditorHeight(), "px") : '72px',
      left: 0,
      width: getEditorWidth(),
      background: '#fff',
      boxShadow: placement === 'overlay' ? 'none' : '0 3px 6px -4px rgba(0,0,0,.12), 0 6px 16px 0 rgba(0,0,0,.08)',
      borderRadius: placement === 'overlay' ? '0' : '2px',
      zIndex: (editorConfig === null || editorConfig === void 0 ? void 0 : editorConfig.zIndex) || 100
    }, popupProps.style),
    onClick: function onClick(e) {
      return e.stopPropagation();
    },
    children: /*#__PURE__*/React.cloneElement(children, {
      key: editorKey,
      onClose: onClose
    })
  });

  // 将辑器内容渲染到单元格内部
  return /*#__PURE__*/ReactDOM.createPortal(editorContent, currentTarget);
};