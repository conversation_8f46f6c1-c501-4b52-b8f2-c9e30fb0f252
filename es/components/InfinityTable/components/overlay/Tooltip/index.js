function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
import React from 'react';
import { jsx as _jsx } from "react/jsx-runtime";
export var Tooltip = function Tooltip(_ref) {
  var _disableControl$toolt2, _editingLock$tooltip2;
  var tooltipState = _ref.tooltipState,
    disableControl = _ref.disableControl,
    editingLock = _ref.editingLock,
    tooltipRef = _ref.tooltipRef;
  if (!tooltipState.visible) return null;
  var getMessage = function getMessage() {
    var _editingLock$tooltip;
    // 如果有自定义消息，优先使用自定义消息
    if (tooltipState.message) {
      return tooltipState.message;
    }

    // 否则使用默认消息
    if (tooltipState.isPermissionTip) {
      var _disableControl$toolt;
      return (disableControl === null || disableControl === void 0 || (_disableControl$toolt = disableControl.tooltip) === null || _disableControl$toolt === void 0 ? void 0 : _disableControl$toolt.message) || '暂无编辑权限';
    }
    return (editingLock === null || editingLock === void 0 || (_editingLock$tooltip = editingLock.tooltip) === null || _editingLock$tooltip === void 0 ? void 0 : _editingLock$tooltip.message) || '他人正在编辑';
  };
  return /*#__PURE__*/_jsx("div", {
    ref: tooltipRef,
    className: "infinity-table-tooltip",
    style: _objectSpread({
      position: 'fixed',
      left: tooltipState.position.x,
      top: tooltipState.position.y,
      transform: 'translate(-50%, -100%)',
      backgroundColor: 'rgba(0, 0, 0, 0.75)',
      color: '#fff',
      padding: '6px 8px',
      borderRadius: '4px',
      fontSize: '12px',
      zIndex: 1000,
      pointerEvents: 'none',
      whiteSpace: 'nowrap'
    }, tooltipState.isPermissionTip ? disableControl === null || disableControl === void 0 || (_disableControl$toolt2 = disableControl.tooltip) === null || _disableControl$toolt2 === void 0 ? void 0 : _disableControl$toolt2.style : editingLock === null || editingLock === void 0 || (_editingLock$tooltip2 = editingLock.tooltip) === null || _editingLock$tooltip2 === void 0 ? void 0 : _editingLock$tooltip2.style),
    children: getMessage()
  });
};