import "antd/es/space/style";
import _Space from "antd/es/space";
import "antd/es/image/style";
import _Image from "antd/es/image";
import React from 'react';
import { ClearButton } from "../../common/ClearButton";
import { isSupportWebp, formatWH } from "./utils";
import "./index.less";

// 添加工具函数
import { jsx as _jsx } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
var formatImageUrl = function formatImageUrl(url, width, height) {
  if (!url || typeof url !== 'string' || !url.startsWith('http') || url.includes('?')) {
    return url;
  }
  var size = formatWH(width, height);
  var process = ['?x-oss-process=image', 'auto-orient,1', 'quality,Q_80', 'bright,-1', "resize,s_".concat(size) // 使用短边自适应缩放
  ];
  if (isSupportWebp) {
    process.push('format,webp');
  }
  return "".concat(url).concat(process.join('/'));
};
export var ImagesDisplay = function ImagesDisplay(_ref) {
  var _ref$value = _ref.value,
    value = _ref$value === void 0 ? [] : _ref$value,
    style = _ref.style,
    _ref$width = _ref.width,
    width = _ref$width === void 0 ? 48 : _ref$width,
    _ref$height = _ref.height,
    height = _ref$height === void 0 ? 48 : _ref$height,
    onClear = _ref.onClear,
    overlayProps = _ref.overlayProps;
  var DISPLAY_COUNT = 2;
  var images = Array.isArray(value) ? value : [];
  var displayImages = images.slice(0, DISPLAY_COUNT);
  var totalCount = images.length;
  return /*#__PURE__*/_jsxs("div", {
    className: "infinity-table-images-display-wrapper",
    children: [(overlayProps === null || overlayProps === void 0 ? void 0 : overlayProps.showClear) && onClear && images.length > 0 && /*#__PURE__*/_jsx(ClearButton, {
      onClear: onClear
    }), /*#__PURE__*/_jsx(_Space, {
      size: 4,
      className: "infinity-table-images-display",
      style: style,
      children: displayImages.map(function (url, index) {
        return /*#__PURE__*/_jsxs("div", {
          className: "image-wrapper",
          style: {
            width: width,
            height: height,
            borderRadius: 2
          },
          children: [/*#__PURE__*/_jsx(_Image, {
            src: formatImageUrl(url, width, height),
            width: width,
            height: height,
            style: {
              objectFit: 'cover',
              borderRadius: 2
            }
          }), index === DISPLAY_COUNT - 1 && totalCount > DISPLAY_COUNT && /*#__PURE__*/_jsx("div", {
            className: "image-overlay",
            children: /*#__PURE__*/_jsxs("span", {
              children: [totalCount, "\u5F20"]
            })
          })]
        }, url);
      })
    })]
  });
};
ImagesDisplay.displayName = 'ImagesDisplay';