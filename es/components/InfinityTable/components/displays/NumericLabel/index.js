import React from 'react';
import "./index.less";
import { jsx as _jsx } from "react/jsx-runtime";
export var NumberDisplay = function NumberDisplay(_ref) {
  var value = _ref.value,
    style = _ref.style,
    prefix = _ref.prefix,
    suffix = _ref.suffix;
  var formattedValue = React.useMemo(function () {
    if (value == null) return '';

    //暂时不考虑精度参数
    var displayValue = "".concat(value);
    if (prefix) displayValue = prefix + displayValue;
    if (suffix) displayValue = displayValue + ' ' + suffix;
    return displayValue;
  }, [value, prefix, suffix]);
  return /*#__PURE__*/_jsx("div", {
    className: "infinity-table-number-display ".concat(suffix ? 'with-suffix' : ''),
    style: style,
    children: formattedValue
  });
};
NumberDisplay.displayName = 'NumberDisplay';