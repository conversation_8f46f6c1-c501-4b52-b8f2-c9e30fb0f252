import React from 'react';
import dayjs from 'dayjs';
import "./index.less";
import { jsx as _jsx } from "react/jsx-runtime";
export var DateDisplay = function DateDisplay(_ref) {
  var value = _ref.value,
    style = _ref.style,
    _ref$format = _ref.format,
    format = _ref$format === void 0 ? 'YYYY-MM-DD' : _ref$format;
  return /*#__PURE__*/_jsx("span", {
    className: "date-display",
    style: style,
    children: value ? dayjs(value).format(format) : '-'
  });
};
DateDisplay.displayName = 'DateDisplay';