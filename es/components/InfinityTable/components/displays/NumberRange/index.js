function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
import React from 'react';
import "./index.less";
import { jsx as _jsx } from "react/jsx-runtime";
import { Fragment as _Fragment } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
export var NumberRangeDisplay = function NumberRangeDisplay(_ref) {
  var value = _ref.value,
    style = _ref.style,
    _ref$unit = _ref.unit,
    unit = _ref$unit === void 0 ? '' : _ref$unit,
    _ref$precision = _ref.precision,
    precision = _ref$precision === void 0 ? 0 : _ref$precision,
    formatter = _ref.formatter;
  var formatNumber = React.useCallback(function (num) {
    if (num == null) return '-';
    return formatter ? formatter(num) : num.toFixed(precision);
  }, [formatter, precision]);
  var renderUnit = React.useCallback(function (unitText) {
    return unitText ? /*#__PURE__*/_jsx("span", {
      className: "unit",
      children: " ".concat(unitText)
    }) : '';
  }, []);
  var formattedValue = React.useMemo(function () {
    if (!value || _typeof(value) !== 'object') return '-';
    var _ref2 = value,
      min = _ref2.min,
      max = _ref2.max;
    if (min == null && max == null) {
      return '';
    }
    if (min != null && max == null) {
      return /*#__PURE__*/_jsxs(_Fragment, {
        children: [formatNumber(min), renderUnit(unit)]
      });
    }
    if (min == null && max != null) {
      return /*#__PURE__*/_jsxs(_Fragment, {
        children: [formatNumber(max), renderUnit(unit)]
      });
    }
    if (min === max) {
      return /*#__PURE__*/_jsxs(_Fragment, {
        children: [formatNumber(min), renderUnit(unit)]
      });
    }
    return /*#__PURE__*/_jsxs(_Fragment, {
      children: [formatNumber(min), " ~ ", formatNumber(max), renderUnit(unit)]
    });
  }, [value, formatNumber, unit, renderUnit]);
  return /*#__PURE__*/_jsx("span", {
    className: "infinity-table-number-range-display",
    style: style,
    children: formattedValue
  });
};
NumberRangeDisplay.displayName = 'NumberRangeDisplay';