function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * @file ClearButton Component
 * @description 表格通用清除按钮组件
 * @review 2024/12/19
 */

import React, { memo, useCallback } from 'react';
import classNames from 'classnames';
import "./index.less";
import { jsx as _jsx } from "react/jsx-runtime";
// 常量配置区
var CLEAR_BUTTON_CONFIG = {
  /** 清除图标URL地址 - 建议后续迁移到统一的资源配置文件中 */
  ICON_URL: 'https://s.xinc818.com/files/webcim4tarhdvztp4m3/remove_icon.png',
  /** 按钮容器默认样式 */
  CONTAINER_STYLE: {
    position: 'absolute',
    right: 0,
    top: 0,
    width: '40px',
    height: '70px',
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    zIndex: 1,
    backgroundImage: 'linear-gradient(-89deg, #FFFFFF 50%, #ffffff00 100%)'
  },
  /** 图标默认样式 */
  ICON_STYLE: {
    position: 'absolute',
    right: '6px',
    top: '6px',
    cursor: 'pointer'
  }
};

/**
 * ClearButton 组件 - 用于显示一个带渐变背景的清除按钮
 * 
 * @component
 * @example
 * ```tsx
 * <ClearButton 
 *   onClear={() => handleClear()} 
 *   className="custom-clear"
 *   style={{ width: '50px' }}
 * />
 * ```
 */
export var ClearButton = /*#__PURE__*/memo(function (_ref) {
  var onClear = _ref.onClear,
    style = _ref.style,
    className = _ref.className;
  // 处理清除按钮点击事件，阻止事件冒泡
  var handleClear = useCallback(function (e) {
    e.stopPropagation();
    onClear(e);
  }, [onClear]);
  return /*#__PURE__*/_jsx("div", {
    className: classNames('infinity-table-clear-button', className),
    style: _objectSpread(_objectSpread({}, CLEAR_BUTTON_CONFIG.CONTAINER_STYLE), style),
    role: "button",
    "aria-label": "\u6E05\u9664",
    children: /*#__PURE__*/_jsx("img", {
      src: CLEAR_BUTTON_CONFIG.ICON_URL,
      width: 16,
      height: 16,
      onClick: handleClear,
      className: "infinity-table-clear-icon",
      alt: "\u6E05\u9664",
      style: CLEAR_BUTTON_CONFIG.ICON_STYLE
    })
  });
});

// 设置组件显示名称，用于 React DevTools
ClearButton.displayName = 'ClearButton';
export default ClearButton;