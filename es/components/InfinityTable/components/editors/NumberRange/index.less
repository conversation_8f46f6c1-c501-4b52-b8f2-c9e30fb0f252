.infinity-table-number-range-editor {
  width: 232px;
  height: 89px;
  background: #FFFFFF;
  // border: 1px solid #E5E6E8;
  border-radius: 2px;
  font-size: 12px !important;
  color: #1D2129 !important;
  display: flex;
  flex-direction: column;

  .number-range-content {
    padding: 12px 16px 12px 16px;
    flex: 1;
    &.has-value {
      [class$="-space-compact"] {
        background: #FFFFFF !important;
        border: 1px solid #C9CDD4 !important;
      }
    }

    [class$="-space-compact"] {
      display: flex;
      width: 200px;
      height: 24px;
      background: #F7F8FA;
      border-radius: 2px;
      align-items: center;

      [class$="-input-number-focused"] {
        background-color: transparent !important;
      }


      .infinity-table-number-range-input {
        flex: 1;
        min-width: 0;
        font-size: 12px !important;
        color: #1D2129 !important;


      
        
        [class$="-input-number"] {
          width: 100%;
          height: 24px;
          background-color: #F7F8FA !important;
          border: none !important;
          box-shadow: none !important;

          &:focus {
            border: none !important;
            box-shadow: none !important;
            background-color: transparent !important
          }

        

          [class$="-input-number-input"] {
            height: 24px;
            line-height: 24px;
            padding: 0 8px;
            background-color: #F7F8FA !important;
            text-align: center;
          }

          &-handler-wrap {
            display: none;
          }
        }
      }

      .infinity-table-number-range-separator {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0 4px;
        color: #999;
        height: 24px;
        line-height: 24px;
      }

      .infinity-table-number-range-unit {
        padding: 0 8px;
        color: #999;
        height: 24px;
        line-height: 24px;
      }
    }
  }

  .number-range-footer {
    display: flex;
    padding: 8px 12px;
    border-top: 1px solid #E5E6E8;
    gap: 8px;
    justify-content: flex-end;

    button {
      width: 48px;
      height: 24px;
      overflow: hidden;
      padding: 0;
      border-radius: 2px;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      &[class*="-primary"] {
        background: #FF3029;
        border-color: #FF3029;

        &:hover, &:focus {
          background: #ff4d47;
          border-color: #ff4d47;
        }
      }

      &:not([class*="-primary"]) {
        border-color: #E5E6E8;
        color: #666;

        &:hover, &:focus {
          border-color: #d9d9d9;
          color: #333;
        }
      }
    }
  }
}

.infinity-table-cell-editing {
  .infinity-table-number-range-editor {
    .infinity-table-number-range-input {
      &:hover {
        background-color: #f5f5f5;
      }

      &:focus-within {
        background-color: #fff;
      }
    }
  }
}