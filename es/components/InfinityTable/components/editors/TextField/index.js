function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
import "antd/es/input/style";
import _Input from "antd/es/input";
var _excluded = ["value", "onCellChange", "style", "maxLength", "allowClear", "placeholder"];
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }
function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }
/**
 * @file TextField Editor Component
 * @description 表格单元格文本编辑器组件
 * @review 2024/12/19
 */

import React, { useState, useEffect } from 'react';
import { useTableDebug } from "../../../utils/debug";
import "./index.less";
import { jsx as _jsx } from "react/jsx-runtime";
var DEFAULT_MIN_HEIGHT = 72;
export var TextEditor = function TextEditor(_ref) {
  var value = _ref.value,
    onCellChange = _ref.onCellChange,
    style = _ref.style,
    maxLength = _ref.maxLength,
    _ref$allowClear = _ref.allowClear,
    allowClear = _ref$allowClear === void 0 ? false : _ref$allowClear,
    _ref$placeholder = _ref.placeholder,
    placeholder = _ref$placeholder === void 0 ? '请输入' : _ref$placeholder,
    props = _objectWithoutProperties(_ref, _excluded);
  var _useState = useState(''),
    _useState2 = _slicedToArray(_useState, 2),
    innerValue = _useState2[0],
    setInnerValue = _useState2[1];
  var debug = useTableDebug();

  // 同步外部传入的值
  useEffect(function () {
    setInnerValue(String(value !== null && value !== void 0 ? value : ''));
  }, [value]);

  /**
   * 处理输入值变化
   * @param e 输入事件对象
   */
  var handleChange = function handleChange(e) {
    var newValue = e.target.value;
    setInnerValue(newValue);
  };

  /**
   * 处理失焦事件，触发值更新
   */
  var handleBlur = function handleBlur() {
    debug.info('文本编辑器: 最终修改值', innerValue);
    onCellChange === null || onCellChange === void 0 || onCellChange(innerValue);
  };
  return /*#__PURE__*/_jsx("div", {
    className: "infinity-table-text-editor-wrapper",
    children: /*#__PURE__*/_jsx(_Input.TextArea, _objectSpread(_objectSpread({
      className: "infinity-table-text-editor",
      value: innerValue,
      style: _objectSpread(_objectSpread({}, style), {}, {
        minHeight: "".concat(DEFAULT_MIN_HEIGHT, "px"),
        border: 'none',
        fontSize: '12px',
        resize: 'none',
        lineHeight: '20px'
      }),
      maxLength: maxLength,
      placeholder: placeholder,
      allowClear: allowClear,
      autoFocus: true
    }, props), {}, {
      onChange: handleChange // 放在props后面以确保使用我们自己的onChange
      ,
      onBlur: handleBlur
    }))
  });
};
TextEditor.displayName = 'TextEditor';
export default TextEditor;