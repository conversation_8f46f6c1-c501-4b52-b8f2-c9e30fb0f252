@import '../../../styles/variables.less';

.infinity-table-text-editor-wrapper {
  width: 100%;
  position: relative;
  min-height: 72px;
  font-size: 12px;
  box-shadow: 0 2px 12px 0 #00000033;
  // padding: 12px 12px 0 12px !important;
  resize: none;
  // border: 1px solid rgb(78, 89, 105);
}

.infinity-table-text-editor {
  width: 100%;
  border: none;
  outline: none;
  padding-top: 12px !important;
  padding-left: 12px !important;
  background: #fff !important;
  resize: none !important;

  &:focus {
    box-shadow: none !important;
    border-color: #4E5969 !important;
  }
  
  // &:hover {
  //   background-color: #fafafa;
  // }
  
  &[class$="-input-affix-wrapper"] {
    padding: 0;
    
    [class$="-input"] {
      padding: 8px 8px;
    }
    
    [class$="-input-suffix"] {
      margin-right: 4px;
    }
  }
  
  &[class$="-input-textarea"] {
    width: 100%;
    
    textarea {
      resize: none;
      background: #fff !important;
      min-height: 72px !important;
      border: 0 none !important;
      font-size: 12px !important;

      &:focus {
        outline: none !important;
        box-shadow: none !important;
        border-color: transparent !important;
      }
    }
  }

  &[class$="-input"] {
    resize: none !important;
  }
}
