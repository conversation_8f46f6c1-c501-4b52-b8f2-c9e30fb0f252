.infinity-table-search-select-editor {
  width: 100%;
  padding: 8px 0px;
  width: 240px;
  height: 228px;
  background: #FFFFFF;
  border: 1px solid #E5E6E8;
  font-size: 12px;
  color: #1D2129;
  border-radius: 2px;
  .search-container {
    background: #fff;
    border-radius: 2px;
    
    [class*="-input-affix-wrapper"] {
      background-color: #F7F8FA;
      border: 0 none !important;
      width: 216px;
      margin-left: 12px;
      margin-bottom: 12px;
      
      &:hover, &:focus {
        border-color: #d9d9d9;
      }
      
      input {
        background-color: #F7F8FA;
        width: 176px !important;
        &:focus {
          box-shadow: none;
        }
      }
    }
  }

  .search-dropdown-content {
    background: #fff;
    border-radius: 0 0 2px 2px;
    
    .loading-container {
      padding: 12px 0;
      text-align: center;
    }
    
    .options-container {
      max-height: 168px;
      overflow-y: auto;
      
      .option-item {
        padding: 5px 12px;
        min-height: 32px;
        line-height: 22px;
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        
        &:hover, &.active, &.selected {
          background-color: #E8F3FF;
        }

        &.selected {
          font-weight: 600;
        }

        span {
          display: inline;
        }
      }
      
      .empty-text {
        padding: 12px;
        text-align: center;
        color: #999;
      }
    }
  }
} 