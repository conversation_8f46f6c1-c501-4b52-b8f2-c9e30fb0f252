function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }
function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
/**
 * @file MultiSelect Editor Component
 * @description 表格单元格多选编辑器组件
 * @review 2024/12/19
 */

import React, { useCallback, useMemo } from 'react';
import "./index.less";

/** 选项高度配置（包含padding） */
import { jsx as _jsx } from "react/jsx-runtime";
var OPTION_HEIGHT = 32;

/** 选项数据结构定义 */

/** 组件属性定义 */

/**
 * 多选编辑器组件
 * @param props - 组件属性
 * @returns React组件
 */
export var MultiSelectEditor = function MultiSelectEditor(_ref) {
  var _ref$value = _ref.value,
    value = _ref$value === void 0 ? [] : _ref$value,
    onCellChange = _ref.onCellChange,
    _ref$options = _ref.options,
    options = _ref$options === void 0 ? [] : _ref$options,
    _ref$placeholder = _ref.placeholder,
    placeholder = _ref$placeholder === void 0 ? '请选择' : _ref$placeholder;
  // 将输入值统一转换为字符串数组
  var selectedValues = useMemo(function () {
    if (!Array.isArray(value)) return [];
    return value.map(function (v) {
      return String(v);
    }).filter(Boolean);
  }, [value]);

  // 计算列表容器高度
  var listHeight = useMemo(function () {
    return options.length * OPTION_HEIGHT + 2;
  }, [options.length]);

  // 处理选项点击事件
  var handleOptionClick = useCallback(function (optionValue) {
    var newValues = selectedValues.includes(optionValue) ? selectedValues.filter(function (v) {
      return v !== optionValue;
    }) : [].concat(_toConsumableArray(selectedValues), [optionValue]);
    onCellChange === null || onCellChange === void 0 || onCellChange(newValues);
  }, [selectedValues, onCellChange]);

  // 渲染选项列表
  var renderOptions = useCallback(function () {
    return options.map(function (option) {
      return /*#__PURE__*/_jsx("div", {
        className: "option-item ".concat(selectedValues.includes(option.value) ? 'selected' : ''),
        onClick: function onClick() {
          return handleOptionClick(option.value);
        },
        style: option.color ? {
          color: option.color
        } : undefined,
        children: option.label
      }, option.value);
    });
  }, [options, selectedValues, handleOptionClick]);
  return /*#__PURE__*/_jsx("div", {
    className: "infinity-table-select-editor",
    style: {
      height: "".concat(listHeight, "px")
    },
    children: options.length > 0 ? /*#__PURE__*/_jsx("div", {
      className: "options-list",
      children: renderOptions()
    }) : /*#__PURE__*/_jsx("div", {
      className: "placeholder",
      children: placeholder
    })
  });
};

// 设置组件显示名称，用于调试
MultiSelectEditor.displayName = 'MultiSelectEditor';