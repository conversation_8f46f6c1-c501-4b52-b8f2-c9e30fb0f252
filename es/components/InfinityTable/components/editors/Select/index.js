/**
 * @file Select Editor Component
 * @description 表格单元格下拉选择器编辑器组件
 * @review 2024/12/19
 */

import React, { useCallback, useMemo } from 'react';
import "./index.less";

/**
 * 下拉选项接口定义
 */

/**
 * Select编辑器属性接口
 */
import { jsx as _jsx } from "react/jsx-runtime";
/** 每个选项的固定高度（包含内边距），单位：像素 */
var OPTION_HEIGHT = 32;

/** 组件类名前缀 */
var PREFIX_CLS = 'infinity-table-select-editor';

/**
 * 下拉选择器编辑器组件
 */
export var SelectEditor = function SelectEditor(_ref) {
  var value = _ref.value,
    onCellChange = _ref.onCellChange,
    _ref$options = _ref.options,
    options = _ref$options === void 0 ? [] : _ref$options,
    _ref$placeholder = _ref.placeholder,
    placeholder = _ref$placeholder === void 0 ? '请选择' : _ref$placeholder;
  // 计算下拉列表容器高度
  var listHeight = useMemo(function () {
    return options.length * OPTION_HEIGHT + 2;
  }, [options.length]);

  // 处理选项点击事件
  var handleOptionClick = useCallback(function (optionValue) {
    onCellChange === null || onCellChange === void 0 || onCellChange(optionValue);
  }, [onCellChange]);

  // 渲染选项列表
  var renderOptions = useCallback(function () {
    return /*#__PURE__*/_jsx("div", {
      className: "options-list",
      children: options.map(function (option) {
        return /*#__PURE__*/_jsx("div", {
          className: "option-item ".concat(value === option.value ? 'selected' : ''),
          onClick: function onClick() {
            return handleOptionClick(option.value);
          },
          style: option.color ? {
            color: option.color
          } : undefined,
          title: option.label // 添加title属性，方便文本过长时查看完整内容
          ,
          children: option.label
        }, option.value);
      })
    });
  }, [options, value, handleOptionClick]);
  return /*#__PURE__*/_jsx("div", {
    className: PREFIX_CLS,
    style: {
      height: "".concat(listHeight, "px")
    },
    children: options.length > 0 ? renderOptions() : /*#__PURE__*/_jsx("div", {
      className: "placeholder",
      title: placeholder,
      children: placeholder
    })
  });
};

// 设置组件显示名称，方便调试
SelectEditor.displayName = 'SelectEditor';