function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
import { EDITOR_TYPES } from "../../constants";

/**
 * 单元格显示组件导入
 * 按照字母顺序排列，便于维护和查找
 */
import { DateDisplay } from "../displays/DatePicker";
import { ImagesDisplay } from "../displays/ImageGallery";
import { MultiSelectDisplay } from "../displays/MultiSelect";
import { NumberDisplay } from "../displays/NumericLabel";
import { NumberRangeDisplay } from "../displays/NumberRange";
import { SearchSelectDisplay } from "../displays/SearchSelect";
import { SelectDisplay } from "../displays/Select";
import { TextDisplay } from "../displays/TextLabel";

/**
 * 单元格编辑器组件导入
 * 按照字母顺序排列，与显示组件保持对应关系
 */
import { DateEditor } from "../editors/DatePicker";
import { ImagesEditor } from "../editors/ImageUploader";
import { MultiSelectEditor } from "../editors/MultiSelect";
import { NumberEditor } from "../editors/NumericField";
import { NumberRangeEditor } from "../editors/NumberRange";
import { SearchSelectEditor } from "../editors/SearchSelect";
import { SelectEditor } from "../editors/Select";
import { TextEditor } from "../editors/TextField";

/**
 * 组件注册表类
 * 管理所有单元格的显示和编辑组件
 */
var ComponentRegistry = /*#__PURE__*/function () {
  function ComponentRegistry() {
    _classCallCheck(this, ComponentRegistry);
    _defineProperty(this, "components", new Map());
  }
  _createClass(ComponentRegistry, [{
    key: "register",
    value:
    /**
     * 注册一个新的组件类型
     * @param type - 组件类型标识符
     * @param component - 包含Display和Editor的组件对象
     */
    function register(type, component) {
      if (!type || !(component !== null && component !== void 0 && component.Display) || !(component !== null && component !== void 0 && component.Editor)) {
        throw new Error('Invalid component registration parameters');
      }
      this.components.set(type, component);
    }

    /**
     * 获取指定类型的显示组件
     * @param type - 组件类型标识符
     * @returns 对应的显示组件，如果未找到则返回undefined
     */
  }, {
    key: "getDisplayComponent",
    value: function getDisplayComponent(type) {
      var _this$components$get;
      return (_this$components$get = this.components.get(type)) === null || _this$components$get === void 0 ? void 0 : _this$components$get.Display;
    }

    /**
     * 获取指定类型的编辑器组件
     * @param type - 组件类型标识符
     * @returns 对应的编辑器组件，如果未找到则返回undefined
     */
  }, {
    key: "getEditorComponent",
    value: function getEditorComponent(type) {
      var _this$components$get2;
      return (_this$components$get2 = this.components.get(type)) === null || _this$components$get2 === void 0 ? void 0 : _this$components$get2.Editor;
    }
  }]);
  return ComponentRegistry;
}(); // 创建全局单例注册表实例
export var registry = new ComponentRegistry();

/**
 * 注册所有内置组件
 * 使用对象映射方式，便于维护和扩展
 */
var registerBuiltInComponents = function registerBuiltInComponents() {
  var builtInComponents = _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, EDITOR_TYPES.TEXT, {
    Display: TextDisplay,
    Editor: TextEditor
  }), EDITOR_TYPES.NUMBER, {
    Display: NumberDisplay,
    Editor: NumberEditor
  }), EDITOR_TYPES.DATE, {
    Display: DateDisplay,
    Editor: DateEditor
  }), EDITOR_TYPES.SELECT, {
    Display: SelectDisplay,
    Editor: SelectEditor
  }), EDITOR_TYPES.SEARCH_SELECT, {
    Display: SearchSelectDisplay,
    Editor: SearchSelectEditor
  }), EDITOR_TYPES.MULTI_SELECT, {
    Display: MultiSelectDisplay,
    Editor: MultiSelectEditor
  }), EDITOR_TYPES.NUMBER_RANGE, {
    Display: NumberRangeDisplay,
    Editor: NumberRangeEditor
  }), EDITOR_TYPES.IMAGES, {
    Display: ImagesDisplay,
    Editor: ImagesEditor
  });
  Object.entries(builtInComponents).forEach(function (_ref) {
    var _ref2 = _slicedToArray(_ref, 2),
      type = _ref2[0],
      component = _ref2[1];
    registry.register(type, component);
  });
};

// 初始化注册内置组件
registerBuiltInComponents();

/**
 * 便捷方法：获取编辑器组件
 * @param type - 组件类型标识符
 */
export var getEditor = function getEditor(type) {
  return registry.getEditorComponent(type);
};

/**
 * 便捷方法：获取显示组件
 * @param type - 组件类型标识符
 */
export var getDisplay = function getDisplay(type) {
  return registry.getDisplayComponent(type);
};

/**
 * 注册自定义组件的方法
 * @param type - 自定义组件类型标识符
 * @param displayComponent - 自定义显示组件
 * @param editorComponent - 自定义编辑器组件
 * @throws 当参数无效时抛出错误
 */
export var registerComponent = function registerComponent(type, displayComponent, editorComponent) {
  if (!type || !displayComponent || !editorComponent) {
    throw new Error('Invalid custom component registration parameters');
  }
  registry.register(type, {
    Display: displayComponent,
    Editor: editorComponent
  });
};