function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
var _excluded = ["type", "value", "onCellChange", "editorProps"];
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }
function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }
import React, { memo, useMemo } from 'react';
import { getEditor } from "../componentRegistry";

/**
 * 可编辑单元格的属性接口定义
 * @interface EditorCellProps
 * @template T - 单元格值类型，默认为 CellValue
 */
import { jsx as _jsx } from "react/jsx-runtime";
/**
 * 可编辑单元格组件
 * 
 * @component
 * @description 根据传入的 type 动态渲染对应的编辑器组件，支持自定义编辑器注册
 * 
 * @example
 * ```tsx
 * <EditorCell
 *   type="text"
 *   value="示例文本"
 *   onCellChange={(value) => console.log('值已更新:', value)}
 *   editorProps={{ placeholder: '请输入' }}
 * />
 * ```
 */
export var EditorCell = /*#__PURE__*/memo(function (_ref) {
  var type = _ref.type,
    value = _ref.value,
    onCellChange = _ref.onCellChange,
    _ref$editorProps = _ref.editorProps,
    editorProps = _ref$editorProps === void 0 ? {} : _ref$editorProps,
    props = _objectWithoutProperties(_ref, _excluded);
  // 获取对应类型的编辑器组件
  var EditorComponent = useMemo(function () {
    return getEditor(type);
  }, [type]);

  // 编辑器组件不存在时的错误处理
  if (!EditorComponent) {
    if (process.env.NODE_ENV !== 'production') {
      console.warn("[EditorCell] \u672A\u627E\u5230\u7C7B\u578B\u4E3A \"".concat(type, "\" \u7684\u7F16\u8F91\u5668\u7EC4\u4EF6\uFF0C\u8BF7\u68C0\u67E5\u662F\u5426\u5DF2\u6B63\u786E\u6CE8\u518C"));
    }
    return null;
  }
  return /*#__PURE__*/_jsx(EditorComponent, _objectSpread(_objectSpread({
    value: value,
    onCellChange: onCellChange
  }, editorProps), props));
});

// 导出类型定义，方便其他组件使用