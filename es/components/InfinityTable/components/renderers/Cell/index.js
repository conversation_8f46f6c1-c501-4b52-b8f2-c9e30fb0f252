var _excluded = ["value", "record", "index", "column", "type", "editor", "displayRender", "onClear"];
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }
function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
import React, { memo } from "react";
import { getDisplay } from "../componentRegistry";
import { isSafari } from "../../../utils/browser";
import { TABLE_LAYOUT_CONFIG } from "../../../constants";
import "./index.less";

/**
 * 单元格弹出层属性接口
 * @interface OverlayProps
 * @property {React.CSSProperties} [style] - 自定义样式
 * @property {string} [className] - 自定义类名
 * @property {Function} [onClear] - 清除回调函数
 */

/**
 * 展示单元格属性接口
 * @interface DisplayCellProps
 * @extends {Record<string, unknown>}
 */
import { jsx as _jsx } from "react/jsx-runtime";
/**
 * 获取基础单元格样式
 * @param {React.CSSProperties} [overlayStyle] - 覆盖样式
 * @returns {React.CSSProperties} 合并后的样式对象
 */
var getBaseCellStyle = function getBaseCellStyle(overlayStyle) {
  var baseStyle = _objectSpread({
    height: "".concat(TABLE_LAYOUT_CONFIG.CELL_HEIGHT, "px"),
    overflow: "hidden",
    padding: "12px 12px",
    width: "100%",
    position: "relative"
  }, overlayStyle);
  if (isSafari()) {
    baseStyle.WebkitUserSelect = "none";
  }
  return baseStyle;
};

/**
 * 展示单元格组件
 * @component DisplayCell
 * @description 用于渲染表格单元格的内容，支持自定义渲染和默认组件渲染
 */
export var DisplayCell = /*#__PURE__*/memo(function (_ref) {
  var value = _ref.value,
    record = _ref.record,
    index = _ref.index,
    column = _ref.column,
    type = _ref.type,
    editor = _ref.editor,
    displayRender = _ref.displayRender,
    onClear = _ref.onClear,
    props = _objectWithoutProperties(_ref, _excluded);
  // 获取对应类型的显示组件
  var Component = getDisplay(type);
  var overlayProps = (editor === null || editor === void 0 ? void 0 : editor.overlayProps) || {};

  // 构建样式和类名
  var cellWrapperStyle = getBaseCellStyle(overlayProps.style);
  var cellClassName = ["infinity-table-display-cell", overlayProps.className].filter(Boolean).join(" ");

  // 使用自定义渲染函数
  if (displayRender) {
    return /*#__PURE__*/_jsx("div", {
      className: cellClassName,
      style: cellWrapperStyle,
      "data-cell-type": type,
      "data-type": "custom-display-render",
      children: displayRender({
        value: value,
        record: record,
        index: index,
        column: column
      })
    });
  }

  // 组件不存在时给出警告
  if (!Component) {
    console.warn("[InfinityTable] \u672A\u627E\u5230\u7C7B\u578B\u4E3A \"".concat(type, "\" \u7684\u663E\u793A\u7EC4\u4EF6\uFF0C\u8BF7\u68C0\u67E5\u7EC4\u4EF6\u6CE8\u518C\u8868"));
    return null;
  }
  return /*#__PURE__*/_jsx("div", {
    className: cellClassName,
    style: cellWrapperStyle,
    "data-cell-type": type,
    children: /*#__PURE__*/_jsx(Component, _objectSpread({
      value: value,
      onClear: onClear,
      overlayProps: overlayProps,
      record: record,
      column: column
    }, props))
  });
});

// 设置组件显示名称，方便调试
DisplayCell.displayName = "DisplayCell";