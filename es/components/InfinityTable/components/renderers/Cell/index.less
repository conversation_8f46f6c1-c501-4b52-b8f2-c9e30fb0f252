@import '../../../styles/variables.less';

.infinity-table-container {    
  .infinity-table-cell {
    position: relative;
    padding: 0 !important;
    font-family: 'PingFang SC';
    
    &:hover:not(.infinity-table-cell-selected):not([class*="-table-cell-fix-left"]):not([class*="-table-cell-fix-right"]) {
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: 1px solid #CACDD4;
        pointer-events: none;
        z-index: 1;
      }
    }
  }

  // 选中状态样式
  .infinity-table-cell-selected {
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 1px solid #4E5969;
      pointer-events: none;
      z-index: 99;
    }
  }
}

.infinity-table-cell.selected-cell {
  background-color: #e6f7ff; // 选中状态的背景色
  border-color: #4E5969; // 选中状态的边框色
}

.infinity-table-display-cell {
  .display-cell-actions {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 4px;
    align-items: center;
    
    .clear-icon {
      cursor: pointer;
      color: rgba(0, 0, 0, 0.45);
      font-size: 12px;
      padding: 4px;
      
      &:hover {
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
}