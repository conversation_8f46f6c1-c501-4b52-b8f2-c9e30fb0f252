@import "./variables.less";

.infinity-table-wrapper {
  position: relative;
  z-index: 111;

  .infinity-table-container {
    background: #fff;
    position: relative;
  }

  // 表头样式
  [class$="-table-thead"] {
    z-index: 100;
    > tr > th {
      padding: 8px 11px 8px 11px !important;
      color: #86909c !important;
      &::before {
        display: none;
        border: 0 none;
      }
    }
    [class$="-cell-scrollbar"] {
      z-index: 1;
    }
  }

  [class$="-table-body"] {
    [class*="-cell-fix-left-last"] {
      z-index: 101 !important; // 确保固定列在最上层
      border-bottom: 1px solid #f0f0f0 !important;
    }
  }

  // 设置边框
  [class$="-table"]:not([class$="-table-bordered"]) {
    .infinity-table-cell {
      border-right: 1px solid #f0f0f0 !important;
      border-bottom: 1px solid #f0f0f0 !important;
      &:last-child {
        border-right: none !important;
      }
      &[class*="-cell-fix-right-first"] {
        z-index: 101 !important; // 确保固定列在最上层
      }
    }
  }

  // 单元格
  .infinity-table-cell {
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
    padding: 0 !important;

    // [class*="-cell-fix-right-first"] {
    //   z-index: 100 !important; // 确保固定列在最上层
    // }
  }

  //禁用状态
  .disabled-cell-content,
  .locked-cell-content {
    position: relative;
    pointer-events: auto !important;
    cursor: not-allowed !important;

    > .infinity-table-display-cell {
      opacity: 0.7;
    }
  }

  //禁用提示, locked和disabled的tooltip考虑删除
  .infinity-table-tooltip,
  .locked-cell-tooltip,
  .disabled-cell-tooltip {
    position: fixed;
    z-index: 1100;
    max-width: 250px;
    padding: 6px 8px;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.75);
    border-radius: 4px;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12);
    pointer-events: none;

    &:after {
      content: "";
      position: absolute;
      top: 100%;
      left: 50%;
      margin-left: -5px;
      border-width: 5px;
      border-style: solid;
      border-color: rgba(0, 0, 0, 0.75) transparent transparent transparent;
    }
  }
}
