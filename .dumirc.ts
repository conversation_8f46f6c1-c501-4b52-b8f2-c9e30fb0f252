import { defineConfig } from 'dumi';
import { resolve } from 'path';
import MillionLint from "@million/lint";

export default defineConfig({
  outputPath: 'docs-dist',
  themeConfig: {
    name: 'infinityTable',
    nav: [
      { title: '配置文档', link: '/guide/getting-started' },
      { title: '博客', link: '/guide/blog' },
      { title: '完整示例', link: '/guide/demo' },
    ],
    contentWidth: 1600,
    demoInheritHeight: true,
    deviceWidth: '100%',
  },
  resolve: {
    entryFile: './src/index.ts',
    atomDirs: [
      { type: 'component', dir: 'src/components' },
    ],
  },
  styles: [
    // 'antd/dist/antd.css',
    `
    #root {
      overflow-x: hidden;
    }
    .dumi-default-header-left {
      width: auto !important;
    }
    .dumi-default-header-content {
      max-width: 100% !important;
      padding: 0 24px;
    }
    .dumi-default-doc-layout > main {
      max-width: 100% !important;
      padding: 0 24px;
    }
    .dumi-default-previewer {
      min-height: 500px;
    }
    .dumi-default-previewer-demo {
      min-height: 400px;
      padding: 24px;
    }
    @media screen and (max-width: 768px) {
      .dumi-default-header-content,
      .dumi-default-doc-layout > main {
        padding: 0 16px;
        width: calc(100% - 40px);
        overflow-x: hidden;
      }
      .dumi-default-previewer-demo {
        padding: 16px;
        width: 100%;
        overflow-x: hidden;
      }
    }
    /* 导航栏固定样式 */
    .dumi-default-header {
      position: fixed !important;
      top: 0;
      left: 0;
      right: 0;
      z-index: 100;
      background: #fff;
      backdrop-filter: blur(5px);
      border-bottom: 1px solid #eee;
    }

    /* logo 样式调整 */
    .dumi-default-header-left {
      width: 260px !important;  /* 与侧边栏宽度对齐 */
      padding-left: 24px !important;
      // border-right: 1px solid #eee;
    }

    /* 内容区域调整，为固定导航栏留出空间 */
    .dumi-default-doc-layout {
      padding-top: 64px !important;  /* 导航栏高度 */
    }
    .dumi-default-doc-layout {
      /* 侧边栏样式 */
      .dumi-default-sidebar {
        position: fixed !important;
        top: 77px !important;
        left: 0;
        bottom: 0;
        overflow-y: auto;
        max-height: calc(100vh - 77px) !important;
        width: 260px !important;
        padding: 24px 16px !important;
        border-right: 1px solid #eee;
        background: #fff;
        font-size: 15px;

        li {
          margin: 8px 0;
          line-height: 1.6;
        }

        a {
          padding: 8px 12px;
          border-radius: 4px;
          transition: all 0.3s;

          &:hover {
            background-color: rgba(0, 0, 0, 0.04);
          }
        }
      }

      > main {
        margin-left: 260px !important;
        padding: 24px 48px !important;
        font-size: 15px;
        // line-height: 1.8;
      }
    }



    /* 移动端适配 */
    @media screen and (max-width: 768px) {
      .dumi-default-header-left {
        width: auto !important;
        border-right: none;
      }
      
      .dumi-default-sidebar {
        position: static !important;
        width: 100%;
      }
      
      .dumi-default-doc-layout > main {
        margin-left: 0 !important;
        padding: 24px 16px !important;
      }
    }
    `
  ],
  extraBabelPlugins: [
    [
      'import',
      {
        libraryName: 'antd',
        libraryDirectory: 'es',
        style: true
      }
    ]
  ],
  lessLoader: {
    lessOptions: {
      modifyVars: {
        '@ant-prefix': 'micro',
      },
      javascriptEnabled: true,
    }
  },
  apiParser: {},
  favicons: ['/favicon.ico'],
  base: process.env.NODE_ENV === 'production' ? '/table/' : '/',
  publicPath: process.env.NODE_ENV === 'production' ? '/table/' : '/',
  // scripts: process.env.NODE_ENV === 'development' ? [
  //   'https://unpkg.com/react-scan/dist/auto.global.js'
  // ] : [],
  alias: {
    '@xc/infinity-table': resolve(__dirname, 'src/components/InfinityTable'),
    '@table': resolve(__dirname, 'src/components/InfinityTable'),
    '@components': resolve(__dirname, 'src/components/InfinityTable/components'),
    '@utils': resolve(__dirname, 'src/components/InfinityTable/utils'),
    '@types': resolve(__dirname, 'src/components/InfinityTable/types/index'),
    '@constants': resolve(__dirname, 'src/components/InfinityTable/constants'),
  },
  // chainWebpack: (config) => {
  //   config.plugin('million-lint').use(MillionLint.webpack());
  // },
}); 