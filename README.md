# Antd Excel-like Table Editor

一个基于 Antd Table 的类 Excel 编辑表格组件，支持自定义显示组件和编辑组件，提供灵活的单元格编辑能力。

## 架构设计

本项目采用模块化、可扩展的架构设计，分为组件部分和演示接入部分。以下是整体架构和代码设计的详细说明，旨在帮助开发者快速理解项目结构和功能实现。

### 组件结构

项目的代码结构采用树状图展示，清晰划分各个功能模块和组件，便于维护和扩展。

```
src/
├── components/
│   └── InfinityTable/
│       ├── components/
│       │   ├── ColumnManager/        # 列管理器组件
│       │   ├── displays/             # 显示组件集合
│       │   │   ├── DateDisplay/      # 日期显示组件
│       │   │   ├── ImagesDisplay/    # 图片显示组件
│       │   │   ├── MultiSelectDisplay/ # 多选显示组件
│       │   │   ├── NumberDisplay/    # 数字显示组件
│       │   │   ├── NumberRangeDisplay/ # 数字范围显示组件
│       │   │   ├── PriceDisplay/     # 价格显示组件
│       │   │   ├── SelectDisplay/    # 单选显示组件
│       │   │   └── TextDisplay/      # 文本显示组件
│       │   └── editors/              # 编辑器组件集合
│       │       ├── DateEditor/       # 日期编辑器
│       │       ├── ImagesEditor/     # 图片编辑器
│       │       ├── MultiSelectEditor/ # 多选编辑器
│       │       └── ...               # 其他编辑器
│       ├── types/                    # 类型定义
│       └── constants/                # 常量配置
└── pages/
    └── TableEditor/                   # 示例页面
```

#### 目录说明

- **components/InfinityTable/components/**: 主要包含表格编辑器的各种子组件，包括显示组件和编辑组件。
  - **ColumnManager/**: 负责表格列的管理，如显示/隐藏和顺序调整。
  - **displays/**: 包含不同类型数据的显示组件，每个组件负责特定类型数据的渲染。
  - **editors/**: 包含不同类型数据的编辑组件，每个组件负责特定类型数据的编辑逻辑。

- **types/**: 定义项目中使用的 TypeScript 类型，包括接口、类型别名等，确保代码的类型安全和可维护性。

- **constants/**: 存放项目中使用的常量配置，如默认配置、枚举值等。

- **pages/TableEditor/**: 提供一个示例页面，展示如何在实际项目中接入和使用 InfinityTable 组件。

### 核心功能模块

项目主要由以下核心功能模块组成，每个模块负责特定的功能，确保代码的高内聚低耦合。

1. **显示组件 (Displays)**
   - **职责**: 负责单元格数据的展示逻辑，依据数据类型渲染不同的 UI 组件。
   - **实现**: 每个显示组件都实现了 `DisplayProps` 接口，确保统一的属性传递和行为。
   - **包含的显示组件**:
     - 文本 (TextDisplay)：用于展示文本数据。
     - 日期 (DateDisplay)：用于展示日期数据，支持格式化显示。
     - 数字 (NumberDisplay)：用于展示数字数据，支持千分位格式等。
     - 图片 (ImagesDisplay)：用于展示图片列表。
     - 选择器 (SelectDisplay/MultiSelectDisplay)：用于展示单选或多选数据。
     - 价格 (PriceDisplay)：用于展示带货币符号的价格数据。
     - 数字范围 (NumberRangeDisplay)：用于展示数字范围，如“1-10”。

2. **编辑组件 (Editors)**
   - **职责**: 负责单元格数据的编辑逻辑，提供多种编辑器类型以适应不同的数据类型和编辑需求。
   - **实现**: 每个编辑器都实现了 `EditorProps` 接口，确保统一的属性传递和行为。
   - **主要编辑器类型**:
     - 日期编辑器 (DateEditor)：提供日期选择功能，支持弹出式和内嵌式编辑模式。
     - 图片上传编辑器 (ImagesEditor)：支持图片的上传和预览。
     - 多选编辑器 (MultiSelectEditor)：提供多选框或下拉菜单供用户选择多个选项。
     - 其他编辑器：根据需要扩展，如文本编辑器、数字编辑器等。

3. **列管理器 (ColumnManager)**
   - **职责**: 提供表格列的显示/隐藏控制和顺序调整功能，增强用户对表格展示的灵活性。
   - **实现**:
     - 使用 `react-dnd` 实现列顺序的拖拽调整。
     - 分为可见列和隐藏列两个区域，用户可以通过拖拽在两者之间移动列。
     - 支持保存列的设置，保证用户自定义的列配置在刷新后依然有效。

### 关键特性

项目具备多项关键特性，提升用户体验和开发者的使用便利性。

1. **单元格编辑**
   - 支持点击单元格进入编辑状态，激活对应的编辑器。
   - 提供多种类型的编辑器，满足不同数据类型的编辑需求。
   - 支持自定义编辑器，开发者可以根据业务需求扩展特定的编辑组件。

2. **列管理**
   - 支持列的显示/隐藏，用户可以根据需要自定义展示的列。
   - 支持列顺序调整，通过拖拽操作改变列的排列顺序。
   - 支持列设置的保存，用户的列配置在刷新或重新进入页面后依然保持。

3. **样式与交互**
   - 统一的样式处理，确保表格在不同设备和浏览器上的一致性。
   - 响应式设计，适配不同屏幕尺寸，提升移动端使用体验。
   - 流畅的动画效果，增强用户在交互过程中的视觉体验。

4. **类型系统**
   - 完善的 TypeScript 类型定义，提供强类型支持，提升开发效率和代码可靠性。
   - 提供组件接口定义，确保组件间的契约清晰明确，方便团队协作开发。

### 使用示例

以下是一个简单的使用示例，展示如何在项目中接入和使用 `InfinityTable` 组件：

```tsx
import React from 'react';
import { InfinityTable } from './components/InfinityTable';

const columns = [
  {
    title: '文本',
    dataIndex: 'text',
    display: 'text',
    editor: 'text',
  },
  {
    title: '日期',
    dataIndex: 'date',
    display: 'date',
    editor: 'date',
  },
  // ...其他列配置
];

const data = [
  {
    key: '1',
    text: '示例文本',
    date: '2023-10-01',
    // ...其他数据
  },
  // ...其他数据行
];

const handleChange = (updatedData) => {
  console.log('表格数据更新:', updatedData);
};

const TableDemo = () => {
  return (
    <InfinityTable
      columns={columns}
      dataSource={data}
      onChange={handleChange}
    />
  );
};

export default TableDemo;
```

### 扩展性设计

项目在设计之初充分考虑了扩展性，提供多种方式让开发者根据具体业务需求进行定制和扩展。

1. **自定义显示组件**
   - 实现 `DisplayProps` 接口，确保自定义显示组件与现有组件兼容。
   - 将自定义组件注册到显示组件集合中，使其可在表格中使用。

2. **自定义编辑器**
   - 实现 `EditorProps` 接口，确保自定义编辑器与现有编辑器兼容。
   - 将自定义编辑器注册到编辑器集合中，使其可在表格中使用。

3. **样式定制**
   - 提供统一的样式变量，方便开发者进行全局样式调整。
   - 支持主题定制，开发者可以根据项目需求调整组件的主题风格。

### 后续规划

为了进一步提升项目的功能和用户体验，未来将持续进行以下优化和功能扩展：

1. **支持更多类型的编辑器**
   - 增加如富文本编辑器、文件上传编辑器等，满足更多样化的编辑需求。

2. **优化性能和渲染效率**
   - 通过虚拟化技术提升大型数据表格的渲染性能，确保流畅的用户体验。

3. **增加单元格验证功能**
   - 提供数据验证机制，确保用户输入的数据符合预期格式和规则，提高数据的准确性和可靠性。

4. **支持快捷键操作**
   - 提供键盘快捷键支持，提升用户在表格中的操作效率。

5. **支持单元格合并**
   - 增强表格的展示能力，支持跨行跨列的单元格合并，类似于 Excel 的合并单元格功能。

这个 README.md 提供了项目的整体架构、功能模块、关键特性和使用方法的详细说明。如果您觉得还需要补充或修改的地方，请告诉我。
```




to:
倒数第2列，弹出层较宽。最后一列是固定列。解决方案:fixed:right