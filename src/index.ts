/**
 * @file InfinityTable 组件入口文件
 * @description 导出 InfinityTable 组件及其相关类型定义
 */

// 核心组件导出
export { InfinityTable } from './components/InfinityTable';

// 类型定义导出
export type {
  // 核心属性类型
  InfinityTableProps,
  
  // 列配置相关类型
  ColumnType,
  
  // 单元格相关类型
  CellValue,
  EditingCell,
  
  // 编辑器相关类型
  EditorProps,
  EditorConfig,
  
  // 上下文类型
  TableContextType,
  
  // 插件系统类型
  Plugin,
  PluginContext,
  
  // 工具类型
  DisableRule,
} from './components/InfinityTable/types';