import React, { useCallback } from 'react';
import { Upload, message } from 'antd';
import type { UploadFile, UploadProps, RcFile } from 'antd/es/upload/interface';
import { PlusOutlined } from '@ant-design/icons';
import type { EditorProps } from '@types';
import './index.less';

interface ImagesEditorProps extends EditorProps {
  /** 最大上传数量 */
  maxCount?: number;
  /** 接受的文件类型 */
  accept?: string;
  /** 最大文件大小（字节） */
  maxSize?: number;
  /** 自定义上传方法 */
  customRequest?: UploadProps['customRequest'];
  /** 是否禁用 */
  disabled?: boolean;
}

// 添加内部使用的类型
type ImageFileList = UploadFile<any>[];
type ImageUrl = string;

export const ImagesEditor: React.FC<ImagesEditorProps> = ({
  value = [],
  onCellChange,
  style,
  maxCount = 3,
  accept = 'image/*',
  maxSize = 5 * 1024 * 1024,
  customRequest,
  disabled,
  ...props
}) => {
  // 确保 value 始终是数组
  const normalizedValue = React.useMemo(() => 
    Array.isArray(value) ? value : [], 
    [value]
  );

  // 转换为文件列表
  const [internalFileList, setInternalFileList] = React.useState<ImageFileList>(() => 
    normalizedValue.map((url, index) => ({
      uid: `-${index}`,
      name: `图片${index + 1}`,
      status: 'done',
      url,
      thumbUrl: url,
    }))
  );

  // 同步外部值变化
  React.useEffect(() => {
    const newFileList = normalizedValue.map((url, index) => ({
      uid: `-${index}`,
      name: `图片${index + 1}`,
      status: 'done' as const,
      url,
      thumbUrl: url,
    }));
    setInternalFileList(newFileList);
  }, [normalizedValue]);

  // 处理文件大小检查
  const handleBeforeUpload = useCallback((file: RcFile) => {
    try {
      if (maxSize && file.size > maxSize) {
        message.error(`文件大小不能超过 ${maxSize / 1024 / 1024}MB`);
        return Upload.LIST_IGNORE;
      }

      if (accept) {
        const acceptTypes = accept.split(',').map(type => type.trim());
        const isValidType = acceptTypes.some(type => {
          const regex = new RegExp(type.replace('*', '.*'));
          return regex.test(file.type);
        });

        if (!isValidType) {
          message.error(`请上传 ${accept} 格式的文件`);
          return Upload.LIST_IGNORE;
        }
      }

      return true;
    } catch (error) {
      console.error('文件校验失败:', error);
      message.error('文件校验失败');
      return Upload.LIST_IGNORE;
    }
  }, [maxSize, accept]);

  // 处理移除文件
  const handleRemove = useCallback((file: UploadFile) => {
    const index = internalFileList.findIndex(item => item.uid === file.uid);
    if (index > -1) {
      const newUrls = Array.isArray(value) ? [...value] : [];
      newUrls.splice(index, 1);
      onCellChange?.(newUrls);
    }
    return true;
  }, [value, internalFileList, onCellChange]);

  // 处理上传变化
  const handleChange = useCallback<Required<UploadProps>['onChange']>(({ file, fileList }) => {
    // 更新内部状态
    setInternalFileList(fileList);

    // 仅在文件上传完成时更新外部值
    if (file.status === 'done' && file.response) {
      const newUrls = fileList
        .filter(file => file.status === 'done')
        .map(file => file.response?.url || file.url)
        .filter((url): url is string => Boolean(url));

      onCellChange?.(newUrls);
    }
  }, [onCellChange]);

  return (
    <div className="infinity-table-images-editor">
      <Upload
        listType="picture-card"
        fileList={internalFileList}
        beforeUpload={handleBeforeUpload}
        onRemove={handleRemove}
        customRequest={customRequest}
        accept={accept}
        disabled={disabled}
        multiple
        {...props}
        onChange={handleChange}
      >
        {internalFileList.length < maxCount && !disabled && (
          <div className="upload-button">
            <PlusOutlined />
            <div className="upload-text">上传</div>
          </div>
        )}
      </Upload>
    </div>
  );
};

ImagesEditor.displayName = 'ImagesEditor'; 