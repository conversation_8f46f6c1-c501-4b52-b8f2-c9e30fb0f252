@import '../../../styles/variables.less';

.infinity-table-images-editor {
  padding: 8px;
  background: #fff;
  width: 360px;
  max-height: 400px;
  overflow-y: auto;

  .ant-upload-wrapper {
    .ant-upload-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .ant-upload.ant-upload-select {
      margin: 0 !important;
      border-radius: 2px;
      border: 1px dashed #d9d9d9;
      transition: border-color 0.3s;

      &:hover {
        border-color: #40a9ff;
      }
    }

    .ant-upload-list-item {
      margin: 0 !important;
      padding: 0 !important;
      border: 1px solid #d9d9d9;
      border-radius: 2px;
      overflow: hidden;

      .ant-upload-list-item-thumbnail {
        opacity: 1 !important;
        
        img {
          object-fit: cover;
        }
      }

      .ant-upload-list-item-actions {
        opacity: 0;
        transition: opacity 0.3s;
      }

      &:hover {
        .ant-upload-list-item-actions {
          opacity: 1;
        }
      }
    }
  }

  .upload-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: rgba(0, 0, 0, 0.45);

    .anticon {
      font-size: 20px;
      margin-bottom: 4px;
    }

    .upload-text {
      font-size: 12px;
    }
  }

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
}