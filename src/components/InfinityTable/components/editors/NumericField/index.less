@import '../../../styles/variables.less';

.infinity-table-number-editor-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  font-size: 12px;
  color:#1D2129;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 #00000033;
  // padding-top: 4px !important;
  // border: 1px solid #4E5969;
  height: 72px;

  [class$="-form-horizontal"] {
    width: 100%;
    height: 72px;
    padding-top:6px !important;
    padding-left: 1px !important;
  }

  
  [class$="-input-number-group-addon"] {
    border: 0 none !important;
    background-color: #fff !important;
    padding: 1px 17px 0 0px !important;
    color: #86909C !important;
    font-size:12px !important;
  }
  
  .infinity-table-number-editor {
    flex: 1;
    font-size: 12px !important;
    color:#1D2129 !important;

    &.with-suffix {
      font-size: 12px !important;
      [class$="-input-number-input"] {
        text-align: right;
        padding-right: 4px;
      }
    }

    &:hover {
      border-color: transparent;
    }

    &:focus, &-focused {
      border-color: transparent;
      box-shadow: none;
    }

    [class$="-input-number-input"] {
      &:focus {
        border-color: transparent;
        box-shadow: none;
      }
    }
  }
  
  .infinity-table-number-editor-suffix {
    padding: 0 8px;
    color: rgba(0, 0, 0, 0.45);
    white-space: nowrap;
    font-size: 12px !important;
  }
}

.infinity-table-number-editor {
  width: 100%;
  height: 100%;
  
  [class$="-input-number-input"] {
    padding: 4px 11px;
  }

  &:not(.with-suffix) {
    &:hover {
      border-color: #40a9ff;
    }

    &:focus, &-focused {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}