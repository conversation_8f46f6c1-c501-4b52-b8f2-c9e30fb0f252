/**
 * @file NumericField Editor Component
 * @description 表格单元格数字编辑器组件
 * @review 2024/12/19
 */

import React, { useCallback, useEffect } from 'react';
import { InputNumber, Form } from 'antd';
import type { InputNumberProps } from 'antd';
import type { EditorProps } from '../../../types';
import './index.less';

/** 数字编辑器的属性类型 */
interface NumberEditorProps extends EditorProps, Omit<InputNumberProps, 'onChange' | 'value'> {
  /** 后缀单位 */
  suffix?: string;
}

export const NumberEditor: React.FC<NumberEditorProps> = ({
  value,
  onCellChange,
  onBlur,
  style,
  suffix = '',
  max,
  min,
  ...restProps
}) => {
  const [form] = Form.useForm();

  /** 初始化表单值 */
  useEffect(() => {
    form.setFieldsValue({ number: value });
  }, [form, value]);

  /** 处理失焦事件 */
  const handleBlur = useCallback(async (e: React.FocusEvent<HTMLInputElement>) => {
    try {
      const values = await form.validateFields();
      // 当输入为空时返回 null，否则返回数值
      onCellChange?.(values.number === '' ? null : values.number);
    } catch {
      // 验证失败时恢复原值
      form.setFieldsValue({ number: value });
    }
    onBlur?.(e);
  }, [form, onBlur, onCellChange, value]);

  /** 基础样式配置 */
  const baseStyle = {
    height: '100%',
    width: '100%',
    textAlign: suffix ? 'right' : 'left',
    ...style,
  } as const;

  /** 验证规则配置 */
  const validationRules = [
    {
      type: 'number',
      max: Number(max),
      min: Number(min),
      message: `值必须在 ${min} 和 ${max} 之间`,
      transform: (value: string | number) => (value === '' ? null : value),
    },
  ];

  /** 公共 InputNumber 属性 */
  const commonInputProps = {
    controls: false,
    style: baseStyle,
    bordered: false,
    autoFocus: true,
    onBlur: handleBlur,
    ...restProps,
  };

  return (
    <div className="infinity-table-number-editor-wrapper">
      <Form form={form} initialValues={{ number: value }}>
        <Form.Item name="number" noStyle rules={validationRules}>
          {suffix ? (
            <InputNumber
              className="infinity-table-number-editor with-suffix"
              addonAfter={suffix}
              {...commonInputProps}
            />
          ) : (
            <InputNumber
              className="infinity-table-number-editor"
              {...commonInputProps}
            />
          )}
        </Form.Item>
      </Form>
    </div>
  );
};

NumberEditor.displayName = 'NumberEditor';