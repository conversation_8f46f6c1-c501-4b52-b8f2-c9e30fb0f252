/**
 * @file DatePicker Editor Component
 * @description 表格单元格日期选择器编辑器组件
 * @review 2024/12/19
 */

import React, { useCallback, useMemo } from 'react';
import { Calendar } from 'antd';
import type { Moment } from 'moment';
import moment from 'moment';
import 'moment/locale/zh-cn';
import locale from 'antd/es/calendar/locale/zh_CN';
import type { EditorProps } from '@types';
import { EDITOR_CONFIGS } from '@constants/index';
import './index.less';

interface DateEditorProps extends EditorProps {
  /** 日期格式，默认 YYYY-MM-DD */
  format?: string;
  /** 日期是否禁用的判断函数 */
  disabledDate?: (current: Moment) => boolean;
}

type DateValue = string | number;

/**
 * 日期选择器编辑器组件
 * @component DateEditor
 */
export const DateEditor: React.FC<DateEditorProps> = ({
  value,
  onCellChange,
  style,
  format = 'YYYY-MM-DD',
  ...props
}) => {
  // 设置 moment 的语言为中文
  moment.locale('zh-cn');

  /**
   * 将输入值转换为 Moment 对象
   * @param inputValue - 输入的日期值
   * @returns Moment 对象
   */
  const parseDateValue = (inputValue: DateValue): Moment => {
    if (!inputValue) return moment();
    try {
      const parsedDate = moment(inputValue);
      return parsedDate.isValid() ? parsedDate : moment();
    } catch {
      console.warn('[DateEditor] 无效的日期值:', inputValue);
      return moment();
    }
  };

  // 当前日期值
  const currentValue = useMemo(() => 
    parseDateValue(value as DateValue), 
    [value]
  );

  /**
   * 处理日期选择回调
   * @param date - 选中的日期
   */
  const handleSelect = useCallback((date: Moment) => {
    if (!date.isValid()) {
      console.warn('[DateEditor] 选择的日期无效');
      return;
    }

    try {
      const formattedValue = date.format(format);
      onCellChange?.(formattedValue);
    } catch (error) {
      console.error('[DateEditor] 日期格式化错误:', error);
      onCellChange?.('');
    }
  }, [format, onCellChange]);

  /**
   * 处理日期变更回调
   * @param date - 变更后的日期
   */
  const handleChange = useCallback((date: Moment) => {
    handleSelect(date);
  }, [handleSelect]);

  return (
    <div 
      className="infinity-table-date-editor"
      style={{
        width: EDITOR_CONFIGS.DATE.width,
        ...style
      }}
    >
      <Calendar
        fullscreen={false}
        value={currentValue}
        onSelect={handleSelect}
        locale={locale}
        {...props}
        onChange={handleChange}
      />
    </div>
  );
};

DateEditor.displayName = 'DateEditor';

export default DateEditor;