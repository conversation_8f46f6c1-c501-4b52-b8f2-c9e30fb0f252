/**
 * @file NumberRange Editor Component
 * @description 表格单元格数字范围编辑器组件
 * @review 2024/12/19
 */

import React, { useCallback } from 'react';
import { InputNumber, Form, Space, Button, message } from 'antd';
import classNames from 'classnames';
import type { EditorProps } from '../../../types';
import './index.less';

/** 数字范围编辑器的值类型 */
type NumberRangeValue = [number?, number?] | { min?: number; max?: number };

/** 数字范围编辑器的属性类型 */
interface NumberRangeEditorProps extends EditorProps {
  /** 单位显示文本 */
  unit?: string;
  /** 数字精度 */
  precision?: number;
  /** 最小值限制 */
  min?: number;
  /** 最大值限制 */
  max?: number;
  /** 步长 */
  step?: number;
  /** 格式化函数 */
  formatter?: (value: number | undefined, info?: { userTyping: boolean; input: string }) => string;
  /** 解析函数 */
  parser?: (displayValue: string | undefined) => number;
  /** 占位符文本，可以是字符串或字符串数组 [最小值占位符, 最大值占位符] */
  placeholder?: [string, string] | string;
  /** 保存回调 */
  onSave?: () => void;
  /** 取消回调 */
  onCancel?: () => void;
}

/** 表单值类型 */
interface FormValues {
  min?: number | null;
  max?: number | null;
}

export const NumberRangeEditor: React.FC<NumberRangeEditorProps> = ({
  value,
  onCellChange,
  onSave,
  onCancel,
  style,
  ...props
}) => {
  const [form] = Form.useForm<FormValues>();

  /**
   * 格式化输出值
   * @param values - 表单值
   */
  const formatValue = useCallback((values: FormValues): NumberRangeValue => {
    const { min, max } = values;
    
    const numMin = typeof min === 'number' ? min : undefined;
    const numMax = typeof max === 'number' ? max : undefined;

    return Array.isArray(value) 
      ? [numMin, numMax] as [number?, number?]
      : { min: numMin, max: numMax };
  }, [value]);

  /**
   * 处理输入值变化
   * @param field - 字段名称（min 或 max）
   * @param inputValue - 输入的数值
   */
  const handleInputChange = useCallback(
    async (field: 'min' | 'max', inputValue: number | null) => {
      const currentValues = form.getFieldsValue();
      const newValues = {
        ...currentValues,
        [field]: inputValue,
      };
      
      form.setFieldsValue(newValues);
      const formattedValue = formatValue(newValues);
      onCellChange?.(formattedValue);
    },
    [form, onCellChange, formatValue]
  );

  /** 处理取消操作 */
  const handleCancel = useCallback(() => {
    onCancel?.();
  }, [onCancel]);

  /**
   * 解析输入值为标准格式
   * @param val - 输入值
   */
  const parseValue = (val: unknown): FormValues => {
    if (!val) return { min: undefined, max: undefined };
    
    if (Array.isArray(val)) {
      return { min: val[0], max: val[1] };
    }
    
    if (typeof val === 'object' && val !== null) {
      return val as FormValues;
    }
    
    return { min: undefined, max: undefined };
  };

  /** 处理保存操作 */
  const handleSave = useCallback(async () => {
    try {
      const values = await form.validateFields();
      const formattedValue = formatValue(values);
      onCellChange?.(formattedValue);
      onSave?.();
    } catch (error) {
      if (error && typeof error === 'object' && 'errorFields' in error) {
        const { errorFields } = error as { errorFields: { errors: string[] }[] };
        if (errorFields?.[0]?.errors?.[0]) {
          message.error(errorFields[0].errors[0]);
        }
      }
    }
  }, [form, onCellChange, onSave, formatValue]);

  /** 判断是否有值 */
  const parsedValue = parseValue(value);
  const hasValue = parsedValue.min != null || parsedValue.max != null;

  /** 获取占位符文本 */
  const getPlaceholder = (index: 0 | 1): string => {
    if (Array.isArray(props.placeholder)) {
      return props.placeholder[index];
    }
    return index === 0 ? '最小值' : '最大值';
  };

  return (
    <Form 
      form={form} 
      className="infinity-table-number-range-editor"
      style={style}
      initialValues={parseValue(value)}
    >
      <div className={classNames('number-range-content', { 'has-value': hasValue })}>
        <Space.Compact>
          <Form.Item 
            name="min"
            noStyle
            rules={[
              { type: 'number', message: '请输入有效数字' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const max = getFieldValue('max');
                  if (value != null && max != null && value > max) {
                    return Promise.reject('最小值不能大于最大值');
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <InputNumber
              className="infinity-table-number-range-input"
              controls={false}
              autoFocus
              style={{ width: '70px' }}
              bordered={false}
              {...props}
              placeholder={getPlaceholder(0)}
              onChange={(value) => handleInputChange('min', value)}
            />
          </Form.Item>
          <span className="infinity-table-number-range-separator">-</span>
          <Form.Item 
            name="max"
            noStyle
            rules={[
              { type: 'number', message: '请输入有效数字' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const min = getFieldValue('min');
                  if (value != null && min != null && value < min) {
                    return Promise.reject('最大值不能小于最小值');
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <InputNumber
              className="infinity-table-number-range-input"
              controls={false}
              bordered={false}
              style={{ width: '70px' }}
              {...props}
              placeholder={getPlaceholder(1)}
              onChange={(value) => handleInputChange('max', value)}
            />
          </Form.Item>
          {props.unit && <span className="infinity-table-number-range-unit">{props.unit}</span>}
        </Space.Compact>
      </div>
      <div className="number-range-footer">
        <Button type="primary" onClick={handleSave}>保存</Button>
        <Button onClick={handleCancel}>取消</Button>
      </div>
    </Form>
  );
};

NumberRangeEditor.displayName = 'NumberRangeEditor';