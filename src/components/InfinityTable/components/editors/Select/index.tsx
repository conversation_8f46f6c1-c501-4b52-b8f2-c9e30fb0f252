/**
 * @file Select Editor Component
 * @description 表格单元格下拉选择器编辑器组件
 * @review 2024/12/19
 */

import React, { useCallback, useMemo } from 'react';
import type { EditorProps } from '../../../types';
import './index.less';

/**
 * 下拉选项接口定义
 */
interface SelectOption {
  /** 选项值 */
  value: string;
  /** 选项显示文本 */
  label: string;
  /** 选项文本颜色（可选） */
  color?: string;
}

/**
 * Select编辑器属性接口
 */
interface SelectEditorProps extends EditorProps {
  /** 可选项列表 */
  options?: SelectOption[];
  /** 占位文本 */
  placeholder?: string;
}

/** 每个选项的固定高度（包含内边距），单位：像素 */
const OPTION_HEIGHT = 32;

/** 组件类名前缀 */
const PREFIX_CLS = 'infinity-table-select-editor';

/**
 * 下拉选择器编辑器组件
 */
export const SelectEditor: React.FC<SelectEditorProps> = ({
  value,
  onCellChange,
  options = [],
  placeholder = '请选择',
}) => {
  // 计算下拉列表容器高度
  const listHeight = useMemo(() => options.length * OPTION_HEIGHT + 2, [options.length]);

  // 处理选项点击事件
  const handleOptionClick = useCallback((optionValue: string) => {
    onCellChange?.(optionValue);
  }, [onCellChange]);

  // 渲染选项列表
  const renderOptions = useCallback(() => (
    <div className="options-list">
      {options.map((option) => (
        <div
          key={option.value}
          className={`option-item ${value === option.value ? 'selected' : ''}`}
          onClick={() => handleOptionClick(option.value)}
          style={option.color ? { color: option.color } : undefined}
          title={option.label} // 添加title属性，方便文本过长时查看完整内容
        >
          {option.label}
        </div>
      ))}
    </div>
  ), [options, value, handleOptionClick]);

  return (
    <div 
      className={PREFIX_CLS}
      style={{ height: `${listHeight}px` }}
    >
      {options.length > 0 ? (
        renderOptions()
      ) : (
        <div className="placeholder" title={placeholder}>
          {placeholder}
        </div>
      )}
    </div>
  );
};

// 设置组件显示名称，方便调试
SelectEditor.displayName = 'SelectEditor';