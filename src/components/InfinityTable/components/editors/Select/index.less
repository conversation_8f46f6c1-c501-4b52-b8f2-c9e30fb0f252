.infinity-table-select-editor {
  width: 100%;
  background: #fff;
  border: 1px solid #d9d9d9;
  outline: none;

  &:hover {
    background-color: #fafafa;
  }
  
  .options-list {
    // 移除height和overflow设置，因为父元素已经有了准确的高度
  }

  .option-item {
    height: 32px;
    line-height: 32px;
    padding: 0 8px;
    cursor: pointer;
    transition: background-color 0.3s;
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 12px !important;
    color: #1D2129 !important;

    &:hover {
      background-color: #E8F3FF;
    }

    &.selected {
      font-weight: 600;
      background-color: #E8F3FF;
    }
  }

  .placeholder {
    height: 32px;
    line-height: 32px;
    padding: 0 8px;
    color: #bfbfbf;
  }
} 