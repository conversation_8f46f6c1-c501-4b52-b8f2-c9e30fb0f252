/**
 * @file TextField Editor Component
 * @description 表格单元格文本编辑器组件
 * @review 2024/12/19
 */

import React, { useState, useEffect, type ChangeEvent } from 'react';
import { Input } from 'antd';
import type { EditorProps } from '../../../types';
import { useTableDebug } from '../../../utils/debug';
import './index.less';

interface TextEditorProps extends EditorProps {
  /** 最大输入长度 */
  maxLength?: number;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否显示清除按钮 */
  allowClear?: boolean;
  /** 自适应内容高度 */
  autoSize?: boolean | {
    minRows?: number;
    maxRows?: number;
  };
}

const DEFAULT_MIN_HEIGHT = 72;

export const TextEditor: React.FC<TextEditorProps> = ({
  value,
  onCellChange,
  style,
  maxLength,
  allowClear = false,
  placeholder = '请输入',
  // 暂时注释掉 autoSize，后续可能会用到
  // autoSize = { minRows: 1, maxRows: 6 },
  ...props
}) => {
  const [innerValue, setInnerValue] = useState<string>('');
  const debug = useTableDebug();

  // 同步外部传入的值
  useEffect(() => {
    setInnerValue(String(value ?? ''));
  }, [value]);

  /**
   * 处理输入值变化
   * @param e 输入事件对象
   */
  const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setInnerValue(newValue);
  };

  /**
   * 处理失焦事件，触发值更新
   */
  const handleBlur = () => {
    debug.info('文本编辑器: 最终修改值', innerValue);
    onCellChange?.(innerValue);
  };

  return (
    <div className="infinity-table-text-editor-wrapper">
      <Input.TextArea
        className="infinity-table-text-editor"
        value={innerValue}
        style={{
          ...style,
          minHeight: `${DEFAULT_MIN_HEIGHT}px`,
          border: 'none',
          fontSize: '12px',
          resize: 'none',
          lineHeight: '20px',
        }}
        maxLength={maxLength}
        placeholder={placeholder}
        allowClear={allowClear}
        autoFocus
        {...props}
        onChange={handleChange} // 放在props后面以确保使用我们自己的onChange
        onBlur={handleBlur}
      />
    </div>
  );
};

TextEditor.displayName = 'TextEditor';

export default TextEditor;