/**
 * @file MultiSelect Editor Component
 * @description 表格单元格多选编辑器组件
 * @review 2024/12/19
 */

import React, { useCallback, useMemo } from 'react';
import type { EditorProps } from '../../../types';
import './index.less';

/** 选项高度配置（包含padding） */
const OPTION_HEIGHT = 32;

/** 选项数据结构定义 */
interface SelectOption {
  value: string;
  label: string;
  color?: string;
}

/** 组件属性定义 */
interface MultiSelectEditorProps extends EditorProps<(string | number)[]> {
  /** 可选项列表 */
  options?: SelectOption[];
  /** 占位文本 */
  placeholder?: string;
}

/**
 * 多选编辑器组件
 * @param props - 组件属性
 * @returns React组件
 */
export const MultiSelectEditor: React.FC<MultiSelectEditorProps> = ({
  value = [],
  onCellChange,
  options = [],
  placeholder = '请选择',
}) => {
  // 将输入值统一转换为字符串数组
  const selectedValues = useMemo(() => {
    if (!Array.isArray(value)) return [];
    return value.map(v => String(v)).filter(Boolean);
  }, [value]);
  
  // 计算列表容器高度
  const listHeight = useMemo(() => 
    options.length * OPTION_HEIGHT + 2, 
    [options.length]
  );

  // 处理选项点击事件
  const handleOptionClick = useCallback((optionValue: string) => {
    const newValues = selectedValues.includes(optionValue)
      ? selectedValues.filter(v => v !== optionValue)
      : [...selectedValues, optionValue];
    onCellChange?.(newValues);
  }, [selectedValues, onCellChange]);

  // 渲染选项列表
  const renderOptions = useCallback(() => (
    options.map((option) => (
      <div
        key={option.value}
        className={`option-item ${selectedValues.includes(option.value) ? 'selected' : ''}`}
        onClick={() => handleOptionClick(option.value)}
        style={option.color ? { color: option.color } : undefined}
      >
        {option.label}
      </div>
    ))
  ), [options, selectedValues, handleOptionClick]);

  return (
    <div 
      className="infinity-table-select-editor"
      style={{ height: `${listHeight}px` }}
    >
      {options.length > 0 ? (
        <div className="options-list">
          {renderOptions()}
        </div>
      ) : (
        <div className="placeholder">{placeholder}</div>
      )}
    </div>
  );
};

// 设置组件显示名称，用于调试
MultiSelectEditor.displayName = 'MultiSelectEditor';