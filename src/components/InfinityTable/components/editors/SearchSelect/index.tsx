import React, { useRef, useEffect, useState } from 'react';
import { Spin, Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import type { EditorProps } from '../../../types';
import './index.less';
import { EDITOR_CONFIGS } from '../../../constants';

export interface SearchOption {
  value: string;
  label: string;
}

export interface SearchParams {
  keyword: string;
  record?: Record<string, unknown>;
}

interface SearchSelectEditorProps extends EditorProps {
  options?: SearchOption[];
  onSearch?: (params: SearchParams) => Promise<SearchOption[]>;
  placeholder?: string;
  initialOptions?: SearchOption[];
}

export const SearchSelectEditor: React.FC<SearchSelectEditorProps> = ({
  value,
  onCellChange,
  onSearch,
  style,
  placeholder = '请输入关键字搜索',
  record,
  initialOptions = [],
}) => {
  const [searchText, setSearchText] = useState('');
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<SearchOption[]>(initialOptions);
  const [activeIndex, setActiveIndex] = useState(-1);
  const inputRef = useRef<Input>(null);
  const optionsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  // 处理搜索文本高亮
  const highlightOption = (option: SearchOption) => {
    if (!searchText) return option.label;
    
    const index = option.label.toLowerCase().indexOf(searchText.toLowerCase());
    if (index === -1) return option.label;

    const beforeStr = option.label.substring(0, index);
    const searchStr = option.label.substring(index, index + searchText.length);
    const afterStr = option.label.substring(index + searchText.length);

    return (
      <span>
        {beforeStr}
        <span style={{ color: '#f50' }}>{searchStr}</span>
        {afterStr}
      </span>
    );
  };

  // 处理搜索
  const handleSearch = React.useCallback(async (text: string) => {
    setSearchText(text);
    
    if (!text.trim()) {
      setOptions(initialOptions);
      return;
    }

    if (!onSearch) {
      const filtered = initialOptions.filter(option => 
        option.label.toLowerCase().includes(text.toLowerCase())
      );
      setOptions(filtered);
      return;
    }

    setLoading(true);
    try {
      const searchParams: SearchParams = {
        keyword: text,
        record,
      };
      const results = await onSearch(searchParams);
      setOptions(results);
    } finally {
      setLoading(false);
    }
  }, [onSearch, record, initialOptions]);

  // 处理选项点击
  const handleOptionClick = (option: SearchOption) => {
    onCellChange?.(option);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // 始终阻止上下键的默认行为
    if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
      e.preventDefault();  // 阻止默认的页面滚动
    }

    if (!options.length) return;

    switch (e.key) {
      case 'ArrowDown':
        setActiveIndex(prev => {
          const next = prev + 1 >= options.length ? 0 : prev + 1;
          scrollToOption(next);
          return next;
        });
        break;
      case 'ArrowUp':
        setActiveIndex(prev => {
          const next = prev - 1 < 0 ? options.length - 1 : prev - 1;
          scrollToOption(next);
          return next;
        });
        break;
      case 'Enter':
        e.preventDefault();
        if (activeIndex >= 0 && activeIndex < options.length) {
          handleOptionClick(options[activeIndex]);
        }
        break;
    }
  };

  // 滚动到选中的选项
  const scrollToOption = (index: number) => {
    if (!optionsRef.current) return;
    
    const container = optionsRef.current;
    const option = container.children[index] as HTMLElement;
    if (!option) return;

    const containerTop = container.scrollTop;
    const containerBottom = containerTop + container.clientHeight;
    const optionTop = option.offsetTop;
    const optionBottom = optionTop + option.clientHeight;

    if (optionTop < containerTop) {
      container.scrollTop = optionTop;
    } else if (optionBottom > containerBottom) {
      container.scrollTop = optionBottom - container.clientHeight;
    }
  };

  const dropdownContent = (
    <div className="search-dropdown-content">
      {loading ? (
        <div className="loading-container">
          <Spin size="small" />
        </div>
      ) : (
        <div className="options-container" ref={optionsRef}>
          {options.length > 0 ? (
            options.map((option, index) => (
              <div
                key={option.value}
                className={`option-item ${index === activeIndex ? 'active' : ''} ${option.value === value?.value ? 'selected' : ''}`}
                onClick={() => handleOptionClick(option)}
                onMouseEnter={() => setActiveIndex(index)}
              >
                {highlightOption(option)}
              </div>
            ))
          ) : (
            <div className="empty-text">无匹配数据</div>
          )}
        </div>
      )}
    </div>
  );

  return (
    <div className="infinity-table-search-select-editor" style={{
      ...style,
      width: EDITOR_CONFIGS.SEARCH_SELECT.width,
    }}>
      <div className="search-container">
        <Input
          ref={inputRef}
          placeholder={placeholder}
          value={searchText}
          onChange={e => handleSearch(e.target.value)}
          onKeyDown={handleKeyDown}
          suffix={<SearchOutlined style={{ color: '#999' }} />}
        />
        {dropdownContent}
      </div>
    </div>
  );
};

SearchSelectEditor.displayName = 'SearchSelectEditor';