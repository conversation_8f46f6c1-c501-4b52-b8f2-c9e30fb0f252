import React, { useEffect, useRef, useState } from 'react';
import type { CSSProperties } from 'react';
import { EDITOR_CONFIGS } from '@constants/index';
import { useClickAway } from 'ahooks';
import './index.less';
import { useTableDebug } from '../../../utils/debug';
import ReactDOM from 'react-dom';
import { usePrefixCls } from '@utils/getPrefixCls';

interface Position {
  top: number;
  left: number;
  width: number;
  height: number;
}

interface EditorPopupProps {
  children: React.ReactElement;
  position?: Position;
  type?: string;
  visible: boolean;
  onClose: (reason: 'blur' | 'save' | 'cancel') => void;
  placement?: 'overlay' | 'below';
  overlayContent?: React.ReactNode;
  cellTarget?: HTMLElement;
  popupProps?: {
    width?: number | string | 'cell';
    height?: number | string;
    minWidth?: number;
    maxWidth?: number;
    minHeight?: number;
    maxHeight?: number;
    offset?: [number, number];
    className?: string;
    style?: CSSProperties;
  };
}

export const EditorPopup: React.FC<EditorPopupProps> = ({
  children,
  overlayContent,
  type = 'text',
  visible,
  onClose,
  placement = 'below',
  cellTarget,
  popupProps = {}
}) => {
  const popupRef = useRef<HTMLDivElement>(null);
  const editorConfig = type ? EDITOR_CONFIGS[type.toUpperCase() as keyof typeof EDITOR_CONFIGS] : null;
  const debug = useTableDebug();
  const prefixCls = usePrefixCls('table');
  const [editorKey, setEditorKey] = React.useState(0);
  
  // 使用两个状态来控制渲染
  const [isReady, setIsReady] = React.useState(false);
  const [currentTarget, setCurrentTarget] = React.useState<HTMLElement | null>(null);

  // 添加一个新的 ref 用于测量实际高度
  const measureRef = useRef<HTMLDivElement>(null);
  const [actualHeight, setActualHeight] = useState<number | null>(null);

  // 控制渲染时机
  useEffect(() => {
    if (visible && cellTarget) {
      // 先确保旧内容被清除
      setIsReady(false);
      setCurrentTarget(null);
      
      // 使用两个 RAF 来确保 DOM 完全更新
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          setEditorKey(prev => prev + 1);
          setCurrentTarget(cellTarget);
          setIsReady(true);
        });
      });
    } else {
      setIsReady(false);
      setCurrentTarget(null);
    }
  }, [visible, cellTarget]);

  useEffect(() => {
    if (visible) {
      debug.info('编辑浮层','显示');
    }
  }, [visible]);

  // 修改 useClickAway - 保留点击外部关闭功能
  useClickAway(
    (event: MouseEvent) => {
      if (visible) {
        const target = event.target as HTMLElement;
        const isClickInCell = cellTarget?.contains(target);
        const clickPath = event.composedPath?.() || [];
        
        const isClickInPortal = clickPath.some(node => {
          if (node instanceof Element) {
            return node.getAttribute('role') === 'listbox' ||
                   node.getAttribute('role') === 'tooltip' ||
                   node.getAttribute('role') === 'dialog' ||
                   node.getAttribute('class')?.includes('-dropdown') ||
                   node.getAttribute('class')?.includes('-popup') ||
                   node.getAttribute('class')?.includes('-picker-dropdown');
          }
          return false;
        });

        if (!isClickInCell && !isClickInPortal) {
          setTimeout(() => {
            debug.info('编辑浮层', '关闭');
            const closeAction = type === 'number-range' ? 'cancel' : 'blur';
            onClose(closeAction);
          }, 30);
        }
      }
    },
    popupRef,
    'mousedown'
  );

    // 处理 overlayContent 的渲染
    useEffect(() => {
      if (type === 'text' || type === 'number') return;
  
      if (visible && cellTarget && overlayContent) {
        console.log('overlayContent',visible,cellTarget,overlayContent);
        const overlayContainer = document.createElement('div');
        overlayContainer.className = 'editor-cell-overlay';
        overlayContainer.style.cssText = `
          position: absolute;
          top: 0px;
          left: 0px;
          width: 100%;
          height: 100%;
          background: #fff;
          box-shadow: 0 2px 12px 0 #00000033;
          // border: 1px solid #4E5969;
          z-index: 99;
        `;
  
        cellTarget.style.position = 'relative';
        cellTarget.appendChild(overlayContainer);
  
        ReactDOM.render(<>{overlayContent}</>, overlayContainer);
  
        return () => {
          if (cellTarget.contains(overlayContainer)) {
            ReactDOM.unmountComponentAtNode(overlayContainer);
            cellTarget.removeChild(overlayContainer);
          }
          cellTarget.style.position = '';
        };
      }
    }, [visible, cellTarget, overlayContent, type]);

  // 获取编辑器预期高度
  const getEditorHeight = () => {
    // 如果已经测量到实际高度，优先使用
    if (actualHeight !== null) {
      //console.log('actualHeight',actualHeight);
      return actualHeight;
    }

    // 其他默认逻辑保持不变...
    const configKey = type.toUpperCase().replace('-', '_') as keyof typeof EDITOR_CONFIGS;
    const editorConfig = EDITOR_CONFIGS[configKey];

    if (configKey.toUpperCase() === 'SELECT') {
      const options = children?.props?.options || [];
      const itemCount = options.length;
      const contentHeight = (itemCount * 32);
      return contentHeight;
    }
    
    if (editorConfig?.height) {
      return editorConfig.height;
    }

    if (popupProps.height) {
      return typeof popupProps.height === 'number' 
        ? popupProps.height 
        : parseInt(popupProps.height);
    }

    if (popupProps.maxHeight) {
      return typeof popupProps.maxHeight === 'number'
        ? popupProps.maxHeight
        : parseInt(popupProps.maxHeight);
    }

    //console.log('popupProps.height',300);
    return 300;
  };

  // 计算是否需要向上展示
  const shouldShowAbove = () => {
    if (placement === 'overlay') return false;
    
    const tableBody = cellTarget.closest(`.${prefixCls}-body`);
    if (!tableBody) return false;

    const cellRect = cellTarget.getBoundingClientRect();
    const tableRect = tableBody.getBoundingClientRect();
    const editorHeight = getEditorHeight();
    
    // 检查底部空间是否足够
    const spaceBelow = tableRect.bottom - cellRect.bottom;
    return spaceBelow < editorHeight;
  };

  // 获取编辑器宽度
  const getEditorWidth = () => {
    // 1. 优先使用配置的宽度
    const configKey = type.toUpperCase().replace('-', '_') as keyof typeof EDITOR_CONFIGS;
    const editorConfig = EDITOR_CONFIGS[configKey];

    //console.log(editorConfig,configKey);
    
    if (editorConfig?.width) {
      return editorConfig.width;
    }

    // 2. 其次使用外部传入的 width
    if (popupProps.width) {
      if (popupProps.width === 'cell') {
        return cellTarget?.getBoundingClientRect().width || 'auto';
      }
      return typeof popupProps.width === 'number' 
        ? popupProps.width 
        : parseInt(popupProps.width);
    }

    // 3. 最后使用单元格宽度作为默认值
    return cellTarget?.getBoundingClientRect().width || 'auto';
  };

  // 修改 MeasureComponent 组件
  const MeasureComponent = () => {
    useEffect(() => {
      try {
        if (measureRef.current) {
          const height = measureRef.current.getBoundingClientRect().height;
          setActualHeight(height);
        }
      } catch (error) {
        console.error('Failed to measure editor height:', error);
      }
    }, []);

    // useEffect(() => {
    //   return () => {
    //     setActualHeight(null); // 组件卸载时重置高度
    //   };
    // }, []);

    return (
      <div 
        ref={measureRef}
        style={{ 
          position: 'absolute',
          visibility: 'hidden',
          pointerEvents: 'none',
          width: getEditorWidth(),
          left: '-9999px',
          top: '-9999px',
        }}
      >
        {React.cloneElement(children as React.ReactElement, { key: 'measure' })}
      </div>
    );
  };

  // 只有当完全准备好时才渲染
  if (!isReady || !currentTarget) {
    // 即使未准备好显示，也要渲染测量组件
    return ReactDOM.createPortal(<MeasureComponent />, document.body);
  }

  // 创建编辑器内容
  const editorContent = (
    <div 
      ref={popupRef}
      className={`infinity-table-editor-popup ${placement} ${popupProps.className || ''}`}
      style={{
        position: 'absolute',
        top: placement === 'overlay' 
          ? 0 
          : shouldShowAbove() 
            ? `-${actualHeight || getEditorHeight()}px`
            : '72px',
        left: 0,
        width: getEditorWidth(),
        background: '#fff',
        boxShadow: placement === 'overlay' ? 'none' : '0 3px 6px -4px rgba(0,0,0,.12), 0 6px 16px 0 rgba(0,0,0,.08)',
        borderRadius: placement === 'overlay' ? '0' : '2px',
        zIndex: editorConfig?.zIndex || 100,
        ...popupProps.style,
      }}
      onClick={e => e.stopPropagation()}
    >
      {React.cloneElement(children as React.ReactElement, { 
        key: editorKey,
        onClose 
      })}
    </div>
  );

  // 将辑器内容渲染到单元格内部
  return ReactDOM.createPortal(editorContent, currentTarget);
}; 
