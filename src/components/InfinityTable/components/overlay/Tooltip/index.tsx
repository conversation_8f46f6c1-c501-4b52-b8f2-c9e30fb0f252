import React from 'react';
import type { TooltipState } from '../../../types';
import type { EditingLockConfig, PermissionConfig } from '../../../types';

interface TooltipProps {
  tooltipState: TooltipState;
  disableControl?: PermissionConfig<any>;
  editingLock?: EditingLockConfig;
  tooltipRef: React.RefObject<HTMLDivElement>;
}

export const Tooltip: React.FC<TooltipProps> = ({
  tooltipState,
  disableControl,
  editingLock,
  tooltipRef,
}) => {
  if (!tooltipState.visible) return null;

  const getMessage = () => {
    // 如果有自定义消息，优先使用自定义消息
    if (tooltipState.message) {
      return tooltipState.message;
    }
    
    // 否则使用默认消息
    if (tooltipState.isPermissionTip) {
      return disableControl?.tooltip?.message || '暂无编辑权限';
    }
    return editingLock?.tooltip?.message || '他人正在编辑';
  };

  return (
    <div
      ref={tooltipRef}
      className="infinity-table-tooltip"
      style={{
        position: 'fixed',
        left: tooltipState.position.x,
        top: tooltipState.position.y,
        transform: 'translate(-50%, -100%)',
        backgroundColor: 'rgba(0, 0, 0, 0.75)',
        color: '#fff',
        padding: '6px 8px',
        borderRadius: '4px',
        fontSize: '12px',
        zIndex: 1000,
        pointerEvents: 'none',
        whiteSpace: 'nowrap',
        ...(tooltipState.isPermissionTip 
          ? disableControl?.tooltip?.style 
          : editingLock?.tooltip?.style),
      }}
    >
      {getMessage()}
    </div>
  );
};