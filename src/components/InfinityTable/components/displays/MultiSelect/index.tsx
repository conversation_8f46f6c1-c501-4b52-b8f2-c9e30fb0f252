import React from 'react';
import type { DisplayProps } from '../../../types';
import { ClearButton } from '../../common/ClearButton';
import './index.less';

interface SelectOption {
  value: string;
  label: string;
  color?: string;
}

interface MultiSelectDisplayProps extends DisplayProps {
  options?: SelectOption[];
  maxWidth?: number;
  overlayProps?: {
    showClear?: boolean;
  };
}

export const MultiSelectDisplay: React.FC<MultiSelectDisplayProps> = ({ 
  value = [],
  style,
  options = [],
  onClear,
  overlayProps,
  maxWidth = 200
}) => {
  const tags = React.useMemo(() => {
    if (!Array.isArray(value)) return [];
    
    return value.map(itemValue => {
      const option = options.find(opt => opt.value === itemValue);
      return option || { value: itemValue, label: itemValue };
    });
  }, [value, options]);

  const renderTag = (option: SelectOption) => (
    <div
      key={option.value}
      style={{
        backgroundColor: option.color || 'rgba(134, 144, 156, 0.1)',
      }}
      className="infinity-table-multiselect-item"
    >
      {option.label}
    </div>
  );

  return (
    <div className="infinity-table-multiselect-display" style={{ ...style, maxWidth }}>
      {(overlayProps?.showClear && onClear && options.length > 0) && (
        <ClearButton onClear={onClear} />
      )}
      {tags.map(renderTag)}
    </div>
  );
};

MultiSelectDisplay.displayName = 'MultiSelectDisplay';