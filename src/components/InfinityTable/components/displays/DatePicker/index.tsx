import React from 'react';
import dayjs from 'dayjs';
import type { DisplayProps } from '../../../types';
import './index.less';

interface DateDisplayProps extends DisplayProps {
  format?: string;
}

export const DateDisplay: React.FC<DateDisplayProps> = ({ 
  value,
  style,
  format = 'YYYY-MM-DD'
}) => (
  <span className="date-display" style={style}>
    {value ? dayjs(value).format(format) : '-'}
  </span>
);

DateDisplay.displayName = 'DateDisplay'; 