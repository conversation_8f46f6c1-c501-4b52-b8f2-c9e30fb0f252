import React from 'react';
import type { DisplayProps } from '../../../types';
import { ClearButton } from '../../common/ClearButton';
import './index.less';

interface SelectOption {
  value: string;
  label: string;
  color?: string;
}

interface SelectDisplayProps extends DisplayProps {
  options?: SelectOption[];
  onClear?: () => void;
  overlayProps?: {
    showClear?: boolean;
  };
}

export const SelectDisplay: React.FC<SelectDisplayProps> = ({ 
  value,
  style,
  color,
  options = [],
  onClear,
  overlayProps
}) => {
  const option = React.useMemo(() => {
    return options.find(opt => opt.value === value);
  }, [value, options]);
  
  const textRef = React.useRef<HTMLDivElement>(null);
  const [isMultiline, setIsMultiline] = React.useState(false);
  
  React.useEffect(() => {
    if (textRef.current) {
      const height = textRef.current.offsetHeight;
      setIsMultiline(height > 42); // 20px 是单行文本的高度
    }
  }, [option?.label]);
  
  return (
    <div 
      ref={textRef}
      style={{
        ...style,
        backgroundColor: option?.color || color || 'rgba(134, 144, 156, 0.1)',
      }}
      className={`infinity-table-select-display ${isMultiline ? 'multiline' : ''}`}
    >
      {(overlayProps?.showClear && onClear && options.length > 0) && (
        <ClearButton onClear={onClear} />
      )}
      {option?.label || ''}
    </div>
  );
};

SelectDisplay.displayName = 'SelectDisplay'; 