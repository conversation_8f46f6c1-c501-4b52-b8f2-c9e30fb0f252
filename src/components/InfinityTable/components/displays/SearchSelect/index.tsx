import React from 'react';
import type { DisplayProps } from '../../../types';
import { ClearButton } from '../../common/ClearButton';
import './index.less';

export interface SearchOption {
  value: string;
  label: string;
}

interface SearchSelectDisplayProps extends DisplayProps {
  options?: SearchOption[];
  initialOptions?: SearchOption[];
  onClear?: () => void;
  overlayProps?: {
    showClear?: boolean;
  };
}

export const SearchSelectDisplay: React.FC<SearchSelectDisplayProps> = ({ 
  value,
  style,
  onClear,
  overlayProps
}) => {
  const displayValue = typeof value === 'object' && value ? value.label : '-';
  
  const textRef = React.useRef<HTMLDivElement>(null);
  const [isMultiline, setIsMultiline] = React.useState(false);
  
  React.useEffect(() => {
    if (textRef.current) {
      const height = textRef.current.offsetHeight;
      setIsMultiline(height > 42);
    }
  }, [displayValue]);
  
  return (
    <div 
      ref={textRef}
      style={{
        ...style,
        backgroundColor: 'rgba(134, 144, 156, 0.1)',
      }}
      className={`infinity-table-search-select-display ${isMultiline ? 'multiline' : ''}`}
    >
      {(overlayProps?.showClear && onClear && displayValue) && (
        <ClearButton onClear={onClear} />
      )}
      {displayValue}
    </div>
  );
};

SearchSelectDisplay.displayName = 'SearchSelectDisplay';