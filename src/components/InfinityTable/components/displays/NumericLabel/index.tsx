import React from 'react';
import type { DisplayProps } from '../../../types';
import './index.less';

interface NumberDisplayProps extends DisplayProps {
  prefix?: string;
  suffix?: string;
}

export const NumberDisplay: React.FC<NumberDisplayProps> = ({ 
  value,
  style,
  prefix,
  suffix
}) => {
  const formattedValue = React.useMemo(() => {
    if (value == null) return '';
    
    //暂时不考虑精度参数
    let displayValue = `${value}`;
      
    if (prefix) displayValue = prefix + displayValue;
    if (suffix) displayValue = displayValue + ' '+ suffix;
    
    return displayValue;
  }, [value, prefix, suffix]);

  return (
    <div 
      className={`infinity-table-number-display ${suffix ? 'with-suffix' : ''}`} 
      style={style}
    >
      {formattedValue}
    </div>
  );
};

NumberDisplay.displayName = 'NumberDisplay';