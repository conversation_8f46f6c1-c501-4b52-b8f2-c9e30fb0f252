import React from 'react';
import type { DisplayProps } from '../../../types';
import { useRenderTracker } from "../../../utils/debug";
import './index.less';

interface TextDisplayProps extends DisplayProps {
  ellipsis?: boolean;
  maxLength?: number;
}

export const TextDisplay: React.FC<TextDisplayProps> = ({ 
  value,
  style,
  ellipsis = true,
  maxLength
}) => {
  useRenderTracker('TextDisplay');
  const textRef = React.useRef<HTMLSpanElement>(null);
  const [isThreeLines, setIsThreeLines] = React.useState(false);

  React.useEffect(() => {
    if (textRef.current) {
      const height = textRef.current.offsetHeight;
      setIsThreeLines(height > 42); // 72px 是三行文本的高度
    }
  }, [value]);

  const displayValue = React.useMemo(() => {
    if (value == null) return '-';
    
    let text = String(value);
    if (maxLength && text.length > maxLength) {
      text = text.slice(0, maxLength) + '...';
    }
    return text;
  }, [value, maxLength]);

  return (
    <span 
      ref={textRef}
      className={`infinity-table-text-display ${ellipsis ? 'ellipsis' : ''} ${isThreeLines ? 'three-lines' : ''}`} 
      style={style}
      title={ellipsis ? String(value) : undefined}
    >
      {displayValue}
    </span>
  );
};

TextDisplay.displayName = 'TextDisplay'; 