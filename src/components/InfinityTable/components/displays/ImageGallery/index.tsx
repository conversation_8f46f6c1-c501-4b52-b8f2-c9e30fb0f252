import React from 'react';
import { Image, Space } from 'antd';
import { ClearButton } from '../../common/ClearButton';
import type { DisplayProps } from '../../../types';
import { isSupportWebp, formatWH } from './utils';
import './index.less';

// 添加工具函数
const formatImageUrl = (url: string, width: number, height: number) => {
  if (!url || typeof url !== 'string' || !url.startsWith('http') || url.includes('?')) {
    return url;
  }
  
  const size = formatWH(width, height);
  
  const process = [
    '?x-oss-process=image',
    'auto-orient,1',
    'quality,Q_80',
    'bright,-1',
    `resize,s_${size}`,  // 使用短边自适应缩放
  ];

  if (isSupportWebp) {
    process.push('format,webp');
  }

  return `${url}${process.join('/')}`;
};

interface ImagesDisplayProps extends DisplayProps {
  width?: number;
  height?: number;
  maxCount?: number;
  onClear?: () => void;
  overlayProps?: {
    showClear?: boolean;
  };
}

export const ImagesDisplay: React.FC<ImagesDisplayProps> = ({ 
  value = [],
  style,
  width = 48,
  height = 48,
  onClear,
  overlayProps
}) => {
  const DISPLAY_COUNT = 2;
  const images = Array.isArray(value) ? value : [];
  const displayImages = images.slice(0, DISPLAY_COUNT);
  const totalCount = images.length;

  return (
    <div className="infinity-table-images-display-wrapper">
      {(overlayProps?.showClear && onClear && images.length > 0) && (
        <ClearButton onClear={onClear} />
      )}
      <Space size={4} className="infinity-table-images-display" style={style}>
        {displayImages.map((url, index) => (
          <div 
            key={url} 
            className="image-wrapper"
            style={{ width, height, borderRadius: 2 }}
          >
            <Image
              src={formatImageUrl(url, width, height)}
              width={width}
              height={height}
              style={{ objectFit: 'cover', borderRadius: 2 }}
            />
            {index === DISPLAY_COUNT - 1 && totalCount > DISPLAY_COUNT && (
              <div className="image-overlay">
                <span>{totalCount}张</span>
              </div>
            )}
          </div>
        ))}
      </Space>
    </div>
  );
};

ImagesDisplay.displayName = 'ImagesDisplay'; 