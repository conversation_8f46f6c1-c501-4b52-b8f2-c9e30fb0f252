export let isSupportWebp = false;

function checkIsSupportWebp() {
  const webp = new Image();
  webp.src =
    'data:image/webp;base64,UklGRjIAAABXRUJQVlA4ICYAAACyAgCdASoBAAEALmk0mk0iIiIiIgBoSygABc6zbAAA/v56QAAAAA==';
  webp.onerror = function () {
    isSupportWebp = false;
  };
  webp.onload = function () {
    isSupportWebp = true;
  };
}
checkIsSupportWebp();

// 显示器像素比例
const pixelRatio = Math.ceil(window.devicePixelRatio || 1);

export function getNumber(value?: number | string) {
  if (typeof value === 'string') {
    return Number(value.replace('px', ''));
  }
  return value;
}

export function formatWH(width?: number | string, height?: number | string) {
  const w = getNumber(width);
  const h = getNumber(height);

  if (!w && !h) return 0;

  return Math.floor(pixelRatio * Math.max(w as number, h as number));
}