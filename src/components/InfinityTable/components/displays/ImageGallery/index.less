.infinity-table-images-display-wrapper {
  // position: relative;
  
  .infinity-table-images-display {
    display: flex;
    align-items: center;

    .image-wrapper {
      position: relative;
      flex-shrink: 0;
      overflow: hidden;
      background-color: #f5f5f5;

      [class$="-image"] {
        display: block;        
        img {
          display: block;
          object-fit: cover;
        }
      }

      .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
      }
    }
  }
}