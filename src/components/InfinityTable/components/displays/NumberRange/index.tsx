import React from 'react';
import type { DisplayProps } from '../../../types';
import './index.less';

interface ObjectRangeValue {
  min?: number;
  max?: number;
}

interface NumberRangeDisplayProps extends DisplayProps {
  unit?: string;
  precision?: number;
  formatter?: (value: number) => string;
}

export const NumberRangeDisplay: React.FC<NumberRangeDisplayProps> = ({ 
  value,
  style,
  unit = '',
  precision = 0,
  formatter
}) => {
  const formatNumber = React.useCallback((num?: number) => {
    if (num == null) return '-';
    return formatter 
      ? formatter(num)
      : num.toFixed(precision);
  }, [formatter, precision]);

  const renderUnit = React.useCallback((unitText: string) => {
    return unitText ? <span className="unit">{` ${unitText}`}</span> : '';
  }, []);

  const formattedValue = React.useMemo(() => {
    if (!value || typeof value !== 'object') return '-';
    
    const { min, max } = value as ObjectRangeValue;
    
    if (min == null && max == null) {
      return '';
    }
    
    if (min != null && max == null) {
      return <>{formatNumber(min)}{renderUnit(unit)}</>;
    }
    
    if (min == null && max != null) {
      return <>{formatNumber(max)}{renderUnit(unit)}</>;
    }
    
    if (min === max) {
      return <>{formatNumber(min)}{renderUnit(unit)}</>;
    }
    
    return <>{formatNumber(min)} ~ {formatNumber(max)}{renderUnit(unit)}</>;
  }, [value, formatNumber, unit, renderUnit]);

  return (
    <span className="infinity-table-number-range-display" style={style}>
      {formattedValue}
    </span>
  );
};

NumberRangeDisplay.displayName = 'NumberRangeDisplay'; 