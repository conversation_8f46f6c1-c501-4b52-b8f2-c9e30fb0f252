import React from 'react';
import type { CellComponent } from './types';
import { EDITOR_TYPES } from '@constants/index';

/**
 * 单元格显示组件导入
 * 按照字母顺序排列，便于维护和查找
 */
import { DateDisplay } from '../displays/DatePicker';
import { ImagesDisplay } from '../displays/ImageGallery';
import { MultiSelectDisplay } from '../displays/MultiSelect';
import { NumberDisplay } from '../displays/NumericLabel';
import { NumberRangeDisplay } from '../displays/NumberRange';
import { SearchSelectDisplay } from '../displays/SearchSelect';
import { SelectDisplay } from '../displays/Select';
import { TextDisplay } from '../displays/TextLabel';

/**
 * 单元格编辑器组件导入
 * 按照字母顺序排列，与显示组件保持对应关系
 */
import { DateEditor } from '../editors/DatePicker';
import { ImagesEditor } from '../editors/ImageUploader';
import { MultiSelectEditor } from '../editors/MultiSelect';
import { NumberEditor } from '../editors/NumericField';
import { NumberRangeEditor } from '../editors/NumberRange';
import { SearchSelectEditor } from '../editors/SearchSelect';
import { SelectEditor } from '../editors/Select';
import { TextEditor } from '../editors/TextField';

/**
 * 组件注册表类
 * 管理所有单元格的显示和编辑组件
 */
class ComponentRegistry {
  private readonly components = new Map<string, CellComponent>();

  /**
   * 注册一个新的组件类型
   * @param type - 组件类型标识符
   * @param component - 包含Display和Editor的组件对象
   */
  public register(type: string, component: CellComponent): void {
    if (!type || !component?.Display || !component?.Editor) {
      throw new Error('Invalid component registration parameters');
    }
    this.components.set(type, component);
  }

  /**
   * 获取指定类型的显示组件
   * @param type - 组件类型标识符
   * @returns 对应的显示组件，如果未找到则返回undefined
   */
  public getDisplayComponent(type: string): React.ComponentType<any> | undefined {
    return this.components.get(type)?.Display;
  }

  /**
   * 获取指定类型的编辑器组件
   * @param type - 组件类型标识符
   * @returns 对应的编辑器组件，如果未找到则返回undefined
   */
  public getEditorComponent(type: string): React.ComponentType<any> | undefined {
    return this.components.get(type)?.Editor;
  }
}

// 创建全局单例注册表实例
export const registry = new ComponentRegistry();

/**
 * 注册所有内置组件
 * 使用对象映射方式，便于维护和扩展
 */
const registerBuiltInComponents = (): void => {
  const builtInComponents: Record<string, CellComponent> = {
    [EDITOR_TYPES.TEXT]: { Display: TextDisplay, Editor: TextEditor },
    [EDITOR_TYPES.NUMBER]: { Display: NumberDisplay, Editor: NumberEditor },
    [EDITOR_TYPES.DATE]: { Display: DateDisplay, Editor: DateEditor },
    [EDITOR_TYPES.SELECT]: { Display: SelectDisplay, Editor: SelectEditor },
    [EDITOR_TYPES.SEARCH_SELECT]: { Display: SearchSelectDisplay, Editor: SearchSelectEditor },
    [EDITOR_TYPES.MULTI_SELECT]: { Display: MultiSelectDisplay, Editor: MultiSelectEditor },
    [EDITOR_TYPES.NUMBER_RANGE]: { Display: NumberRangeDisplay, Editor: NumberRangeEditor },
    [EDITOR_TYPES.IMAGES]: { Display: ImagesDisplay, Editor: ImagesEditor },
  };

  Object.entries(builtInComponents).forEach(([type, component]) => {
    registry.register(type, component);
  });
};

// 初始化注册内置组件
registerBuiltInComponents();

/**
 * 便捷方法：获取编辑器组件
 * @param type - 组件类型标识符
 */
export const getEditor = (type: string): React.ComponentType<any> | undefined => 
  registry.getEditorComponent(type);

/**
 * 便捷方法：获取显示组件
 * @param type - 组件类型标识符
 */
export const getDisplay = (type: string): React.ComponentType<any> | undefined => 
  registry.getDisplayComponent(type);

/**
 * 注册自定义组件的方法
 * @param type - 自定义组件类型标识符
 * @param displayComponent - 自定义显示组件
 * @param editorComponent - 自定义编辑器组件
 * @throws 当参数无效时抛出错误
 */
export const registerComponent = (
  type: string,
  displayComponent: React.ComponentType<any>,
  editorComponent: React.ComponentType<any>,
): void => {
  if (!type || !displayComponent || !editorComponent) {
    throw new Error('Invalid custom component registration parameters');
  }
  
  registry.register(type, {
    Display: displayComponent,
    Editor: editorComponent,
  });
}; 