import React, { memo, useMemo } from 'react';
import { getEditor } from '../componentRegistry';
import type { CellValue } from '../../../types/index';

/**
 * 可编辑单元格的属性接口定义
 * @interface EditorCellProps
 * @template T - 单元格值类型，默认为 CellValue
 */
interface EditorCellProps<T = CellValue> {
  /** 编辑器类型标识符 */
  type: string;
  
  /** 编辑器模式 */
  mode?: 'edit' | 'display';
  
  /** 
   * 单元格当前值
   * @default undefined
   */
  value?: T;
  
  /** 
   * 单元格值变更回调函数
   * @param value - 更新后的单元格值
   */
  onCellChange: (value: T) => void;
  
  /** 
   * 编辑器失焦回调函数
   * @optional
   */
  onBlur?: () => void;
  
  /** 
   * 编辑器组件特定的属性配置
   * @optional
   */
  editorProps?: Record<string, unknown>;
  
  /** 
   * 自定义样式对象
   * @optional
   */
  style?: React.CSSProperties;
  
  /** 
   * 自定义 CSS 类名
   * @optional
   */
  className?: string;
  
  record?: Record<string, unknown>;
  onSave?: () => void;
  onCancel?: () => void;
}

/**
 * 可编辑单元格组件
 * 
 * @component
 * @description 根据传入的 type 动态渲染对应的编辑器组件，支持自定义编辑器注册
 * 
 * @example
 * ```tsx
 * <EditorCell
 *   type="text"
 *   value="示例文本"
 *   onCellChange={(value) => console.log('值已更新:', value)}
 *   editorProps={{ placeholder: '请输入' }}
 * />
 * ```
 */
export const EditorCell = memo(<T extends CellValue>({
  type,
  value,
  onCellChange,
  editorProps = {},
  ...props
}: EditorCellProps<T>) => {
  // 获取对应类型的编辑器组件
  const EditorComponent = useMemo(() => getEditor(type), [type]);

  // 编辑器组件不存在时的错误处理
  if (!EditorComponent) {
    if (process.env.NODE_ENV !== 'production') {
      console.warn(
        `[EditorCell] 未找到类型为 "${type}" 的编辑器组件，请检查是否已正确注册`
      );
    }
    return null;
  }
  
  return (
    <EditorComponent
      value={value}
      onCellChange={onCellChange}
      {...editorProps}
      {...props}
    />
  );
}) as <T extends CellValue>(props: EditorCellProps<T>) => React.ReactElement | null;


// 导出类型定义，方便其他组件使用
export type { EditorCellProps };