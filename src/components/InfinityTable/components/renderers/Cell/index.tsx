import React, { memo } from "react";
import { getDisplay } from "../componentRegistry";
import { isSafari } from '../../../utils/browser';
import { TABLE_LAYOUT_CONFIG } from "@constants/index";
import type { CellValue, EditorConfig, ColumnType } from "@types";
import "./index.less";

/**
 * 单元格弹出层属性接口
 * @interface OverlayProps
 * @property {React.CSSProperties} [style] - 自定义样式
 * @property {string} [className] - 自定义类名
 * @property {Function} [onClear] - 清除回调函数
 */
interface OverlayProps {
  style?: React.CSSProperties;
  className?: string;
  onClear?: (params: {
    record: Record<string, unknown>;
    onChange: (value: CellValue) => void;
  }) => void;
}

/**
 * 展示单元格属性接口
 * @interface DisplayCellProps
 * @extends {Record<string, unknown>}
 */
interface DisplayCellProps extends Record<string, unknown> {
  /** 编辑器模式 */
  mode?: 'edit' | 'display';
  /** 单元格值 */
  value: CellValue;
  /** 行数据记录 */
  record: Record<string, unknown>;
  /** 行索引 */
  index: number;
  /** 列配置 */
  column: ColumnType;
  /** 单元格类型 */
  type: string;
  /** 编辑器配置 */
  editor?: EditorConfig & {
    overlayProps?: OverlayProps;
  };
  /** 自定义渲染函数 */
  displayRender?: (props: {
    value: CellValue;
    record: Record<string, unknown>;
    index: number;
    column: ColumnType;
  }) => React.ReactNode;
  /** 清除回调 */
  onClear?: () => void;
}

/**
 * 获取基础单元格样式
 * @param {React.CSSProperties} [overlayStyle] - 覆盖样式
 * @returns {React.CSSProperties} 合并后的样式对象
 */
const getBaseCellStyle = (
  overlayStyle?: React.CSSProperties
): React.CSSProperties => {
  const baseStyle = {
    height: `${TABLE_LAYOUT_CONFIG.CELL_HEIGHT}px`,
    overflow: "hidden", 
    padding: "12px 12px",
    width: "100%",
    position: "relative",
    ...overlayStyle,
  };

  if (isSafari()) {
    baseStyle.WebkitUserSelect = "none";
  }

  return baseStyle;
};

/**
 * 展示单元格组件
 * @component DisplayCell
 * @description 用于渲染表格单元格的内容，支持自定义渲染和默认组件渲染
 */
export const DisplayCell: React.FC<DisplayCellProps> = memo(
  ({
    value,
    record,
    index,
    column,
    type,
    editor,
    displayRender,
    onClear,
    ...props
  }) => {
    // 获取对应类型的显示组件
    const Component = getDisplay(type);
    const overlayProps = editor?.overlayProps || {};

    // 构建样式和类名
    const cellWrapperStyle = getBaseCellStyle(overlayProps.style);
    const cellClassName = [
      "infinity-table-display-cell",
      overlayProps.className,
    ]
      .filter(Boolean)
      .join(" ");

    // 使用自定义渲染函数
    if (displayRender) {
      return (
        <div
          className={cellClassName}
          style={cellWrapperStyle}
          data-cell-type={type}
          data-type="custom-display-render"
        >
          {displayRender({
            value,
            record,
            index,
            column,
          })}
        </div>
      );
    }

    // 组件不存在时给出警告
    if (!Component) {
      console.warn(
        `[InfinityTable] 未找到类型为 "${type}" 的显示组件，请检查组件注册表`
      );
      return null;
    }

    return (
      <div
        className={cellClassName}
        style={cellWrapperStyle}
        data-cell-type={type}
      >
        <Component
          value={value}
          onClear={onClear}
          overlayProps={overlayProps}
          record={record}
          column={column}
          {...props}
        />
      </div>
    );
  }
);

// 设置组件显示名称，方便调试
DisplayCell.displayName = "DisplayCell";
