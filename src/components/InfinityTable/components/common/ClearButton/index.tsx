/**
 * @file ClearButton Component
 * @description 表格通用清除按钮组件
 * @review 2024/12/19
 */

import React, { memo, useCallback } from 'react';
import type { FC, CSSProperties, MouseEvent } from 'react';
import classNames from 'classnames';
import './index.less';

interface ClearButtonProps {
  /** 点击清除按钮的回调函数 */
  onClear: (e: MouseEvent) => void;
  /** 自定义样式对象 */
  style?: CSSProperties;
  /** 自定义类名 */
  className?: string;
}

// 常量配置区
const CLEAR_BUTTON_CONFIG = {
  /** 清除图标URL地址 - 建议后续迁移到统一的资源配置文件中 */
  ICON_URL: 'https://s.xinc818.com/files/webcim4tarhdvztp4m3/remove_icon.png',
  /** 按钮容器默认样式 */
  CONTAINER_STYLE: {
    position: 'absolute' as const,
    right: 0,
    top: 0,
    width: '40px',
    height: '70px',
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    zIndex: 1,
    backgroundImage: 'linear-gradient(-89deg, #FFFFFF 50%, #ffffff00 100%)',
  },
  /** 图标默认样式 */
  ICON_STYLE: {
    position: 'absolute' as const,
    right: '6px',
    top: '6px',
    cursor: 'pointer',
  },
} as const;

/**
 * ClearButton 组件 - 用于显示一个带渐变背景的清除按钮
 * 
 * @component
 * @example
 * ```tsx
 * <ClearButton 
 *   onClear={() => handleClear()} 
 *   className="custom-clear"
 *   style={{ width: '50px' }}
 * />
 * ```
 */
export const ClearButton: FC<ClearButtonProps> = memo(({
  onClear,
  style,
  className,
}) => {
  // 处理清除按钮点击事件，阻止事件冒泡
  const handleClear = useCallback((e: MouseEvent): void => {
    e.stopPropagation();
    onClear(e);
  }, [onClear]);

  return (
    <div
      className={classNames('infinity-table-clear-button', className)}
      style={{
        ...CLEAR_BUTTON_CONFIG.CONTAINER_STYLE,
        ...style,
      }}
      role="button"
      aria-label="清除"
    >
      <img 
        src={CLEAR_BUTTON_CONFIG.ICON_URL}
        width={16}
        height={16}
        onClick={handleClear}
        className="infinity-table-clear-icon"
        alt="清除"
        style={CLEAR_BUTTON_CONFIG.ICON_STYLE}
      />
    </div>
  );
});

// 设置组件显示名称，用于 React DevTools
ClearButton.displayName = 'ClearButton';

export default ClearButton; 