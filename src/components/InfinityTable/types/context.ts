/**
 * InfinityTable Context 相关类型定义
 * 包含表格上下文所需的所有类型定义,包括:
 * - 位置信息类型
 * - 选中单元格类型
 * - 编辑单元格类型
 * - Context 状态和操作类型
 */
import type { ColumnType, CellValue, DebugConfig } from './index';

/**
 * 单元格位置信息接口
 * 用于定位单元格在视图中的具体位置和尺寸
 */
export interface Position {
  top: number;      // 单元格顶部距离
  left: number;     // 单元格左侧距离
  width: number;    // 单元格宽度
  height: number;   // 单元格高度
}

/**
 * 编辑单元格接口
 * 记录当前正在编辑的单元格的完整信息
 * @template T 数据类型
 */
export interface EditingCell<T> {
  rowKey: string;           // 行唯一标识
  dataIndex: string;        // 列数据索引
  rowIndex: number;         // 行索引
  value: CellValue;         // 当前编辑值
  initialValue: CellValue;  // 初始值
  record: T;                // 行数据
  cellTarget: HTMLElement;  // 单元格 DOM 元素
  column: ColumnType<T>;    // 列配置
  editor?: {                // 编辑器配置
    type: string;           // 编辑器类型
    props?: Record<string, unknown>;  // 编辑器属性
    saveOnBlur?: boolean;  // 添加这个字段
    editorRender?: React.ComponentType<any>;
    displayRender?: (props: {
      value: CellValue;
      record: Record<string, unknown>;
      index: number;
      column: ColumnType<T>;
    }) => React.ReactNode;
  };
  position: Position;       // 位置信息
  keepOpen?: boolean;      // 是否保持编辑器打开
  isFirstEdit?: boolean;   // 是否首次编辑
}

/**
 * Context 状态接口
 * 定义表格上下文中需要维护的状态
 * @template T 数据类型
 */
export interface TableContextState<T> {
  dataSource: T[];                 // 表格数据源
  initialCellValue: CellValue | null;  // 单元格初始值
  debug?: any;                 // 是否开启调试模式
  prefixCls?: string;             // CSS 类名前缀
  editingCellsMap: Map<string, EditingCell<T>>;
  updateTrigger: number;
  generateCellKey: (rowIndex: number, columnKey: string) => string;
}

/**
 * Context actions 接口
 * 定义表格上下文中可以执行的操作
 * @template T 数据类型
 */
export interface TableContextActions<T> {
  /**
   * 开始编辑单元格
   * @param params 包含来源和目标单元格信息
   */
  startEdit: (params: StartEditOptions) => void;
  
  /**
   * 设置单元格初始值
   * @param value 初始值
   */
  setInitialCellValue: (value: CellValue | null) => void;
}

/**
 * Context 值类型
 * 组合状态和操作接口
 * @template T 数据类型
 */
export type TableContextValue<T> = TableContextState<T> & TableContextActions<T>;

/**
 * Provider Props 接口
 * Context Provider 组件的属性定义
 * @template T 数据类型
 */
export interface TableContextProviderProps<T> {
  children: React.ReactNode;     // 子组件
  dataSource: T[];              // 数据源
  debug?: DebugConfig;          // 调试配置
  prefixCls?: string;           // CSS 类名前缀
}

/**
 * StartEditOptions 接口
 * 开始编辑操作的选项配置
 */
export interface StartEditOptions {
  from: string;
  cell: EditingCell | null;
  currentCell?: {
    rowIndex: number;
    columnKey: string;
  };
} 