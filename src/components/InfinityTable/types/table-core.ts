/**
 * InfinityTable 核心类型定义文件
 * 包含表格核心功能所需的类型定义,包括:
 * - 禁用规则
 * - 权限配置
 * - 编辑锁定
 * - 核心组件属性
 */
import type { TablePaginationConfig } from "antd/lib/table";
import type { ColumnType, CellValue } from "./index";
import type { ReactNode } from "react";

/**
 * 单元格禁用规则接口
 * 用于配置单元格级别的禁用逻辑
 * @template T 数据类型
 */
export interface DisableRule<T> {
  field: string;                  // 需要禁用的字段名
  match: (record: T) => boolean;  // 禁用条件判断函数
}

/**
 * 权限配置接口
 * 用于配置表格的权限控制
 * @template T 数据类型
 */
export interface PermissionConfig<T> {
  rules?: (string | DisableRule<T>)[];  // 禁用规则列表,支持字段名或规则对象
  tooltip?: {                           // 禁用提示配置
    show?: boolean;                     // 是否显示提示
    message?: string;                   // 提示信息
    style?: React.CSSProperties;        // 提示样式
  };
}

/**
 * 编辑锁定配置接口
 * 用于配置行级别的编辑锁定
 */
export interface EditingLockConfig {
  editingRows?: string[];              // 被锁定的行 key 列表
  tooltip?: {                          // 锁定提示配置
    show?: boolean;                    // 是否显示提示
    message?: string;                  // 提示信息
    style?: React.CSSProperties;       // 提示样式
  };
}

/**
 * 表格核心组件属性接口
 * 定义表格组件的核心属性
 * @template T 数据类型,必须是一个对象类型
 */
export interface TableCoreProps<T extends Record<string, unknown>> {
  columns: ColumnType<T>[];            // 列配置
  dataSource: T[];                     // 数据源
  height: number;                      // 表格高度
  width: number | string;              // 表格宽度
  disableControl?: PermissionConfig<T>; // 禁用控制配置
  editingLock?: EditingLockConfig;     // 编辑锁定配置
  pagination?: TablePaginationConfig;   // 分页配置
  rowKey?: string | ((record: T) => string);  // 行唯一标识
  rowSelection?: {                     // 行选择配置
    selectedRowKeys: string[];         // 已选择的行 key 列表
    onChange: (selectedRowKeys: string[], selectedRows: T[]) => void;  // 选择变化回调
  };
  infinityLoad?: {                     // 无限加载配置
    enabled: boolean;                  // 是否启用无限加载
  };
  onEditStateChange?: (params: {       // 编辑状态变化回调
    value: CellValue;                  // 编辑的值
    record: T;                         // 行数据
    column: ColumnType<T>;             // 列配置
    type: "unchanged" | "invalid" | "cancelled" | "success";  // 编辑状态
  }) => Promise<void> | void;
}

/**
 * 编辑弹窗包装组件属性接口
 * 用于编辑弹窗的配置
 */
export interface EditorPopupWrapperProps {
  onEditStateChange?: TableCoreProps<any>["onEditStateChange"];  // 编辑状态变化回调
}