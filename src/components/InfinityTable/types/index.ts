/**
 * InfinityTable 类型定义文件
 * 包含组件所需的所有类型定义,包括:
 * - 组件属性类型
 * - 编辑器类型
 * - 渲染器类型
 * - 验证器类型
 * - 工具类型
 */

import type { TableProps, TablePaginationConfig } from 'antd';
import type { ColumnType as AntdColumnType } from 'antd/lib/table';
import type { Key } from 'react';
import { ColumnType } from 'antd/es/table';

/**
 * 扩展的表格列类型
 * @template T 数据类型
 * @extends ColumnType<T> antd 表格列类型
 */
export interface TableColumnType<T = unknown> extends ColumnType<T> {
  show?: boolean;        // 是否显示该列
  plainTitle?: string;   // 列标题的纯文本,用于搜索和排序
  unremovable?: boolean; // 标记该列是否不可删除(必须显示)
}

/**
 * 列项配置
 * @template T 数据类型
 * @extends TableColumnType<T> 扩展的表格列类型
 */
export interface ColumnItem<T = unknown> extends TableColumnType<T> {
  key: string;      // 列的唯一标识
  enabled: boolean; // 是否启用该列
}

/**
 * 可拖拽项的类型定义
 */
export interface DraggableItem {
  index: number;         // 拖拽项的索引
  column: ColumnItem;    // 列配置信息
}

/**
 * 单元格值的基础类型
 * 支持字符串、数字、布尔值、null 和 undefined
 */
export type CellValue = string | number | boolean | null | undefined;

/**
 * 编辑中的单元格信息
 * @template T 数据类型
 */
export interface EditingCell<T> {
  rowKey: string;           // 行的唯一标识
  dataIndex: string;        // 列的数据索引
  value: CellValue;         // 当前值
  initialValue: CellValue;  // 初始值
  record: T;                // 行数据
  column: ColumnType<T>;    // 列配置
  editor: EditorConfig<T>;  // 编辑器配置
  position: {               // 单元格位置信息
    top: number;
    left: number;
    width: number;
    height: number;
  };
  cellTarget: HTMLElement;  // 单元格DOM元素
}

/**
 * 列配置类型
 * @template T 数据类型
 */
export interface ColumnType<T = any> {
  title: string;                // 列标题
  dataIndex: string;           // 数据字段名
  key: string;                 // 列的唯一标识
  width?: number;              // 列宽
  fixed?: 'left' | 'right';    // 列固定方向
  editor?: EditorConfig;       // 编辑器配置
  render?: (value: any, record: T, index: number) => React.ReactNode;  // 自定义渲染函数
  cellRender?: (props: {      // 单元格渲染函数
    value: CellValue;
    record: T;
    index: number;
    isEditing: boolean;
    column: ColumnType<T>;
  }) => React.ReactNode;
}

/**
 * 表格组件属性
 * @template T 数据类型
 * @extends TableProps<T> antd 表格属性
 */
export interface InfinityTableProps<T> extends TableProps<T> {
  prefixCls?: string;          // 组件 CSS 类名前缀
  columns: ColumnType<T>[];    // 列配置
  dataSource: T[];            // 数据源
  width?: number | string;     // 表格宽度
  height?: number | string;   // 表格高度
  onCellSelect?: (cell: SelectedCell | null) => void;  // 单元格选中回调
  rowSelection?: {           // 行选择配置
    selectedRowKeys?: Key[];
    onChange: (selectedRowKeys: Key[], selectedRows: T[]) => void;
  };
  rules?: string[];             // 禁用列配置
  columnManager?: ColumnManagerConfig;  // 列管理器配置
  debug?: DebugConfig;          // 调试配置
  onEditStateChange?: (params: {       // 编辑状态变化回调
    value: CellValue;
    record: T;
    column: ColumnType<T>;
    type: 'unchanged' | 'invalid' | 'cancelled' |'success';
  }) => Promise<void> | void;
}

/**
 * 编辑器属性
 * @template T 值类型
 * @template R 行数据类型
 */
export interface EditorProps<T = CellValue, R = Record<string, any>> {
  value: T;                    // 当前值
  onChange: (value: T) => void;  // 值变化回调
  onSave?: () => void;          // 保存回调
  onCancel?: () => void;        // 取消回调
  onClose: () => void;          // 关闭回调
  style?: React.CSSProperties;   // 样式
  record?: R;                    // 行数据
  [key: string]: any;           // 其他属性
}

/**
 * 渲染器属性
 * @template T 值类型
 */
export interface RendererProps<T = CellValue> {
  value: T;                     // 当前值
  style?: React.CSSProperties;  // 样式
  record: Record<string, T>;    // 行数据
  index: number;                // 行索引
  column: ColumnType<T>;        // 列配置
}

/**
 * 单元格格式规则
 * @template T 值类型
 */
export interface Rule<T = CellValue> {
  condition: (value: T) => boolean;  // 条件函数
  style: React.CSSProperties;        // 满足条件时应用的样式
}

/**
 * 验证器函数类型
 * 返回 true 表示验证通过,返回字符串表示验证失败信息
 */
export type ValidatorFn = (value: CellValue) => boolean | string;


/**
 * 选中单元格信息
 */
export interface SelectedCell {
  rowKey: string;     // 行标识
  dataIndex: string;  // 列标识
}

/**
 * 表格上下文类型
 * @template T 数据类型
 */
export interface TableContextType<T extends Record<string, unknown>> {
  editingCell: EditingCell<T> | null;   // 当前编辑的单元格
  selectedCell: SelectedCell | null;     // 当前选中的单元格
  startEdit: (cell: EditingCell<T>) => void;  // 开始编辑
  finishEdit: () => void;                     // 结束编辑
  updateCell: (params: {                      // 更新单元格
    rowKey: string;
    dataIndex: string;
    value: CellValue;
    record: T;
    column: ColumnType<T>;
    source: 'builtin' | 'custom';
  }) => void;
  setSelectedCell?: (cell: SelectedCell | null) => void;  // 设置选中单元格
  getSelectedCell?: () => SelectedCell | null;            // 获取选中单元格
  getEditingCell?: () => EditingCell<T> | null;          // 获取编辑中单元格
  isEditing?: (rowKey: string, dataIndex: string) => boolean;  // 判断单元格是否在编辑
}

/**
 * 编辑器配置
 * @template T 数据类型
 */
export interface EditorConfig<T extends Record<string, unknown> = Record<string, unknown>> {
  type: string;                 // 编辑器类型
  props?: Record<string, unknown>;  // 编辑器属性
  editorRender?: React.ComponentType<EditorComponentProps>;  // 自定义编辑器渲染
  validators?: {                // 验证器配置
    required?: boolean | {
      value: boolean;
      message: string;
    };
    custom?: (value: CellValue, record: T) => Promise<string | void> | string | void;
  };
  displayRender?: (props: {    // 显示渲染器
    value: CellValue;
    record: T;
    index: number;
    column: ColumnType<T>;
  }) => React.ReactNode;
  beforeEdit?: (record: T) => Promise<boolean> | boolean;  // 编辑前钩子
  onEditComplete?: (           // 编辑完成回调
    value: CellValue, 
    record: T,
    customData?: any          // 自定义数据
  ) => Promise<void> | void;
  overlayProps?: {            // 浮层属性
    onClear?: (params: { 
      record: T; 
      onChange: (value: CellValue) => void 
    }) => void;
  };
  saveOnBlur?: boolean;       // 是否在失焦时保存
}

/**
 * 单元格属性
 */
export interface CellProps {
  value: any;                 // 单元格值
  render?: (value: any, record: any, index: number) => React.ReactNode;  // 渲染函数
  record: any;                // 行数据
  index: number;              // 行索引
  editor?: EditorConfig<any>; // 编辑器配置
}

/**
 * 显示组件属性
 * @template T 值类型
 */
export interface DisplayProps<T = CellValue> {
  value: T;                   // 显示值
  style?: React.CSSProperties;  // 样式
  [key: string]: any;          // 其他属性
}

/**
 * 列管理器属性
 */
export interface ColumnManagerProps {
  onChange: (columns: ColumnType<any>[]) => void;  // 列变化回调
  defaultColumns?: ColumnType<any>[];              // 默认列配置
}

/**
 * 调试配置
 */
export interface DebugConfig {
  enabled?: boolean;          // 是否启用调试
}

/**
 * 列管理器配置
 */
export interface ColumnManagerConfig {
  onChange: (columnKeys: string[]) => void;  // 列变化回调
}

/**
 * 扩展列类型
 * @template T 数据类型
 */
export interface ExtendedColumnType<T> extends ColumnType<T> {
  show?: boolean;            // 是否显示
  dataIndex: string;         // 数据字段名
}


/**
 * 提示框状态
 */
export interface TooltipState {
  visible: boolean;          // 是否显示
  isPermissionTip: boolean;  // 是否为权限提示
  message?: string;          // 提示信息
  position: {                // 提示框位置
    x: number;
    y: number;
  };
}

/**
 * 权限配置
 * @template T 数据类型
 */
export interface PermissionConfig<T> {
  rules?: (string | DisableRule<T>)[];  // 禁用规则
  tooltip?: {                           // 提示配置
    show?: boolean;
    message?: string;
    style?: React.CSSProperties;
  };
}

/**
 * 编辑锁定配置
 */
export interface EditingLockConfig {
  editingRows?: string[];    // 锁定的行
  tooltip?: {                // 提示配置
    show?: boolean;
    message?: string;
    style?: React.CSSProperties;
  };
}

/**
 * 编辑器组件属性
 * @template T 值类型
 */
export interface EditorComponentProps<T = CellValue> extends EditorProps<T> {
  mode: 'edit' | 'display';  // 编辑器模式
  type?: string;             // 编辑器类型
  onSave?: () => void;      // 保存回调
  onCancel?: () => void;    // 取消回调
}

/**
 * 搜索选择器选项
 */
export interface SearchOption {
  value: string;            // 选项值
  label: string;            // 选项标签
}

/**
 * 搜索选择器属性
 */
export interface SearchSelectProps {
  value?: string;           // 当前值
  onChange?: (value: string) => void;  // 值变化回调
  placeholder?: string;     // 占位文本
  onSearch: (params: SearchParams) => Promise<SearchOption[]>;  // 搜索回调
  initialOptions?: SearchOption[];  // 初始选项
  defaultOpen?: boolean;    // 是否默认展开
  loading?: boolean;        // 是否加载中
}

/**
 * 搜索参数
 */
export interface SearchParams {
  keyword: string;          // 搜索关键词
  field: string;           // 搜索字段
  record: Record<string, unknown>;  // 行数据
}

/**
 * 编辑锁定行配置
 */
interface EditingRowConfig {
  id: string;              // 行标识
  message?: string;        // 锁定提示
}

/**
 * 编辑锁定配置
 */
interface EditingLockConfig {
  editingRows: EditingRowConfig[];  // 锁定的行配置
  tooltip: {                        // 提示配置
    show: boolean;
    message: string;
  };
}

