/**
 * @file Column Manager Component
 * @description 表格列管理器组件，用于管理表格列的显示、隐藏和排序
 * @review 2024/12/19
 */

import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { Modal, Button, Empty, Image, Tooltip } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import type { TableColumnType, ColumnItem, DraggableItem } from '../../types';
import { useTableDebug } from '@utils/debug';
import './index.less';

/**
 * 可拖拽标签组件属性
 */
interface DraggableTagProps {
  title: string;
  onClose: () => void;
  unremovable?: boolean;
}

/**
 * 可拖拽标签组件
 * 用于已启用列区域的标签显示
 */
const DraggableTag: React.FC<DraggableTagProps> = ({ 
  title, 
  onClose,
  unremovable 
}) => (
  <div className="infinity-table-column-manager-tag infinity-table-column-manager-tag-draggable">
    <span>{title}</span>
    {!unremovable && (
      <Image
        src="https://s.xinc818.com/files/webcim4tkc1x5ry9500/remove.png"
        preview={false}
        width={16}
        height={16}
        className="infinity-table-column-manager-tag-close-icon"
        onClick={(e) => {
          e.stopPropagation();
          onClose();
        }}
      />
    )}
  </div>
);

/**
 * 可选择标签组件属性
 */
interface SelectableTagProps {
  title: string;
  onAdd: () => void;
}

/**
 * 可选择标签组件
 * 用于未启用列区域的标签显示
 */
const SelectableTag: React.FC<SelectableTagProps> = ({ 
  title, 
  onAdd
}) => (
  <div className="infinity-table-column-manager-tag">
    <span>{title}</span>
    <Image
      src="https://s.xinc818.com/files/webcim4tkotv25xzbny/add.png"
      preview={false}
      width={16}
      height={16}
      className="infinity-table-column-manager-tag-add-icon"
      onClick={(e) => {
        e.stopPropagation();
        onAdd();
      }}
    />
  </div>
);

/**
 * 拖拽包装器组件属性
 */
interface DraggableWrapperProps<T> {
  column: ColumnItem<T>;
  index: number;
  moveColumn: (dragIndex: number, hoverIndex: number) => void;
  onClose: () => void;
}

/**
 * 拖拽包装器组件
 * 现列拖拽排序功能
 */
const DraggableWrapper = <T extends Record<string, unknown>>({
  column,
  index,
  moveColumn,
  onClose
}: DraggableWrapperProps<T>) => {
  const ref = useRef<HTMLDivElement>(null);
  
  const [{ isDragging }, drag] = useDrag<DraggableItem, unknown, { isDragging: boolean }>({
    type: 'draggable-column',
    item: () => ({ 
      index, 
      column: column as ColumnItem<unknown> // 类型断言解决类型不兼容问题
    }),
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: 'draggable-column',
    hover: (item: DraggableItem) => {
      if (!ref.current) return;
      const dragIndex = item.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) return;
      moveColumn(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  drag(drop(ref));

  return (
    <div 
      ref={ref} 
      style={{ opacity: isDragging ? 0.3 : 1 }}
      className="infinity-table-column-manager-draggable-wrapper"
    >
      <DraggableTag
        title={extractTitle(column.title)}
        onClose={onClose}
        unremovable={column.unremovable}
      />
    </div>
  );
};

/**
 * 添加辅助函数来提取真实的标题文本
 */
const extractTitle = (titleNode: React.ReactNode): string => {
  //console.log('titleNode', titleNode);
  if (typeof titleNode === 'string') {
    return titleNode;
  }
  // 如果是 TitleRender 组件，提取 name 属性
  if (React.isValidElement(titleNode) && 'props' in titleNode) {
    return titleNode.props.name || '';
  }
  return '';
};

/**
 * 列管理器组件属性
 */
interface ColumnManagerProps<T = unknown> {
  columns: TableColumnType<T>[];
  defaultColumns?: TableColumnType<T>[];
  onChange: (columnKeys: string[]) => void;
}

/**
 * 列管理器组件
 * 管理表格列的显示、隐藏和排序
 */
export function ColumnManager<T = unknown>({ 
  columns = [],
  defaultColumns = [],
  onChange 
}: ColumnManagerProps<T>) {
  const debug = useTableDebug();
  const [visible, setVisible] = useState(false);
  
  // 添加状态管理
  const [enabledItems, setEnabledItems] = useState<ColumnItem<T>[]>([]);
  const [disabledItems, setDisabledItems] = useState<ColumnItem<T>[]>([]);
  
  // 过滤出可以管理的列（非固定列）
  const managableColumns = useMemo(() => 
    columns.filter(col => !col.fixed)
  , [columns]);

  // 初始化列状态
  const initializeColumns = useCallback(() => {
    const enabled: ColumnItem<T>[] = [];
    const disabled: ColumnItem<T>[] = [];

    // 使用 managableColumns 作为初始数据源
    managableColumns.forEach(col => {
      const columnItem: ColumnItem<T> = {
        ...col,
        key: col.dataIndex as string,
        enabled: col.show !== false,
      };

      (columnItem.enabled ? enabled : disabled).push(columnItem);
    });

    return { enabled, disabled };
  }, [managableColumns]);

  // 每次打开弹窗时初始化列状态
  useEffect(() => {
    if (visible) {
      const { enabled, disabled } = initializeColumns();
      setEnabledItems(enabled);
      setDisabledItems(disabled);
    }
  }, [visible, initializeColumns]);

  // 重置为默认设置
  const resetToDefault = useCallback(() => {
    if (!defaultColumns?.length) {
      const enabled: ColumnItem<T>[] = managableColumns.map(col => ({
        ...col,
        key: col.dataIndex as string,
        enabled: true
      }));
      setEnabledItems(enabled);
      setDisabledItems([]);
      return;
    }

    // 如果有 defaultColumns，则使用它作为数据源
    const enabled: ColumnItem<T>[] = [];
    const disabled: ColumnItem<T>[] = [];

    defaultColumns.forEach(col => {
      const columnItem: ColumnItem<T> = {
        ...col,
        key: col.dataIndex as string,
        enabled: col.show !== false,
      };

      (columnItem.enabled ? enabled : disabled).push(columnItem);
    });

    setEnabledItems(enabled);
    setDisabledItems(disabled);
  }, [defaultColumns, managableColumns]);

  // 处理列拖拽排序
  const moveColumn = useCallback((dragIndex: number, hoverIndex: number) => {
    setEnabledItems((prevColumns) =>
      update(prevColumns, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, prevColumns[dragIndex]],
        ],
      })
    );
  }, []);

  // 切换列的启用状态
  const toggleColumn = useCallback((item: ColumnItem<T>) => {
    if (item.enabled) {
      setEnabledItems(prev => prev.filter(col => col.key !== item.key));
      setDisabledItems(prev => [...prev, { ...item, enabled: false }]);
    } else {
      setDisabledItems(prev => prev.filter(col => col.key !== item.key));
      setEnabledItems(prev => [...prev, { ...item, enabled: true }]);
    }
  }, []);

  // 启用所有列(debug功能)
  const enableAllColumns = useCallback(() => {
    setEnabledItems(prev => [
      ...prev,
      ...disabledItems.map(item => ({ ...item, enabled: true }))
    ]);
    setDisabledItems([]);
  }, [disabledItems]);

  // 保存列设置
  const handleSave = useCallback(() => {
    const fixedColumns = columns.filter(col => col.fixed);
    const managedColumns = enabledItems.map(item => item.dataIndex as string);
    const fixedColumnKeys = fixedColumns.map(col => col.dataIndex as string);
    
    const newColumnKeys = [...managedColumns, ...fixedColumnKeys];
    
    onChange(newColumnKeys);
    setVisible(false);
  }, [columns, enabledItems, onChange]);

  return (
    <>
      <Tooltip 
        title="列设置" 
        placement="topRight"
        overlayClassName="infinity-table-column-manager-tooltip"
      >
        <Button
          type="text"
          icon={<SettingOutlined style={{ color: '#8E97A2', fontSize: '14px' }} />}
          onClick={() => setVisible(true)}
          className="infinity-table-column-manager-settings-button"
        />
      </Tooltip>
      <Modal
        title="自定义显示列"
        open={visible}
        width={800}
        onCancel={() => setVisible(false)}
        footer={
          <div className="infinity-table-column-manager-footer">
            <div className="infinity-table-column-manager-footer-left">
              <Button 
                className="infinity-table-column-manager-default-btn"
                onClick={resetToDefault}
              >
                恢复默认
              </Button>
              {debug?.enabled && (
                <Button 
                  className="infinity-table-column-manager-debug-btn"
                  onClick={enableAllColumns}
                >
                  一键设置
                </Button>
              )}
            </div>
            <div className="infinity-table-column-manager-footer-right">
              <Button 
                onClick={handleSave}
                className="infinity-table-column-manager-confirm-btn"
              >
                确定
              </Button>
              <Button 
                onClick={() => setVisible(false)} 
                className="infinity-table-column-manager-cancel-btn"
              >
                取消
              </Button>
            </div>
          </div>
        }
        className="infinity-table-column-manager-modal"
      >
        <div className="infinity-table-column-manager">
          <div className="infinity-table-column-manager-enabled-columns">
            <div className="infinity-table-column-manager-header">拖动区块调整显示顺序</div>
            <DndProvider backend={HTML5Backend}>
              <div className="infinity-table-column-manager-draggable-container">
                {enabledItems.map((column, index) => (
                  <DraggableWrapper
                    key={column.key}
                    column={column}
                    index={index}
                    moveColumn={moveColumn}
                    onClose={() => toggleColumn(column)}
                  />
                ))}
              </div>
            </DndProvider>
          </div>

          <div className="infinity-table-column-manager-disabled-columns">
            <div className="infinity-table-column-manager-header">选择显示列</div>
            <div className="infinity-table-column-manager-static-container">
              {disabledItems.length > 0 ? (
                disabledItems.map(column => (
                  <div key={column.key} className="infinity-table-column-manager-draggable-wrapper">
                    <SelectableTag
                      title={extractTitle(column.title)}
                      onAdd={() => toggleColumn(column)}
                    />
                  </div>
                ))
              ) : (
                <Empty
                  image="https://s.xinc818.com/files/webcilk3f2akdfsqng6/<EMAIL>"
                  imageStyle={{ width: '100px', height: '100px' }}
                  description="暂无可添加列"
                  style={{ margin: '20px auto', color: '#86909C' }}
                />
              )}
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
}