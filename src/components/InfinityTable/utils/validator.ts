import { message } from 'antd';
import type { CellValue } from '../types';

/**
 * 单元格验证结果
 * @description 定义验证器返回的结果结构
 */
export interface ValidateResult {
  /** 验证是否通过 */
  valid: boolean;
  /** 验证失败时的提示信息 */
  message?: string;
}

/**
 * 验证器配置选项
 * @description 定义验证规则的配置结构
 */
export interface ValidateOptions {
  /** 必填校验配置 */
  required?: {
    /** 是否必填 */
    value: boolean;
    /** 必填验证失败时的提示信息 */
    message: string;
  };
  /** 
   * 自定义验证函数
   * @param value - 待验证的单元格值
   * @param record - 当前行数据记录
   * @returns 返回验证结果，支持同步和异步
   */
  custom?: (
    value: CellValue, 
    record?: Record<string, any>
  ) => Promise<ValidateResult> | ValidateResult;
}

/**
 * 检查值是否为空
 * @description 统一判断各种数据类型的空值情况
 * @param value - 待检查的值
 * @returns 是否为空
 */
const isEmpty = (value: CellValue): boolean => {
  if (Array.isArray(value)) {
    return value.length === 0;
  }
  
  return value === null || 
         value === undefined || 
         value === '';
};

/**
 * 处理验证错误消息
 * @description 统一的错误提示处理
 * @param errorMessage - 错误提示信息
 */
const handleValidationError = (errorMessage?: string): void => {
  if(errorMessage) {
    message.error({
      content: errorMessage,
      key: 'cell-validation-error',
      duration: 3,
    });
  }
};

/**
 * 单元格值验证
 * @description 统一处理必填和自定义验证逻辑
 * @param value - 待验证的值
 * @param validators - 验证规则配置
 * @param record - 当前行数据记录
 * @returns 验证是否通过
 */
export const validateCell = async (
  value: CellValue, 
  validators: ValidateOptions,
  record?: Record<string, any>
): Promise<boolean> => {
  try {
    // 必填验证
    if (validators.required?.value && isEmpty(value)) {
      const errorMessage = validators.required.message || '该字段不能为空';
      handleValidationError(errorMessage);
      return false;
    }

    // 自定义验证
    if (typeof validators.custom === 'function') {
      const result = await Promise.resolve(validators.custom(value, record));
      
      if (!result.valid) {
        handleValidationError(result.message);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('[InfinityTable] Cell validation error:', error);
    handleValidationError('验证过程发生错误');
    return false;
  }
}; 