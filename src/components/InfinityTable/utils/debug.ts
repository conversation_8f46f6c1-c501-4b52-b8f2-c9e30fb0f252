import { notification } from 'antd';
import { useTableContext } from '../core/context';
import type { NotificationPlacement } from 'antd/es/notification/interface';
import { useEffect, useRef } from 'react';

/**
 * 日志类型定义
 * @description 用于统一管理日志的类型
 */
type LogType = 'info' | 'warn' | 'error' | 'render';

/**
 * 日志配置接口
 * @description 定义日志的配置选项
 */
interface LogOptions {
  placement?: NotificationPlacement;
  duration?: number;
  closeIcon?: React.ReactNode;
}

/**
 * 默认日志配置
 * @description 统一的日志配置，方便后续维护和修改
 */
const DEFAULT_LOG_OPTIONS: LogOptions = {
  placement: 'topRight',
  duration: 3,
};

/**
 * 日志处理函数映射
 * @description 统一管理不同类型日志的控制台输出方法
 */
const LOG_FUNCTIONS: Record<LogType, typeof console.log> = {
  info: console.log,
  warn: console.warn,
  error: console.error,
  render: console.log,
};

/**
 * 通知处理函数映射
 * @description 统一管理不同类型日志的通知显示方法
 */
const NOTIFICATION_FUNCTIONS: Record<LogType, typeof notification.info> = {
  info: notification.info,
  warn: notification.warning,
  error: notification.error,
  render: notification.info,
};

/**
 * 格式化日志消息
 * @param content - 日志内容
 * @returns 格式化后的日志内容
 */
const formatLogMessage = (content: string): string => `[InfinityTable] ${content}`;

/**
 * 发送日志消息
 * @param type - 日志类型
 * @param title - 日志标题
 * @param content - 日志内容
 * @param options - 日志配置选项
 */
const logMessage = (
  type: LogType,
  title: string,
  content: string,
  options: LogOptions = DEFAULT_LOG_OPTIONS,
  showNotification = true,
): void => {
  LOG_FUNCTIONS[type](formatLogMessage(content));
  
  if (showNotification && type !== 'render') {
    NOTIFICATION_FUNCTIONS[type]({
      message: title,
      description: content,
      ...DEFAULT_LOG_OPTIONS,
      ...options,
    });
  }
};

/**
 * 组件渲染追踪钩子
 * @param componentName - 组件名称
 * @returns void
 */
export const useRenderTracker = (componentName: string) => {
  const renderCount = useRef(0);

  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && window.location.href.includes('debug')) {
      renderCount.current += 1;
      console.log(
        `%c[InfinityTable]%c ${componentName} %c渲染次数: %c${renderCount.current}`,
        'color: #1890ff; font-weight: bold',
        'color: #52c41a; font-weight: bold', 
        'color: #722ed1',
        'color: #f5222d; font-weight: bold'
      );
    }
  });
};

/**
 * Table调试钩子
 * @description 提供表格组件的调试功能
 * @returns 调试工具对象
 */
export const useTableDebug = () => {
  const { debug } = useTableContext();

  /**
   * 统一的日志处理函数
   * @param title - 日志标题
   * @param content - 日志内容
   * @param type - 日志类型
   * @param options - 日志配置选项
   */
  const log = (
    title: string,
    content: string,
    type: LogType = 'info',
    options?: LogOptions,
  ): void => {
    if (!debug?.enabled) return;
    logMessage(type, title, content, options);
  };

  return {
    /**
     * 信息日志
     * @param title - 日志标题
     * @param content - 日志内容
     * @param options - 日志配置选项
     */
    info: (title: string, content: string, options?: LogOptions) => 
      log(title, content, 'info', options),

    /**
     * 警告日志
     * @param title - 日志标题
     * @param content - 日志内容
     * @param options - 日志配置选项
     */
    warn: (title: string, content: string, options?: LogOptions) => 
      log(title, content, 'warn', options),

    /**
     * 错误日志
     * @param title - 日志标题
     * @param content - 日志内容
     * @param options - 日志配置选项
     */
    error: (title: string, content: string, options?: LogOptions) => 
      log(title, content, 'error', options),

    /**
     * 调试开关状态
     */
    enabled: debug?.enabled,
  };
}; 
