import React, { useMemo, useState, useCallback, useEffect } from "react";
import { Table, message as antdMessage } from "antd";
import type { ColumnType as AntdColumnType } from "antd/lib/table";
import { DisplayCell } from "@components/renderers/Cell";
import { useTableContext } from "./context";
import type { ColumnType, CellValue } from "@types";
import { TABLE_LAYOUT_CONFIG } from "@constants/index";
import { usePrefixCls } from "@utils/getPrefixCls";
import { Tooltip } from "../components/overlay/Tooltip";
import { useTooltip } from "../hooks/useTooltip";
import type { TableCoreProps } from "../types/table-core";
import { CellEditor } from './cellEditor';
import { useRenderTracker } from "../utils/debug";

/**
 * TableCore 组件 - 表格核心组件
 * 
 * @description
 * 核心功能包括:
 * 1. 表格基础渲染与布局
 * 2. 单元格编辑状态管理
 * 3. 行选择处理
 * 4. 单元格禁用和锁定状态控制
 * 5. 工具提示显示
 */
export const TableCore = <T extends Record<string, unknown>>({
  columns,                // 列配置
  height,                // 表格高度
  width: propWidth,      // 表格宽度
  disableControl = {     // 禁用控制配置
    rules: [],
    tooltip: {
      show: true,
      message: "暂无编辑权限",
    },
  },
  editingLock = {        // 编辑锁定配置
    editingRows: [],
    tooltip: {
      show: true,
      message: "他人正在编辑",
    },
  },
  rowKey = "id",         // 行唯一标识
  rowSelection,          // 行选择配置
  onEditStateChange,     // 编辑状态变更回调
  ...restProps
}: TableCoreProps<T>) => {
  useRenderTracker('TableCore');
  
  // ================ Hooks & Context ================
  const prefixCls = usePrefixCls("table");
  const { 
    startEdit, 
    dataSource, 
    setInitialCellValue,
    editingCellsMap,
  } = useTableContext<T>();
  
  const { 
    tooltipState, 
    tooltipRef, 
    handleCellHover, 
    handleCellLeave,
    clearTooltip
  } = useTooltip();

  // ================ State ================
  // 选中单元格状态
  const [selectedCell, setSelectedCell] = useState<{
    rowKey: string;
    dataIndex: string;
  } | null>(null);

  // ================ Utility Functions ================
  /**
   * 获取行的唯一键值
   * @param record 行数据记录
   * @returns 行的唯一标识符
   */
  const getRowKey = (record: T): string => {
    const key = typeof rowKey === "function" ? rowKey(record) : record[rowKey];
    return String(key);
  };

  /**
   * 判断行是否被锁定
   * @param rowKey 行标识
   */
  const checkRowEditingLockStatus = useCallback((rowKey: string): boolean => {
    return editingLock?.editingRows?.some(row => row.id === rowKey) || false;
  }, [editingLock?.editingRows]);

  /**
   * 获取行的锁定提示消息
   * @param rowKey 行标识
   */
  const getRowEditingLockMessage = useCallback((rowKey: string): string => {
    const lockedRow = editingLock?.editingRows?.find(row => row.id === rowKey);
    return lockedRow?.message || editingLock?.tooltip?.message || '';
  }, [editingLock]);

  /**
   * 判断单元格是否禁用
   * @param record 行数据
   * @param column 列配置
   */
  const isDisabledCell = useCallback(
    (record: T, column: ColumnType<T>) => {
      // 检查全局禁用规则
      if (disableControl?.rules) {
        const isGlobalDisabled = disableControl.rules.some((rule) => {
          if (typeof rule === "string") return rule === column.dataIndex;
          if (typeof rule === "object" && "field" in rule && "match" in rule) {
            return rule.field === column.dataIndex && rule.match(record);
          }
          return false;
        });
        if (isGlobalDisabled) return true;
      }

      // 检查列级别禁用配置
      if (column.disabled !== undefined) {
        return typeof column.disabled === "function"
          ? column.disabled(record)
          : column.disabled;
      }

      return false;
    },
    [disableControl?.rules]
  );

  

  // ================ Event Handlers ================
  /**
   * 处理单元格点击事件
   * @param record 行数据
   * @param column 列配置
   */
  const handleCellClick = (record: T, column: ColumnType<T>) => {
    if (disableControl.rules?.includes(column.dataIndex)) return;
    const currentRowKey = getRowKey(record);
    if (checkRowEditingLockStatus(currentRowKey)) return;

    setSelectedCell({
      rowKey: currentRowKey,
      dataIndex: column.dataIndex,
    });
  };

  /**
   * 处理单元格双击事件
   * @param e 鼠标事件
   * @param record 行数据
   * @param column 列配置
   */
  const handleCellDoubleClick = async (
    e: React.MouseEvent<HTMLElement>,
    record: T,
    column: ColumnType<T>
  ) => {
    //console.log('双击事件触发', { record, column });

    await new Promise(resolve => setTimeout(resolve, 45));

    // 防止频繁操作
    if (editingCellsMap.size > 0) {
      antdMessage.info({
        content: "操作过于频繁，请稍后再试",
        key: 'editing-in-progress',
        duration: 2
      });
      return;
    }

    // 权限检查
    if (isDisabledCell(record, column)) return;
    const currentRowKey = getRowKey(record);
    if (checkRowEditingLockStatus(currentRowKey)) return;
    if (!column.editor) return;

    // 编辑前检查
    if (column.editor?.beforeEdit) {
      try {
        const canEdit = await column.editor.beforeEdit(record);
        if (!canEdit) return;
      } catch (err) {
        console.error("BeforeEdit check failed:", err);
        return;
      }
    }

    // 获取单元格DOM元素
    const findTableCell = (element: HTMLElement | null): HTMLElement | null => {
      if (!element) return null;
      if (element.classList.contains("infinity-table-cell")) {
        return element;
      }
      return findTableCell(element.parentElement);
    };

    // 计算编辑器位置
    const target = e.target as HTMLElement;
    const cell = e.currentTarget || findTableCell(target);
    const rect = cell.getBoundingClientRect();
    const tableBody = cell.closest(`.${prefixCls}-body`);
    if (!tableBody) return;
    const tableWrapper = cell.closest(".infinity-table-wrapper");
    if (!tableWrapper) return;
    const wrapperRect = tableWrapper.getBoundingClientRect();
    const position = {
      top: rect.top - wrapperRect.top + (tableBody.scrollTop || 0),
      left: rect.left - wrapperRect.left + (tableBody.scrollLeft || 0),
      width: rect.width,
      height: rect.height,
    };

    // 启动编辑模式
    const value = record[column.dataIndex] as CellValue;
    setInitialCellValue(value);
    startEdit({
      from: 'handleCellDoubleClick',
      cell: {
        rowIndex: dataSource.indexOf(record),
        rowKey: getRowKey(record),
        dataIndex: column.dataIndex,
        value: value,
        record,
        column,
        editor: column.editor,
        position,
        cellTarget: cell,
      }
    });
  };

  /**
   * 处理复制功能
   * @param e 键盘事件
   */
  const handleCopy = useCallback((e: KeyboardEvent) => {
    if ((e.metaKey || e.ctrlKey) && e.key === 'c') {
      // 直接通过选中单元格的类名获取元素
      const cell = document.querySelector('.infinity-table-cell-selected') as HTMLElement;
      
      if (cell) {
        // 检查是否为图片类型单元格
        const hasImageDisplay = cell.querySelector('.infinity-table-images-display');
        if (hasImageDisplay) {
          // 如果是图片类型，则不执行复制
          return;
        }

        // 尝试多种方式获取文本内容
        let textToCopy = '';
        
        // 1. 首先尝试获取.infinity-table-display-cell的内容
        const displayCell = cell.querySelector('.infinity-table-display-cell');
        if (displayCell) {
          textToCopy = displayCell.textContent || '';
        }
        
        // 2. 如果没有找到.infinity-table-display-cell，尝试获取单元格的直接文本内容
        if (!textToCopy) {
          textToCopy = cell.textContent || '';
        }
        
        // 3. 清理文本内容（去除多余空格）
        textToCopy = textToCopy.trim();

        // 复制到剪贴板
        if (textToCopy) {
          navigator.clipboard.writeText(textToCopy).then(() => {
            antdMessage.success({
              content: '已复制到剪贴板',
              key: 'copy-success',
              duration: 1
            });
          }).catch(() => {
            antdMessage.error({
              content: '复制失败',
              key: 'copy-error',
              duration: 1
            });
          });
        }
      }
    }
  }, []); // 不再需要依赖 selectedCell

  // 添加键盘事件监听
  useEffect(() => {
    const handler = (e: KeyboardEvent) => handleCopy(e);
    document.addEventListener('keydown', handler);
    return () => {
      document.removeEventListener('keydown', handler);
    };
  }, [handleCopy]);

  // ================ Render Functions ================
  /**
   * 渲染单元格内容
   * @param value 单元格值
   * @param record 行数据
   * @param column 列配置
   */
  const renderCell = (value: unknown, record: T, column: ColumnType<T>) => {
    const isLocked = checkRowEditingLockStatus(getRowKey(record));
    const isDisabled = isDisabledCell(record, column);

    const content = (
      <DisplayCell
        mode="display"
        type={column.editor?.type || "text"}
        value={value as CellValue}
        displayRender={column.editor?.displayRender}
        {...column.editor?.props}
      />
    );

    return isLocked || isDisabled ? (
      <div
        className={`infinity-table-display-cell ${
          isDisabled ? "disabled-cell-content" : ""
        } ${isLocked ? "locked-cell-content" : ""}`}
      >
        {content}
      </div>
    ) : (
      content
    );
  };

  // ================ Column Configuration ================
  /**
   * 处理列配置，增强列的交互能力
   * 
   * 业务流程:
   * 1. 基础展示
   *    - 渲染单元格内容
   *    - 应用列样式和宽度
   * 
   * 2. 交互控制
   *    - 双击进入编辑模式
   *    - 单击选中单元格
   *    - 处理单元格的hover效果
   * 
   * 3. 权限控制
   *    - 检查单元格是否可编辑
   *    - 处理被锁定的行
   *    - 显示禁用/锁定状态的样式
   * 
   * 4. 提示信息
   *    - 权限不足时显示tooltip
   *    - 行被锁定时显示提示
   */
  const processedColumns = useMemo(() => {
    return columns.map((column) => {
      const baseColumn: AntdColumnType<T> = {
        ...column,
        onCell: (record: T) => {
          const rowKey = getRowKey(record);
          const isLocked = checkRowEditingLockStatus(rowKey);
          const isDisabled = isDisabledCell(record, column);
          const isGlobalDisabled = disableControl?.rules?.some((rule) => {
            if (typeof rule === "string") return rule === column.dataIndex;
            if (typeof rule === "object" && "field" in rule && "match" in rule) {
              return rule.field === column.dataIndex && rule.match(record);
            }
            return false;
          });
          
          // 判断单元格是否被选中
          const isSelected = selectedCell?.rowKey === rowKey && 
                           selectedCell?.dataIndex === column.dataIndex;
          // 判断是否为固定列                 
          const isFixedColumn = column.fixed === "left" || column.fixed === "right";

          return {
            // 设置单元格类名和data属性
            className: `infinity-table-cell 
              ${!isFixedColumn && isSelected ? "infinity-table-cell-selected" : ""} 
              ${isDisabled ? "disabled-cell-content" : ""} 
              ${isLocked ? "locked-cell-content" : ""}`,
            // 双击编辑处理
            onDoubleClick: isDisabled || isLocked
              ? undefined
              : (e) => handleCellDoubleClick(e, record, column),
            
            // 单击选中处理    
            onClick: isDisabled || isLocked
              ? undefined
              : () => handleCellClick(record, column),
            
            // 悬停提示处理    
            onMouseEnter: (e: React.MouseEvent) => handleCellHover(
              e, 
              isGlobalDisabled,
              (isLocked && getRowEditingLockMessage(rowKey)) || 
              (isGlobalDisabled && disableControl?.tooltip?.message)
            ),
            onMouseLeave: handleCellLeave,
          };
        },
        
        // 单元格渲染逻辑
        render: (value: unknown, record: T) => {
          return column.render ? (
            // 使用自定义渲染
            <div
              className={`infinity-table-display-cell ${column.className || ""} 
                ${isDisabledCell(record, column) ? "disabled-cell-content" : ""} 
                ${checkRowEditingLockStatus(getRowKey(record))
                  ? "locked-cell-content"
                  : ""}`}
              style={{
                height: `${TABLE_LAYOUT_CONFIG.CELL_HEIGHT}px`,
                overflow: "hidden",
                padding: "12px 12px",
                width: "100%",
              }}
            >
              {column.render(value, record, dataSource.indexOf(record))}
            </div>
          ) : (
            // 使用默认单元格渲染
            renderCell(value, record, column)
          );
        },
      };
      return baseColumn;
    });
  }, [
    columns,
    selectedCell,
    disableControl.rules,
    checkRowEditingLockStatus,
    isDisabledCell,
    handleCellHover,
    handleCellLeave,
    //handleCellDoubleClick,
    //handleCellClick,
    dataSource,
    getRowKey,
  ]);

  // ================ Effects ================
  // 监听编辑锁定状态变化
  useEffect(() => {
    clearTooltip();
  }, [editingLock?.editingRows, clearTooltip]);

  // ================ Render ================
  return (
    <div
      className={`infinity-table-wrapper`}
      style={{ height, width: propWidth }}
    >
      <Table<T>
        {...restProps}
        columns={processedColumns}
        dataSource={dataSource}
        rowKey={rowKey}
        pagination={false}
        className={`infinity-table-container infinity-table`}
        sticky
        prefixCls={""}
        rowSelection={rowSelection}
      />
      <CellEditor onEditStateChange={onEditStateChange} />
      <Tooltip
        tooltipState={tooltipState}
        disableControl={disableControl}
        editingLock={editingLock}
        tooltipRef={tooltipRef}
      />
    </div>
  );
};