import React, { useEffect, useState, useRef, useMemo, memo } from "react";
import { message as antdMessage } from "antd";
import { DisplayCell } from "@components/renderers/Cell";
import { EditorCell } from "@components/renderers/EditableCell";
import { EditorPopup } from "@components/overlay/EditorPopup";
import { useTableContext } from "./context";
import type { CellValue } from "@types";
import { useTableDebug } from "@utils/debug";
import { validateCell } from "../utils/validator";
import isEqual from "lodash/isEqual";
import type { EditorPopupWrapperProps } from "../types/table-core";

/**
 * CellEditor 组件 - 单元格编辑器
 * 
 * 业务功能:
 * 1. 编辑状态管理
 *    - 管理单元格的编辑状态
 *    - 处理值的变更和临时存储
 *    - 提供乐观更新支持
 * 
 * 2. 数据验证与保存
 *    - 执行字段验证规则
 *    - 处理保存和取消操作
 *    - 支持自定义保存逻辑
 * 
 * 3. 界面交互
 *    - 渲染编辑器弹出层
 *    - 支持自定义编辑器
 *    - 处理编辑器的显示和隐藏
 * 
 * 4. 状态同步
 *    - 同步编辑状态到上层组件
 *    - 处理编辑状态变更回调
 */
export const CellEditor = memo(({ onEditStateChange }: EditorPopupWrapperProps) => {
  // ================ Hooks & Context ================
  const { 
    startEdit,
    setInitialCellValue,
    editingCellsMap,
    updateTrigger,
    generateCellKey
  } = useTableContext();
  
  const debug = useTableDebug();

  // ================ State ================
  const [currentValue, setCurrentValue] = useState<CellValue>("");
  const valueRef = useRef<CellValue>(currentValue);
  const editorRef = useRef<{ clear: () => void }>(null);
  
  // 乐观更新的临时值状态
  const [optimisticValue, setOptimisticValue] = useState<CellValue | null>(null);

  // ================ Computed Values ================
  // 获取当前编辑的单元格
  const editingCell = useMemo(() => {
    if (!editingCellsMap?.size) return null;
    return Array.from(editingCellsMap.values())[0];
  }, [editingCellsMap, updateTrigger]);

  // ================ Effects ================
  /**
   * 监听编辑单元格变化
   * 当开始编辑新单元格时，初始化相关状态
   */
  useEffect(() => {
    if (editingCell) {
      setCurrentValue(editingCell.value);
      valueRef.current = editingCell.value;
      setInitialCellValue(editingCell.value);
    }
  }, [editingCell, setInitialCellValue]);

  // 如果没有正在编辑的单元格，不渲染任何内容
  if (!editingCell) return null;

  // ================ Event Handlers ================
  /**
   * 处理值变更
   * 更新内部状态并同步到编辑上下文
   * @param newValue 新的单元格值
   */
  const handleValueChange = (newValue: CellValue) => {
    setCurrentValue(newValue);
    valueRef.current = newValue;

    const cellKey = generateCellKey(editingCell.rowIndex, editingCell.column.key);
    const currentEditingCell = editingCellsMap.get(cellKey);

    startEdit({
      from: 'handleValueChange',
      cell: editingCell ? {
        ...editingCell,
        value: newValue,
        initialValue: currentEditingCell?.initialValue ?? editingCell.value
      } : null
    });
  };

  /**
   * 验证单元格值
   * @param value 要验证的值
   * @param record 当前行数据
   * @returns 验证结果
   */
  const validateValue = async (value: CellValue, record: any): Promise<boolean> => {
    const validators = editingCell.column.editor?.validators;
    if (!validators) return true;

    return validateCell(value, {
      required: validators?.required ?? false,
      custom: validators?.custom
        ? (value) => validators.custom?.(value, record) ?? true
        : undefined,
    });
  };

  /**
   * 处理保存操作
   * 执行验证、乐观更新和保存流程
   */
  const handleSave = async (customData?: any) => {
    const latestValue = valueRef.current;
    const cellKey = generateCellKey(editingCell.rowIndex, editingCell.column.key);
    const currentEditingCell = editingCellsMap.get(cellKey);
    
    try {
      if (!currentEditingCell) {
        console.warn('找不到对应的编辑单元格:', cellKey);
        return;
      }

      // 检查值是否变更
      if (isEqual(latestValue, currentEditingCell.initialValue)) {
        if (onEditStateChange) {
          await onEditStateChange({
            value: latestValue,
            record: currentEditingCell.record,
            column: currentEditingCell.column,
            type: "unchanged",
          });
        }
        debug.info("值未变更，退出编辑", "");
        startEdit({
          from: 'handleSave_unchanged',
          cell: null,
          currentCell: {
            rowIndex: editingCell.rowIndex,
            columnKey: editingCell.column.key
          }
        });
        return;
      }

      // 执行验证
      if (currentEditingCell.column.editor?.validators) {
        const isValid = await validateValue(latestValue, currentEditingCell.record);
        if (!isValid) {
          if (onEditStateChange) {
            await onEditStateChange({
              value: latestValue,
              record: currentEditingCell.record,
              column: currentEditingCell.column,
              type: "invalid",
            });
          }
          startEdit({
            from: 'handleSave_invalid',
            cell: null,
            currentCell: {
              rowIndex: editingCell.rowIndex,
              columnKey: editingCell.column.key
            }
          });
          return;
        }
      }

      // 乐观更新处理
      setOptimisticValue(latestValue);

      try {
        // 执行保存
        if (currentEditingCell.column.editor?.onEditComplete) {
          await currentEditingCell.column.editor.onEditComplete(
            latestValue,
            currentEditingCell.record,
            customData
          );
          
          if (onEditStateChange) {
            await onEditStateChange({
              value: latestValue,
              record: currentEditingCell.record,
              column: currentEditingCell.column,
              type: "success",
            });
          }
          debug.info("单元格保存", "编辑器");
        }

        // 保存成功，清理状态
        setOptimisticValue(null);
        startEdit({
          from: 'handleSave_success',
          cell: null,
          currentCell: {
            rowIndex: editingCell.rowIndex,
            columnKey: editingCell.column.key
          }
        });
      } catch (err) {
        // 保存失败，回滚显示
        setOptimisticValue(null);
        throw err;
      }

    } catch (err) {
      console.error("Save failed:", err);
      antdMessage.error(err instanceof Error ? err.message : "保存失败");
    }
  };

  /**
   * 处理编辑器关闭
   * @param reason 关闭原因: save | cancel | blur
   * @param customData 自定义数据
   */
  const handleClose = (reason: "save" | "cancel" | "blur", customData?: any) => {
    if (reason === "blur" && editingCell?.editor?.saveOnBlur === false) {
      startEdit({
        from: 'handleClose_blur',
        cell: null,
        currentCell: {
          rowIndex: editingCell.rowIndex,
          columnKey: editingCell.column.key
        }
      });
      
      if (onEditStateChange) {
        onEditStateChange({
          value: editingCell.value,
          record: editingCell.record,
          column: editingCell.column,
          type: "unchanged",
        })
      }
      return;
    }

    if (reason === "cancel") {
      startEdit({
        from: 'handleClose_cancel',
        cell: null,
        currentCell: {
          rowIndex: editingCell.rowIndex,
          columnKey: editingCell.column.key
        }
      });
      
      if (onEditStateChange) {
        onEditStateChange({
          value: editingCell.value,
          record: editingCell.record,
          column: editingCell.column,
          type: "cancelled",
        })
      }
      return;
    }

    handleSave(customData)
  };

  // ================ Render Functions ================
  interface SaveParams {
    value?: CellValue;
    customData?: unknown;
  }

  /**
   * 渲染编辑器内容
   * 支持自定义编辑器和默认编辑器
   */
  const renderEditor = () => {
    const displayValue = optimisticValue ?? currentValue;
    const CustomEditor = editingCell.editor?.editorRender;
    
    if (CustomEditor) {
      return (
        <CustomEditor
          mode="edit"
          value={displayValue}
          onChange={handleValueChange}
          ref={editorRef}
          record={editingCell.record}
          onSave={(params?: SaveParams) => {
            handleValueChange(params?.value ?? displayValue);
            handleClose("save", params?.customData);
          }}
          onCancel={() => handleClose("cancel")}
          {...(editingCell.editor?.props || {})}
        />
      );
    }

    return (
      <EditorCell
        mode="edit"
        type={editingCell.editor?.type || "text"}
        value={displayValue}
        record={editingCell.record}
        onCellChange={handleValueChange}
        onSave={() => handleClose("save")}
        onCancel={() => handleClose("cancel")}
        {...(editingCell.editor?.props || {})}
      />
    );
  };

  /**
   * 渲染编辑器悬浮内容
   * 用于显示预览和辅助信息
   */
  const renderEditingOverlay = () => {
    if (!editingCell) return null;

    return (
      <DisplayCell
        {...editingCell.editor?.props}
        type={editingCell.editor?.type || "text"}
        value={currentValue}
        editor={editingCell.editor}
        displayRender={editingCell.editor?.displayRender}
        onClear={() => {
          if (editingCell.editor?.overlayProps?.onClear) {
            editingCell.editor.overlayProps.onClear({
              record: editingCell.record,
              editor: editorRef,
              onChange: (value: CellValue) => handleValueChange(value),
            });
          } else {
            handleValueChange(null);
          }
        }}
      />
    );
  };

  // ================ Component Render ================
  return (
    <EditorPopup
      position={editingCell?.position}
      cellTarget={editingCell?.cellTarget}
      type={editingCell.editor?.type}
      placement={
        editingCell?.editor?.type === "text" ||
        editingCell?.editor?.type === "number"
          ? "overlay"
          : "below"
      }
      visible={Boolean(editingCell)}
      onClose={handleClose}
      popupProps={editingCell.editor?.popupProps || {}}
      overlayContent={renderEditingOverlay()}
    >
      {renderEditor()}
    </EditorPopup>
  );
});

CellEditor.displayName = 'CellEditor'; 