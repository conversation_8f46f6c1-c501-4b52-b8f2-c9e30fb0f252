/* eslint-disable react-refresh/only-export-components */

/**
 * TableContext - 表格状态管理上下文
 * 
 * 核心功能:
 * 1. 管理表格编辑状态 - 跟踪哪些单元格正在编辑
 * 2. 提供数据源访问 - 统一管理表格数据
 * 3. 处理单元格选中/编辑 - 控制单元格的编辑生命周期
 * 4. 统一管理调试配置 - 开发环境下的调试工具
 */
import React, { createContext, useContext, useState, useRef, useMemo, useCallback } from 'react';
import type { CellValue } from '../types';
import type { 
  EditingCell, 
  TableContextValue, 
  TableContextProviderProps,
  StartEditOptions
} from '../types/context';
import { useRenderTracker } from "../utils/debug";


// 创建泛型上下文
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const TableContext = createContext<TableContextValue<any> | null>(null);
/**
 * TableContextProvider - 表格上下文提供者组件
 * 
 * @param children - 子组件
 * @param prefixCls - 样式前缀
 * @param dataSource - 表格数据源
 * @param debug - 调试配置
 */
export function TableContextProvider<T extends Record<string, unknown>>({
  children,
  prefixCls,
  dataSource,
  debug
}: TableContextProviderProps<T>) {
  // 使用 Map 存储编辑状态，key 为单元格唯一标识，value 为编辑状态
  // 使用 useRef 确保在重渲染时保持引用不变
  const editingCellsRef = useRef<Map<string, EditingCell<T>>>(new Map());
  
  // updateTrigger 用于强制组件重新渲染
  // 当编辑状态改变时，增加计数器触发更新
  const [updateTrigger, setUpdateTrigger] = useState(0);
  
  // 存储单元格开始编辑时的初始值，用于取消编辑时恢复
  const [initialCellValue, setInitialCellValue] = useState<CellValue | null>(null);

  /**
   * 生成单元格的唯一标识
   * 格式: `${rowIndex}-${columnKey}`
   * 示例: 
   * - 第0行name列: "0-name"
   * - 第1行age列: "1-age"
   */
  const generateCellKey = useCallback((rowIndex: number, columnKey: string) => {
    return `${rowIndex}-${columnKey}`;
  }, []);

  /**
   * 开始/结束编辑的核心处理函数
   * 
   * @param from - 触发来源，用于区分不同的编辑场景
   * @param cell - 要编辑的单元格信息
   * @param currentCell - 当前正在编辑的单元格
   * 
   * 数据流转示例:
   * 1. 开始编辑:
   *    cell = { rowIndex: 0, column: { key: 'name' }, value: '张三' }
   *    → Map { "0-name" => { initialValue: '张三', value: '张三' } }
   * 
   * 2. 修改值:
   *    Map { "0-name" => { initialValue: '张三', value: '李四' } }
   * 
   * 3. 取消编辑:
   *    → Map { "0-name" => { initialValue: '张三', value: '张三' } }
   *    → Map 删除该条目
   * 
   * 4. 确认编辑:
   *    → Map 直接删除该条目，新值通过其他逻辑保存
   */
  const startEdit = useCallback(({ from, cell, currentCell }: StartEditOptions) => {
    if (cell === null && currentCell) {
      // 结束当前单元格的编辑
      const cellKey = generateCellKey(currentCell.rowIndex, currentCell.columnKey);
      
      // 取消编辑时恢复初始值
      if (from === 'handleClose_cancel') {
        const editingCell = editingCellsRef.current.get(cellKey);
        if (editingCell) {
          editingCell.value = editingCell.initialValue;
        }
      }
      
      editingCellsRef.current.delete(cellKey);
    } else if (cell === null) {
      // 清除所有编辑状态（通常用于表格重置）
      editingCellsRef.current.clear();
    } else {
      // 开始新单元格的编辑
      const cellKey = generateCellKey(cell.rowIndex, cell.column.key);
      const existingCell = editingCellsRef.current.get(cellKey);
      
      // 保存初始值，用于后续可能的取消操作
      editingCellsRef.current.set(cellKey, {
        ...cell,
        initialValue: existingCell ? existingCell.initialValue : cell.value
      });
    }
    
    // 触发重渲染，确保UI更新
    setUpdateTrigger(prev => prev + 1);
  }, [generateCellKey]);

  /**
   * 检查指定单元格是否处于编辑状态
   * 
   * 示例:
   * Map包含: { "0-name" => {...} }
   * isEditing(0, "name") → true
   * isEditing(0, "age") → false
   */
  const isEditing = useCallback((rowIndex: number, columnKey: string) => {
    const cellKey = generateCellKey(rowIndex, columnKey);
    return editingCellsRef.current.has(cellKey);
  }, [generateCellKey]);

  /**
   * 获取指定单元格的编辑状态
   * 返回完整的编辑信息或null
   */
  const getEditingCell = useCallback((rowIndex: number, columnKey: string) => {
    const cellKey = generateCellKey(rowIndex, columnKey);
    return editingCellsRef.current.get(cellKey) || null;
  }, [generateCellKey]);

  // 通过 useMemo 缓存上下文值，避免不必要的重渲染
  const contextValue = useMemo<TableContextValue<T>>(() => ({
    dataSource,
    editingCellsMap: editingCellsRef.current,
    isEditing,
    getEditingCell,
    initialCellValue,
    setInitialCellValue,
    debug,
    prefixCls,
    startEdit,
    updateTrigger,
    generateCellKey,
  }), [
    dataSource,
    updateTrigger,
    initialCellValue,
    debug,
    prefixCls,
  ]);

  // 开发环境下跟踪组件渲染
  useRenderTracker('TableContextProvider');

  return (
    <TableContext.Provider value={contextValue}>
      {children}
    </TableContext.Provider>
  );
}

/**
 * useTableContext - 获取表格上下文的自定义Hook
 * 
 * 使用示例:
 * ```tsx
 * const { isEditing, startEdit } = useTableContext<UserData>();
 * ```
 */
export function useTableContext<T extends Record<string, unknown>>(): TableContextValue<T> {
  const context = useContext(TableContext);
  if (!context) {
    throw new Error('useTableContext必须在TableContextProvider内部使用');
  }
  return context as TableContextValue<T>;
}

/**
 * editingCellsRef 数据结构示例:
 * 
 * 假设正在编辑第0行的name列和第1行的age列:
 * 
 * editingCellsRef.current = Map {
 *   "0-name" => {
 *     rowIndex: 0,
 *     column: { key: "name", title: "姓名" },
 *     record: { id: 1, name: "张三", age: 25 },
 *     value: "李四",        // 当前编辑值
 *     initialValue: "张三"  // 开始编辑时的初始值
 *   },
 *   "1-age" => {
 *     rowIndex: 1,
 *     column: { key: "age", title: "年龄" },
 *     record: { id: 2, name: "王五", age: 30 },
 *     value: 31,           // 当前编辑值
 *     initialValue: 30     // 开始编辑时的初始值
 *   }
 * }
 * 
 * 数据流转过程:
 * 1. 开始编辑 → 保存初始值
 * 2. 编辑过程 → 只更新value
 * 3. 取消编辑 → value恢复为initialValue，然后删除Map条目
 * 4. 确认编辑 → 直接删除Map条目，新值由外部逻辑处理
 */