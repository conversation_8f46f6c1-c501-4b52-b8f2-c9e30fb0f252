/**
 * InfinityTable - 高性能虚拟滚动表格组件
 */
import React from 'react';
import type { InfinityTableProps } from './types';
import { TableCore } from './core/table';
import { TableContextProvider } from './core/context';
import { useColumns } from './hooks/useColumns';
import './styles/index.less';

export function InfinityTable<RecordType extends Record<string, unknown>>({
  dataSource,
  width,
  columns,
  rules = [],
  columnManager,
  onEditStateChange,
  prefixCls = 'ant',
  debug = { enabled: false },
  ...props
}: InfinityTableProps<RecordType>) {
  // 根据规则和列管理器配置处理和转换列
  const { finalColumns } = useColumns<RecordType>({
    columns,
    rules,
    columnManager
  });

  // 缓存表格上下文值以避免不必要的重渲染
  const contextValue = React.useMemo(() => ({
    dataSource,
    debug,
    prefixCls
  }), [dataSource, debug, prefixCls]);

  return (
    <div 
      className="infinity-table-root"
      data-testid="infinity-table"
    >
      <TableContextProvider {...contextValue}>
        <TableCore
          width={width}
          columns={finalColumns}
          dataSource={dataSource}
          onEditStateChange={onEditStateChange}
          {...props}
        />
      </TableContextProvider>
    </div>
  );
}

// 导出组件类型供外部使用
export type { InfinityTableProps } from './types';
