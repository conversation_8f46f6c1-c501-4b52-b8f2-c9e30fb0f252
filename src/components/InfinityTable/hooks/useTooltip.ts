import { useState, useCallback, useRef, useEffect } from 'react';
import { throttle } from 'lodash';
import type { TooltipState } from '../types';

const INITIAL_STATE: TooltipState = {
  visible: false,
  isPermissionTip: false,
  message: undefined,
  position: { x: 0, y: 0 }
};

export const useTooltip = () => {
  // 1. 使用 useRef 存储状态,避免重渲染
  const [tooltipState, setTooltipState] = useState<TooltipState>(INITIAL_STATE);
  const tooltipRef = useRef<HTMLDivElement>(null);
  
  // 2. 将 throttle 函数移到 useRef 外部
  const handleMouseMove = useCallback((e: MouseEvent) => {
    setTooltipState(prev => ({
      ...prev,
      position: {
        x: e.clientX,
        y: e.clientY - 10
      }
    }));
  }, []);

  // 3. 使用 useRef 存储 throttle 函数
  const throttledMouseMove = useRef(throttle(handleMouseMove, 16));

  // 4. 优化 handleCellHover,减少依赖
  const handleCellHover = useCallback((
    event: React.MouseEvent,
    isPermissionTip: boolean,
    message?: string
  ) => {
    event.persist();
    
    if (!message && !isPermissionTip) {
      return;
    }
    
    setTooltipState({
      visible: true,
      isPermissionTip,
      message,
      position: {
        x: event.clientX,
        y: event.clientY - 10
      }
    });

    document.addEventListener('mousemove', throttledMouseMove.current);
  }, []);

  // 5. 抽取重置状态逻辑
  const resetTooltipState = useCallback(() => {
    setTooltipState(INITIAL_STATE);
    document.removeEventListener('mousemove', throttledMouseMove.current);
  }, []);

  // 6. 简化 handleCellLeave
  const handleCellLeave = useCallback(() => {
    // 无论是权限提示还是锁定提示，鼠标离开时都应该清除
    resetTooltipState();
  }, [resetTooltipState]);

  // 7. 清理函数优化
  useEffect(() => {
    const currentThrottled = throttledMouseMove.current;
    
    return () => {
      currentThrottled.cancel();
      document.removeEventListener('mousemove', currentThrottled);
    };
  }, []);

  // 8. 导出 clearTooltip 方法
  const clearTooltip = useCallback(() => {
    // 如果当前显示的是权限提示(disableControl)，则不清除
    if (tooltipState.isPermissionTip) {
      return;
    }
    // 其他情况使用 resetTooltipState
    resetTooltipState();
  }, [resetTooltipState, tooltipState.isPermissionTip]);

  return {
    tooltipState,
    tooltipRef,
    handleCellHover,
    handleCellLeave,
    clearTooltip
  };
};