import React from 'react';
import { useMemo } from 'react';
import type { ColumnType as AntdColumnType } from 'antd/es/table';
import { ColumnManager } from '../features/ColumnManager';

interface UseColumnsProps<T extends Record<string, unknown>> {
  columns: AntdColumnType<T>[];
  rules: string[];
  columnManager?: {
    onChange: (columnKeys: string[]) => void;
    defaultColumns?: AntdColumnType<T>[];
  };
}

// 扩展 antd 的列类型，添加我们需要的属性
interface ExtendedColumnType<T> extends AntdColumnType<T> {
  show?: boolean;
  className?: string;
}

// 内部组件，不需要导出
const ManagerCell = <T extends Record<string, unknown>>({
  columns,
  onChange,
  title,
  defaultColumns
}: {
  columns: AntdColumnType<T>[];
  onChange: (columnKeys: string[]) => void;
  title?: React.ReactNode;
  defaultColumns?: AntdColumnType<T>[];
}) => (
  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
    {title && <span>{title}</span>}
    <ColumnManager 
      columns={columns}
      onChange={onChange}
      defaultColumns={defaultColumns}
    />
  </div>
);

export function useColumns<T extends Record<string, unknown>>({ 
  columns, 
  rules, 
  columnManager 
}: UseColumnsProps<T>) {
  // 处理列配置，为禁用列添加className
  const processedColumns = useMemo(() => 
    columns.map(column => ({
      ...column,
      className: `${column.className || ''} ${
        rules.includes(column.dataIndex as string) ? 'disabled-column' : ''
      }`.trim()
    })) as ExtendedColumnType<T>[]
  , [columns, rules]);

  // 过滤出要显示的列
  const visibleColumns = useMemo(() => 
    processedColumns.filter(col => col.show !== false)
  , [processedColumns]);

  // 处理列管理
  const finalColumns = useMemo(() => {
    if (!columnManager) return visibleColumns;

    // 如果没有可见列，返回带有列管理器的空列
    if (visibleColumns.length === 0) {
      return [{
        key: 'column-manager',
        dataIndex: 'column-manager',
        title: (
          <ManagerCell 
            columns={columns} 
            onChange={columnManager.onChange}
            defaultColumns={columnManager.defaultColumns}
          />
        ),
        width: 100
      }];
    }

    // 添加列管理按钮到最后列
    const lastColumn = visibleColumns[visibleColumns.length - 1];
    const enhancedLastColumn = {
      ...lastColumn,
      title: (
        <ManagerCell 
          columns={columns}
          onChange={columnManager.onChange}
          defaultColumns={columnManager.defaultColumns}
          title={lastColumn.title}
        />
      )
    };

    return [
      ...visibleColumns.slice(0, -1),
      enhancedLastColumn
    ];
  }, [visibleColumns, columns, columnManager]);

  return { finalColumns };
} 